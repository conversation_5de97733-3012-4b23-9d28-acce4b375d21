"""
Enhanced Campaign Statistics Module
Inspired by CALLBOT2.1's comprehensive approach to call tracking and statistics
"""

import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

@dataclass
class CallStatistics:
    """Comprehensive call statistics structure"""
    total_calls: int = 0
    completed_calls: int = 0
    failed_calls: int = 0
    no_answer_calls: int = 0
    busy_calls: int = 0
    in_progress_calls: int = 0
    pending_calls: int = 0
    
    def to_dict(self):
        return asdict(self)

@dataclass
class CampaignMetrics:
    """Advanced campaign metrics"""
    total_duration_seconds: int = 0
    total_cost_usd: float = 0.0
    average_call_duration: float = 0.0
    average_cost_per_call: float = 0.0
    success_rate: float = 0.0
    answer_rate: float = 0.0
    
    def to_dict(self):
        return asdict(self)

class EnhancedCampaignAnalyzer:
    """Enhanced campaign analyzer inspired by CALLBOT2.1"""
    
    def __init__(self, data_dir: str):
        self.data_dir = Path(data_dir)
    
    def load_call_logs(self) -> List[Dict]:
        """Load call logs from storage"""
        call_logs_file = self.data_dir / "call_logs.json"
        try:
            if call_logs_file.exists():
                with open(call_logs_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Error loading call logs: {e}")
        return []
    
    def load_campaigns(self) -> Dict:
        """Load campaigns from storage"""
        campaigns_file = self.data_dir / "campaigns.json"
        try:
            if campaigns_file.exists():
                with open(campaigns_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Error loading campaigns: {e}")
        return {}
    
    def get_campaign_calls(self, campaign_id: str) -> List[Dict]:
        """Get all calls for a specific campaign"""
        call_logs = self.load_call_logs()
        return [call for call in call_logs if call.get('campaign_id') == campaign_id]
    
    def calculate_call_statistics(self, calls: List[Dict]) -> CallStatistics:
        """Calculate comprehensive call statistics like CALLBOT2.1"""
        stats = CallStatistics()
        
        if not calls:
            return stats
        
        # Group calls by phone number to get the latest status for each unique contact
        calls_by_number = {}
        for call in calls:
            phone = call.get('contact_phone', '')
            if phone:
                # Keep the most recent call for each number
                if phone not in calls_by_number or call.get('timestamp', '') > calls_by_number[phone].get('timestamp', ''):
                    calls_by_number[phone] = call
        
        # Count unique contacts by their latest call status
        unique_calls = list(calls_by_number.values())
        stats.total_calls = len(unique_calls)
        
        for call in unique_calls:
            status = call.get('status', '').lower()
            if status in ['completed', 'answered']:
                stats.completed_calls += 1
            elif status in ['failed', 'error']:
                stats.failed_calls += 1
            elif status in ['no_answer', 'no-answer', 'noanswer']:
                stats.no_answer_calls += 1
            elif status in ['busy']:
                stats.busy_calls += 1
            elif status in ['in_progress', 'ringing', 'calling']:
                stats.in_progress_calls += 1
            elif status in ['pending', 'initiated', 'queued']:
                stats.pending_calls += 1
        
        return stats
    
    def calculate_campaign_metrics(self, calls: List[Dict]) -> CampaignMetrics:
        """Calculate advanced campaign metrics"""
        metrics = CampaignMetrics()
        
        if not calls:
            return metrics
        
        # Calculate totals
        total_duration = 0
        total_cost = 0.0
        answered_calls = 0
        
        for call in calls:
            # Duration
            duration = call.get('call_duration', 0) or 0
            if isinstance(duration, (int, float)) and duration > 0:
                total_duration += duration
            
            # Cost estimation (if not provided, estimate based on duration)
            cost = call.get('cost', 0) or 0
            if not cost and duration > 0:
                # Estimate cost: ~$0.013 per minute for Telnyx
                cost = (duration / 60) * 0.013
            total_cost += cost
            
            # Count answered calls for success rate
            status = call.get('status', '').lower()
            if status in ['completed', 'answered']:
                answered_calls += 1
        
        metrics.total_duration_seconds = total_duration
        metrics.total_cost_usd = round(total_cost, 4)
        
        if calls:
            metrics.average_call_duration = total_duration / len(calls)
            metrics.average_cost_per_call = total_cost / len(calls)
            metrics.success_rate = (answered_calls / len(calls)) * 100
            metrics.answer_rate = metrics.success_rate  # Same in this context
        
        return metrics
    
    def get_recyclable_contacts(self, campaign_id: str) -> List[Dict]:
        """Get contacts that can be recycled (failed, no-answer, busy)"""
        calls = self.get_campaign_calls(campaign_id)
        
        # Group by phone number and get latest status
        latest_calls = {}
        for call in calls:
            phone = call.get('contact_phone', '')
            if phone:
                if phone not in latest_calls or call.get('timestamp', '') > latest_calls[phone].get('timestamp', ''):
                    latest_calls[phone] = call
        
        # Filter for recyclable statuses
        recyclable = []
        for call in latest_calls.values():
            status = call.get('status', '').lower()
            if status in ['failed', 'no_answer', 'no-answer', 'noanswer', 'busy', 'error']:
                recyclable.append({
                    'contact_name': call.get('contact_name', 'Unknown'),
                    'contact_phone': call.get('contact_phone', ''),
                    'status': call.get('status', ''),
                    'last_attempt': call.get('timestamp', ''),
                    'notes': call.get('notes', '')
                })
        
        return recyclable
    
    def calculate_button_states(self, campaign_id: str, stats: CallStatistics) -> Dict:
        """Calculate campaign action button states like CALLBOT2.1"""
        campaigns = self.load_campaigns()
        campaign = campaigns.get(campaign_id, {})
        status = campaign.get('status', 'draft').lower()
        
        failed_count = stats.failed_calls + stats.no_answer_calls + stats.busy_calls
        
        return {
            'start': {
                'enabled': status in ['draft', 'pending', 'paused'],
                'tooltip': 
                    "Campaign is already running" if status == 'running' else
                    "Campaign is completed" if status == 'completed' else
                    "Start the campaign"
            },
            'pause': {
                'enabled': status == 'running',
                'tooltip':
                    "Campaign is already paused" if status == 'paused' else
                    "Campaign is completed" if status == 'completed' else
                    "Start the campaign first" if status in ['draft', 'pending'] else
                    "Pause the campaign"
            },
            'restart': {
                'enabled': status == 'paused',
                'tooltip':
                    "Campaign is already running" if status == 'running' else
                    "Campaign is completed" if status == 'completed' else
                    "Resume the paused campaign"
            },
            'recycle': {
                'enabled': failed_count > 0 and status in ['completed', 'paused'],
                'tooltip':
                    "No failed calls to recycle" if failed_count == 0 else
                    "Pause campaign before recycling" if status == 'running' else
                    f"Recycle {failed_count} failed/no-answer calls"
            }
        }
    
    def analyze_campaign_comprehensive(self, campaign_id: str) -> Dict:
        """Comprehensive campaign analysis inspired by CALLBOT2.1"""
        try:
            campaigns = self.load_campaigns()
            campaign = campaigns.get(campaign_id)
            
            if not campaign:
                return {'error': 'Campaign not found'}
            
            calls = self.get_campaign_calls(campaign_id)
            
            # Calculate all metrics
            call_stats = self.calculate_call_statistics(calls)
            metrics = self.calculate_campaign_metrics(calls)
            button_states = self.calculate_button_states(campaign_id, call_stats)
            recyclable_contacts = self.get_recyclable_contacts(campaign_id)
            
            # Calculate progress
            total_contacts = len(campaign.get('contacts', []))
            progress_percentage = (call_stats.total_calls / total_contacts * 100) if total_contacts > 0 else 0
            
            # Format recent calls for display
            recent_calls = sorted(calls, key=lambda x: x.get('timestamp', ''), reverse=True)[:20]
            formatted_calls = []
            
            for call in recent_calls:
                duration = call.get('call_duration', 0) or 0
                formatted_duration = None
                if duration > 0:
                    minutes = int(duration // 60)
                    seconds = int(duration % 60)
                    formatted_duration = f"{minutes}:{seconds:02d}"
                
                formatted_calls.append({
                    'id': call.get('id', ''),
                    'contact_name': call.get('contact_name', 'Unknown'),
                    'contact_phone': call.get('contact_phone', ''),
                    'from_phone': call.get('from_phone', ''),
                    'status': call.get('status', ''),
                    'timestamp': call.get('timestamp', ''),
                    'duration': formatted_duration,
                    'cost': f"${call.get('cost', 0):.4f}" if call.get('cost') else None,
                    'telnyx_call_id': call.get('telnyx_call_id', ''),
                    'notes': call.get('notes', '')
                })
            
            return {
                'success': True,
                'data': {
                    'campaign_info': {
                        'id': campaign_id,
                        'name': campaign.get('name', 'Unnamed Campaign'),
                        'status': campaign.get('status', 'draft'),
                        'created_at': campaign.get('created_at'),
                        'started_at': campaign.get('started_at'),
                        'completed_at': campaign.get('completed_at'),
                        'agent_name': campaign.get('agent_name'),
                    },
                    'call_statistics': call_stats.to_dict(),
                    'campaign_metrics': metrics.to_dict(),
                    'button_states': button_states,
                    'recyclable_contacts': recyclable_contacts,
                    'recent_calls': formatted_calls,
                    'progress': {
                        'total_contacts': total_contacts,
                        'contacted_contacts': call_stats.total_calls,
                        'successful_contacts': call_stats.completed_calls,
                        'remaining_contacts': max(0, total_contacts - call_stats.total_calls),
                        'progress_percentage': round(progress_percentage, 1)
                    },
                    'summary': {
                        'total_contacts': total_contacts,
                        'progress_percentage': round(progress_percentage, 1),
                        'success_rate': round(metrics.success_rate, 1),
                        'total_cost': f"${metrics.total_cost_usd:.4f}",
                        'total_duration_minutes': round(metrics.total_duration_seconds / 60, 1),
                        'calls_remaining': max(0, total_contacts - call_stats.total_calls)
                    }
                }
            }
            
        except Exception as e:
            logger.error(f"Error in comprehensive campaign analysis: {e}")
            return {'error': str(e)}

def get_enhanced_campaign_statistics(campaign_id: str, data_dir: str) -> Dict:
    """Main function to get enhanced campaign statistics"""
    analyzer = EnhancedCampaignAnalyzer(data_dir)
    return analyzer.analyze_campaign_comprehensive(campaign_id) 