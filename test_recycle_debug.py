#!/usr/bin/env python3
"""
Test script to debug campaign recycling issue
"""

import requests
import json
import sys

def test_campaign_debug(campaign_id):
    """Test the campaign debug endpoint"""
    
    base_url = "http://localhost:9090"
    
    print(f"🔍 Testing campaign debug for ID: {campaign_id}")
    print("=" * 50)
    
    try:
        # Test the debug endpoint
        debug_url = f"{base_url}/api/campaigns/{campaign_id}/recycle/debug"
        print(f"📡 Calling: {debug_url}")
        
        response = requests.get(debug_url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("\n✅ Debug data received:")
            print(json.dumps(data, indent=2))
            
            # Analyze the data
            print("\n📊 ANALYSIS:")
            print(f"Campaign: {data.get('campaign_name', 'Unknown')}")
            print(f"Total contacts: {data.get('total_contacts', 0)}")
            print(f"Total calls made: {data.get('total_calls_made', 0)}")
            print(f"Unique numbers called: {data.get('unique_numbers_called', 0)}")
            print(f"Recyclable contacts: {data.get('recyclable_contacts', 0)}")
            
            # Show analysis section
            analysis = data.get('analysis', {})
            print(f"\n🔍 STATUS ANALYSIS:")
            print(f"Recyclable statuses: {analysis.get('recyclable_statuses', [])}")
            print(f"Statuses found in logs: {analysis.get('statuses_found_in_logs', [])}")
            print(f"Status breakdown: {analysis.get('status_breakdown', {})}")
            
            # Show contact analysis
            contact_analysis = data.get('contact_analysis', [])
            print(f"\n👥 CONTACT ANALYSIS:")
            for contact in contact_analysis:
                recycle_icon = "✅" if contact.get('would_recycle') else "❌"
                print(f"{recycle_icon} {contact.get('name')} ({contact.get('phone')})")
                print(f"   Status: {contact.get('call_status')} | Would recycle: {contact.get('would_recycle')} | Reason: {contact.get('reason')}")
            
            # Show phone number matching
            phone_matching = data.get('phone_number_matching', {})
            print(f"\n📞 PHONE NUMBER MATCHING:")
            print(f"Contact phones: {phone_matching.get('contact_phones', [])}")
            print(f"Call log phones: {phone_matching.get('call_log_phones', [])}")
            print(f"Matched phones: {phone_matching.get('matched_phones', [])}")
            print(f"Unmatched contacts: {phone_matching.get('unmatched_contacts', [])}")
            
        else:
            print(f"❌ Error: {response.status_code}")
            print(response.text)
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")
        print("💡 Make sure the backend server is running on localhost:9090")
    
    # Also test the regular recycle endpoint to see the error
    print("\n" + "=" * 50)
    print("🔄 Testing actual recycle endpoint:")
    
    try:
        recycle_url = f"{base_url}/api/campaigns/{campaign_id}/recycle"
        recycle_data = {
            "create_new_campaign": False
        }
        
        response = requests.post(recycle_url, json=recycle_data, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Recycle successful:")
            print(json.dumps(data, indent=2))
        else:
            data = response.json()
            print(f"❌ Recycle failed ({response.status_code}):")
            print(json.dumps(data, indent=2))
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")

def list_campaigns():
    """List all campaigns to find campaign IDs"""
    base_url = "http://localhost:9090"
    
    try:
        response = requests.get(f"{base_url}/api/campaigns", timeout=10)
        
        if response.status_code == 200:
            campaigns = response.json()
            print("📋 Available campaigns:")
            for campaign in campaigns:
                print(f"  ID: {campaign.get('id')} | Name: {campaign.get('name')} | Status: {campaign.get('status')}")
            return campaigns
        else:
            print(f"❌ Error listing campaigns: {response.status_code}")
            return []
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")
        return []

if __name__ == "__main__":
    print("🔍 Campaign Recycle Debug Tool")
    print("=" * 50)
    
    # List campaigns first
    campaigns = list_campaigns()
    
    if len(sys.argv) > 1:
        campaign_id = sys.argv[1]
        test_campaign_debug(campaign_id)
    else:
        print("\n💡 Usage: python test_recycle_debug.py <campaign_id>")
        print("💡 Available campaign IDs are shown above")
        if campaigns:
            # Use the first campaign as an example
            first_campaign = campaigns[0]
            campaign_id = first_campaign.get('id')
            print(f"\n🚀 Testing with first campaign: {campaign_id}")
            test_campaign_debug(campaign_id) 