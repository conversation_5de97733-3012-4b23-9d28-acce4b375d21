# Fix call statuses that were incorrectly marked

Write-Host "Fixing call statuses for answered calls..."

# Wait for backend to be ready
Start-Sleep -Seconds 2

# Find the CB call (should be answered, not no_answer)
$cb_result = Invoke-WebRequest -Uri "http://localhost:9090/api/call-logs" -Method GET | ConvertFrom-Json
$cb_index = -1
for ($i = 0; $i -lt $cb_result.Count; $i++) {
    if ($cb_result[$i].contact_phone -eq "+918149718077" -and $cb_result[$i].timestamp -like "2025-06-08T10:56:*") {
        $cb_index = $i
        break
    }
}

# Find the Mike rose call (should be answered, not no_answer)  
$mike_rose_index = -1
for ($i = 0; $i -lt $cb_result.Count; $i++) {
    if ($cb_result[$i].contact_phone -eq "+919552775831" -and $cb_result[$i].timestamp -like "2025-06-08T10:56:*") {
        $mike_rose_index = $i
        break
    }
}

# Fix CB's status (if it had call.answered events)
if ($cb_index -ge 0) {
    Write-Host "Fixing CB call status at index $cb_index"
    $body = @{
        status = "answered"
        reason = "Had call.answered events - incorrectly marked as no_answer"
    } | ConvertTo-Json
    
    try {
        Invoke-WebRequest -Uri "http://localhost:9090/api/call-logs/$cb_index/status" -Method PUT -Body $body -ContentType "application/json"
        Write-Host "✅ Fixed CB's call status"
    } catch {
        Write-Host "❌ Failed to fix CB's call status: $($_.Exception.Message)"
    }
}

# Fix Mike rose's status
if ($mike_rose_index -ge 0) {
    Write-Host "Fixing Mike rose call status at index $mike_rose_index"
    $body = @{
        status = "answered"
        reason = "Had call.answered events and conversation - incorrectly marked as no_answer"
    } | ConvertTo-Json
    
    try {
        Invoke-WebRequest -Uri "http://localhost:9090/api/call-logs/$mike_rose_index/status" -Method PUT -Body $body -ContentType "application/json"
        Write-Host "✅ Fixed Mike rose's call status"
    } catch {
        Write-Host "❌ Failed to fix Mike rose's call status: $($_.Exception.Message)"
    }
}

Write-Host "Call status fixes completed!" 