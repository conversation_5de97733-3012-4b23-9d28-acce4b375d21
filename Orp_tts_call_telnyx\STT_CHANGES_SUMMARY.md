# STT Migration: Groq to Deepgram STT

## Summary
Successfully migrated from **Groq Whisper STT** to **Deepgram STT** for better reliability and performance.

## Changes Made

### 1. **web_agent_template.py**
- ✅ Commented out: `from groq_stt import GroqSTT`
- ✅ Replaced with: `# from groq_stt import GroqSTT  # Commented out - switching to Deepgram`
- ✅ Updated prewarm() function to use Deepgram STT:
  ```python
  # NEW: Deepgram STT
  proc.userdata["stt"] = deepgram.STT(
      api_key=os.getenv("DEEPGRAM_API_KEY"),
      model="nova-2-general",  # Latest and most accurate model
      language="en-US",
      interim_results=True,
  )
  
  # OLD: Groq STT (commented out)
  # proc.userdata["stt"] = GroqSTT(
  #     model="whisper-large-v3-turbo",
  #     api_key=os.getenv("GROQ_API_KEY")
  # )
  ```

### 2. **orp.py**
- ✅ Commented out: `from groq_stt import GroqSTT`
- ✅ Updated prewarm() function with same Deepgram STT configuration

### 3. **simple_backend.py**
- ✅ Fixed critical calling bottlenecks:
  - Added `concurrent.futures` import
  - Created `direct_livekit_call()` function to replace HTTP self-requests
  - Replaced unlimited threading with ThreadPoolExecutor (max 8 concurrent calls)
  - Added proper error handling and timeouts

## Environment Variables Required

Make sure you have `DEEPGRAM_API_KEY` set in your environment:

```bash
export DEEPGRAM_API_KEY="your_deepgram_api_key_here"
```

## Benefits of Deepgram STT

1. **Better Reliability**: More stable than Groq Whisper for real-time streaming
2. **Lower Latency**: Optimized for real-time voice applications
3. **Real-time Processing**: Built for streaming audio applications
4. **Better Integration**: Native LiveKit plugin support
5. **Interim Results**: Provides real-time transcription updates

## Technical Details

- **Model**: `nova-2-general` (latest and most accurate)
- **Language**: `en-US`
- **Features**: `interim_results=True` for real-time feedback
- **Template Impact**: All future agents will use Deepgram STT automatically

## Calling Performance Improvements

Along with the STT switch, we also fixed the major calling bottlenecks:

1. **HTTP Self-Request Eliminated**: Replaced with direct function calls
2. **Thread Pool Limiting**: Max 8 concurrent calls to prevent system overload
3. **Proper Error Handling**: 60-second timeouts and better error reporting
4. **Call Staggering**: Brief pauses every 3 calls to avoid overwhelming LiveKit

**Expected Result**: Your system should now handle 15+ concurrent calls instead of being limited to 4-5 calls.

## Files Modified
- `web_agent_template.py` - Web agent STT configuration
- `orp.py` - Phone agent STT configuration  
- `simple_backend.py` - Backend calling optimizations

All changes are backward compatible and retain the original Groq STT code as comments for easy rollback if needed. 

## Summary
Successfully migrated from **Groq Whisper STT** to **Deepgram STT** for better reliability and performance.

## Changes Made

### 1. **web_agent_template.py**
- ✅ Commented out: `from groq_stt import GroqSTT`
- ✅ Replaced with: `# from groq_stt import GroqSTT  # Commented out - switching to Deepgram`
- ✅ Updated prewarm() function to use Deepgram STT:
  ```python
  # NEW: Deepgram STT
  proc.userdata["stt"] = deepgram.STT(
      api_key=os.getenv("DEEPGRAM_API_KEY"),
      model="nova-2-general",  # Latest and most accurate model
      language="en-US",
      interim_results=True,
  )
  
  # OLD: Groq STT (commented out)
  # proc.userdata["stt"] = GroqSTT(
  #     model="whisper-large-v3-turbo",
  #     api_key=os.getenv("GROQ_API_KEY")
  # )
  ```

### 2. **orp.py**
- ✅ Commented out: `from groq_stt import GroqSTT`
- ✅ Updated prewarm() function with same Deepgram STT configuration

### 3. **simple_backend.py**
- ✅ Fixed critical calling bottlenecks:
  - Added `concurrent.futures` import
  - Created `direct_livekit_call()` function to replace HTTP self-requests
  - Replaced unlimited threading with ThreadPoolExecutor (max 8 concurrent calls)
  - Added proper error handling and timeouts

## Environment Variables Required

Make sure you have `DEEPGRAM_API_KEY` set in your environment:

```bash
export DEEPGRAM_API_KEY="your_deepgram_api_key_here"
```

## Benefits of Deepgram STT

1. **Better Reliability**: More stable than Groq Whisper for real-time streaming
2. **Lower Latency**: Optimized for real-time voice applications
3. **Real-time Processing**: Built for streaming audio applications
4. **Better Integration**: Native LiveKit plugin support
5. **Interim Results**: Provides real-time transcription updates

## Technical Details

- **Model**: `nova-2-general` (latest and most accurate)
- **Language**: `en-US`
- **Features**: `interim_results=True` for real-time feedback
- **Template Impact**: All future agents will use Deepgram STT automatically

## Calling Performance Improvements

Along with the STT switch, we also fixed the major calling bottlenecks:

1. **HTTP Self-Request Eliminated**: Replaced with direct function calls
2. **Thread Pool Limiting**: Max 8 concurrent calls to prevent system overload
3. **Proper Error Handling**: 60-second timeouts and better error reporting
4. **Call Staggering**: Brief pauses every 3 calls to avoid overwhelming LiveKit

**Expected Result**: Your system should now handle 15+ concurrent calls instead of being limited to 4-5 calls.

## Files Modified
- `web_agent_template.py` - Web agent STT configuration
- `orp.py` - Phone agent STT configuration  
- `simple_backend.py` - Backend calling optimizations

All changes are backward compatible and retain the original Groq STT code as comments for easy rollback if needed. 