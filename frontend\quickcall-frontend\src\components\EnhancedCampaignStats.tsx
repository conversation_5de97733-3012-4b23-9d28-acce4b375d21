"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Phone, CheckCircle, XCircle, Clock, Pause, Play, RefreshCw, Users, 
  BarChart3, AlertTriangle, DollarSign, Timer, Recycle, RotateCcw,
  TrendingUp, Target, Activity, Volume2, PhoneCall, PhoneOff,
  FileText, Download, Eye, Calendar, MapPin, MessageSquare
} from "lucide-react"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"

interface EnhancedCampaignStatsProps {
  campaignId: string
  isOpen: boolean
  onClose: () => void
  onRecycle?: (campaignId: string) => void
  onRestart?: (campaignId: string) => void
}

interface CallDetail {
  id: string
  from_phone: string
  to_phone: string
  contact_name: string
  status: string
  outcome: string
  start_time: string
  end_time?: string
  duration: number
  cost: number
  recording_url?: string
  notes?: string
}

interface CampaignStatus {
  id: string
  name: string
  status: string
  created_at: string
  started_at?: string
  completed_at?: string
  agent_name: string
  total_contacts: number
}

interface EnhancedStats {
  campaign_info: CampaignStatus
  progress_percent: number
  totals: {
    total_calls: number
    completed_calls: number
    failed_calls: number
    no_answer_calls: number
    short_calls: number
    unknown_calls: number
  }
  duration: {
    total_duration_seconds: number
    total_duration_minutes: number
    average_duration_seconds: number
  }
  costs: {
    total_cost_usd: number
    average_cost_per_call: number
  }
  call_statistics: {
    total_calls: number
    completed: number
    failed: number
    no_answer: number
  }
  calls: CallDetail[]
  data_source?: string
}

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:9090'

export function EnhancedCampaignStats({ campaignId, isOpen, onClose, onRecycle, onRestart }: EnhancedCampaignStatsProps) {
  const [stats, setStats] = useState<EnhancedStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedTab, setSelectedTab] = useState("overview")
  const [refreshing, setRefreshing] = useState(false)
  const [unresponsiveContacts, setUnresponsiveContacts] = useState<any[]>([])

  const fetchStats = async () => {
    if (!isOpen) return
    
    try {
      setRefreshing(true)
      
      // Try Telnyx-verified stats first (most accurate)
      const telnyxResponse = await fetch(`${API_URL}/api/campaigns/${campaignId}/statistics/telnyx`)
      if (telnyxResponse.ok) {
        const telnyxData = await telnyxResponse.json()
        setStats(telnyxData)
        setError(null)
        return
      }
      
      // Fallback to enhanced stats
      const enhancedResponse = await fetch(`${API_URL}/api/campaigns/${campaignId}/statistics/enhanced`)
      if (enhancedResponse.ok) {
        const enhancedData = await enhancedResponse.json()
        if (enhancedData.success) {
          // Convert enhanced data format to our expected format
          const converted: EnhancedStats = {
            campaign_info: {
              id: campaignId,
              name: enhancedData.data.campaign_info?.name || 'Campaign',
              status: enhancedData.data.campaign_info?.status || 'unknown',
              created_at: enhancedData.data.campaign_info?.created_at || '',
              started_at: enhancedData.data.campaign_info?.started_at,
              completed_at: enhancedData.data.campaign_info?.completed_at,
              agent_name: enhancedData.data.campaign_info?.agent_name || 'AI Agent',
              total_contacts: enhancedData.data.progress?.total_contacts || 0
            },
            progress_percent: enhancedData.data.progress?.progress_percentage || 0,
            totals: {
              total_calls: enhancedData.data.summary?.total_calls || 0,
              completed_calls: enhancedData.data.summary?.successful_calls || 0,
              failed_calls: enhancedData.data.summary?.failed_calls || 0,
              no_answer_calls: enhancedData.data.summary?.no_answer_calls || 0,
              short_calls: enhancedData.data.summary?.short_calls || 0,
              unknown_calls: 0
            },
            duration: {
              total_duration_seconds: enhancedData.data.summary?.total_duration_seconds || 0,
              total_duration_minutes: enhancedData.data.summary?.total_duration_minutes || 0,
              average_duration_seconds: enhancedData.data.summary?.average_duration_seconds || 0
            },
            costs: {
              total_cost_usd: enhancedData.data.summary?.total_cost || 0,
              average_cost_per_call: enhancedData.data.summary?.average_cost_per_call || 0
            },
            call_statistics: {
              total_calls: enhancedData.data.summary?.total_calls || 0,
              completed: enhancedData.data.summary?.successful_calls || 0,
              failed: enhancedData.data.summary?.failed_calls || 0,
              no_answer: enhancedData.data.summary?.no_answer_calls || 0
            },
            calls: enhancedData.data.call_analysis?.latest_calls || [],
            data_source: 'enhanced'
          }
          setStats(converted)
          setError(null)
          return
        }
      }
      
      // Fallback to basic stats
      const basicResponse = await fetch(`${API_URL}/api/campaigns/${campaignId}/statistics`)
      if (basicResponse.ok) {
        const basicData = await basicResponse.json()
        setStats(basicData)
        setError(null)
        return
      }
      
      throw new Error('Failed to fetch campaign statistics from all endpoints')
      
    } catch (err) {
      console.error('Error fetching campaign stats:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  const fetchUnresponsiveContacts = async () => {
    try {
      const response = await fetch(`${API_URL}/api/campaigns/${campaignId}/unresponsive`)
      if (response.ok) {
        const data = await response.json()
        setUnresponsiveContacts(data.unresponsive_contacts || [])
      }
    } catch (err) {
      console.error('Error fetching unresponsive contacts:', err)
    }
  }

  useEffect(() => {
    if (isOpen) {
      fetchStats()
      fetchUnresponsiveContacts()
      
      // Auto-refresh every 30 seconds for active campaigns
      const interval = setInterval(() => {
        if (stats?.campaign_info.status === 'running') {
          fetchStats()
        }
      }, 30000)
      
      return () => clearInterval(interval)
    }
  }, [isOpen, campaignId])

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      running: { variant: "default" as const, icon: Activity, color: "text-blue-600" },
      completed: { variant: "secondary" as const, icon: CheckCircle, color: "text-green-600" },
      paused: { variant: "outline" as const, icon: Pause, color: "text-yellow-600" },
      failed: { variant: "destructive" as const, icon: XCircle, color: "text-red-600" },
      draft: { variant: "outline" as const, icon: Clock, color: "text-gray-600" }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft
    const Icon = config.icon
    
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  const getOutcomeIcon = (status: string) => {
    if (!status) {
      return <Clock className="h-4 w-4 text-gray-500" />
    }
    
    switch (status.toLowerCase()) {
      case 'completed':
      case 'answered':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'no_answer':
      case 'no-answer':
        return <PhoneOff className="h-4 w-4 text-yellow-500" />
      case 'busy':
        return <Phone className="h-4 w-4 text-orange-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}m ${remainingSeconds}s`
  }

  const formatCurrency = (amount: number) => {
    return `$${amount.toFixed(4)}`
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const calculateSuccessRate = () => {
    if (!stats || stats.totals.total_calls === 0) return 0
    return Math.round((stats.totals.completed_calls / stats.totals.total_calls) * 100)
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-4">
            <BarChart3 className="h-6 w-6 text-blue-600" />
            <div>
              <h2 className="text-xl font-semibold">Campaign Analytics</h2>
              <p className="text-sm text-gray-500">
                {stats?.campaign_info.name || `Campaign ${campaignId}`}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={fetchStats}
              disabled={refreshing}
            >
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button variant="outline" size="sm" onClick={onClose}>
              ✕
            </Button>
          </div>
        </div>

        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="flex items-center space-x-2">
              <RefreshCw className="h-5 w-5 animate-spin" />
              <span>Loading campaign statistics...</span>
            </div>
          </div>
        ) : error ? (
          <div className="p-6">
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Error loading campaign statistics: {error}
              </AlertDescription>
            </Alert>
          </div>
        ) : !stats ? (
          <div className="p-6">
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                No statistics available for this campaign.
              </AlertDescription>
            </Alert>
          </div>
        ) : (
          <div className="p-6">
            {/* Campaign Header */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Status</p>
                      <div className="mt-1">
                        {getStatusBadge(stats.campaign_info.status)}
                      </div>
                    </div>
                    <Activity className="h-5 w-5 text-gray-400" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Progress</p>
                      <p className="text-lg font-semibold">{stats.progress_percent.toFixed(1)}%</p>
                      <Progress value={stats.progress_percent} className="mt-1" />
                    </div>
                    <Target className="h-5 w-5 text-gray-400" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Success Rate</p>
                      <p className="text-lg font-semibold text-green-600">{calculateSuccessRate()}%</p>
                    </div>
                    <TrendingUp className="h-5 w-5 text-gray-400" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Total Cost</p>
                      <p className="text-lg font-semibold text-blue-600">
                        {formatCurrency(stats.costs.total_cost_usd)}
                      </p>
                    </div>
                    <DollarSign className="h-5 w-5 text-gray-400" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Main Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center text-sm">
                    <Phone className="h-4 w-4 mr-2" />
                    Call Metrics
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm">Total Calls:</span>
                      <span className="font-medium">{stats.totals.total_calls}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Completed:</span>
                      <span className="font-medium text-green-600">{stats.totals.completed_calls}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Failed:</span>
                      <span className="font-medium text-red-600">{stats.totals.failed_calls}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">No Answer:</span>
                      <span className="font-medium text-yellow-600">{stats.totals.no_answer_calls}</span>
                    </div>
                    {stats.data_source && (
                      <div className="pt-2 border-t">
                        <span className="text-xs text-gray-500">
                          Data Source: {stats.data_source === 'telnyx_api' ? 'Telnyx API' : 'Local Logs'}
                        </span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center text-sm">
                    <Timer className="h-4 w-4 mr-2" />
                    Duration Analysis
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm">Total Duration:</span>
                      <span className="font-medium">{formatDuration(stats.duration.total_duration_seconds)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Average Duration:</span>
                      <span className="font-medium">{formatDuration(Math.round(stats.duration.average_duration_seconds))}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Total Minutes:</span>
                      <span className="font-medium">{stats.duration.total_duration_minutes.toFixed(1)}m</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Short Calls:</span>
                      <span className="font-medium">{stats.totals.short_calls}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center text-sm">
                    <DollarSign className="h-4 w-4 mr-2" />
                    Cost Analysis
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm">Total Cost:</span>
                      <span className="font-medium text-blue-600">{formatCurrency(stats.costs.total_cost_usd)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Avg Cost/Call:</span>
                      <span className="font-medium">{formatCurrency(stats.costs.average_cost_per_call)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Cost/Minute:</span>
                      <span className="font-medium">
                        {stats.duration.total_duration_minutes > 0 
                          ? formatCurrency(stats.costs.total_cost_usd / stats.duration.total_duration_minutes) 
                          : '$0.0000'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Rate:</span>
                      <span className="font-medium text-gray-600">~$0.013/min</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Detailed Tabs */}
            <Tabs value={selectedTab} onValueChange={setSelectedTab}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="calls">Recent Calls</TabsTrigger>
                <TabsTrigger value="actions">Actions</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="mt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Campaign Information</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-500">Agent:</span>
                          <span className="font-medium">{stats.campaign_info.agent_name}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-500">Total Contacts:</span>
                          <span className="font-medium">{stats.campaign_info.total_contacts}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-500">Created:</span>
                          <span className="font-medium">{formatDateTime(stats.campaign_info.created_at)}</span>
                        </div>
                        {stats.campaign_info.started_at && (
                          <div className="flex justify-between">
                            <span className="text-gray-500">Started:</span>
                            <span className="font-medium">{formatDateTime(stats.campaign_info.started_at)}</span>
                          </div>
                        )}
                        {stats.campaign_info.completed_at && (
                          <div className="flex justify-between">
                            <span className="text-gray-500">Completed:</span>
                            <span className="font-medium">{formatDateTime(stats.campaign_info.completed_at)}</span>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Performance Metrics</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div>
                          <div className="flex justify-between mb-1">
                            <span className="text-sm text-gray-500">Success Rate</span>
                            <span className="text-sm font-medium">{calculateSuccessRate()}%</span>
                          </div>
                          <Progress value={calculateSuccessRate()} className="h-2" />
                        </div>
                        <div>
                          <div className="flex justify-between mb-1">
                            <span className="text-sm text-gray-500">Progress</span>
                            <span className="text-sm font-medium">{stats.progress_percent.toFixed(1)}%</span>
                          </div>
                          <Progress value={stats.progress_percent} className="h-2" />
                        </div>
                        <div>
                          <div className="flex justify-between mb-1">
                            <span className="text-sm text-gray-500">Answer Rate</span>
                            <span className="text-sm font-medium">
                              {stats.totals.total_calls > 0 
                                ? Math.round(((stats.totals.completed_calls + stats.totals.no_answer_calls) / stats.totals.total_calls) * 100)
                                : 0}%
                            </span>
                          </div>
                          <Progress 
                            value={stats.totals.total_calls > 0 
                              ? ((stats.totals.completed_calls + stats.totals.no_answer_calls) / stats.totals.total_calls) * 100
                              : 0} 
                            className="h-2" 
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="calls" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center text-sm">
                      <FileText className="h-4 w-4 mr-2" />
                      Recent Call Details ({stats.calls.length})
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-64">
                      {stats.calls.length === 0 ? (
                        <div className="text-center py-8 text-gray-500">
                          No call data available
                        </div>
                      ) : (
                        <div className="space-y-2">
                          {stats.calls.slice(0, 50).map((call, index) => (
                            <div key={call.id || index} className="flex items-center justify-between p-3 border rounded-lg">
                              <div className="flex items-center space-x-3">
                                {getOutcomeIcon(call.status)}
                                <div>
                                  <p className="font-medium text-sm">{call.contact_name || 'Unknown'}</p>
                                  <p className="text-xs text-gray-500">{call.to_phone}</p>
                                </div>
                              </div>
                              <div className="text-right">
                                <p className="text-sm font-medium">{formatDuration(call.duration || 0)}</p>
                                <p className="text-xs text-gray-500">{formatCurrency(call.cost || 0)}</p>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </ScrollArea>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="actions" className="mt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Campaign Actions</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {onRestart && (
                          <Button 
                            onClick={() => onRestart(campaignId)} 
                            className="w-full"
                            variant="outline"
                          >
                            <RotateCcw className="h-4 w-4 mr-2" />
                            Restart Campaign
                          </Button>
                        )}
                        {onRecycle && unresponsiveContacts.length > 0 && (
                          <Button 
                            onClick={() => onRecycle(campaignId)} 
                            className="w-full"
                            variant="outline"
                          >
                            <Recycle className="h-4 w-4 mr-2" />
                            Recycle Unresponsive ({unresponsiveContacts.length})
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Unresponsive Contacts</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ScrollArea className="h-32">
                        {unresponsiveContacts.length === 0 ? (
                          <p className="text-sm text-gray-500">No unresponsive contacts</p>
                        ) : (
                          <div className="space-y-1">
                            {unresponsiveContacts.slice(0, 10).map((contact, index) => (
                              <div key={index} className="text-xs p-2 bg-gray-50 rounded">
                                <p className="font-medium">{contact.contact_name}</p>
                                <p className="text-gray-500">{contact.phone_number}</p>
                              </div>
                            ))}
                          </div>
                        )}
                      </ScrollArea>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        )}
      </div>
    </div>
  )
} 