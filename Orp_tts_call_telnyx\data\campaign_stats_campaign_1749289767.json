{"campaign_id": "campaign_1749289767", "created_at": "2025-06-07T09:49:39.701193+00:00", "total_calls": 5, "completed_calls": 0, "failed_calls": 0, "no_answer_calls": 0, "busy_calls": 0, "total_duration_seconds": 0, "total_cost_usd": 0.0, "calls": [{"call_id": "call_campaign_1749289767_1_1749289779", "phone_number": "+917420068477", "contact_name": "<PERSON>", "notes": "Interested in our services", "status": "dialing", "start_time": "2025-06-07T09:49:39.722207+00:00", "end_time": null, "duration_seconds": 0, "cost_usd": 0.0, "disconnect_reason": null}, {"call_id": "call_campaign_1749289767_2_1749289779", "phone_number": "+923023638174", "contact_name": "", "notes": "Demo request", "status": "dialing", "start_time": "2025-06-07T09:49:39.898719+00:00", "end_time": null, "duration_seconds": 0, "cost_usd": 0.0, "disconnect_reason": null}, {"call_id": "call_campaign_1749289767_3_1749289780", "phone_number": "+923047587700", "contact_name": "<PERSON>", "notes": "Demo request", "status": "dialing", "start_time": "2025-06-07T09:49:40.370384+00:00", "end_time": null, "duration_seconds": 0, "cost_usd": 0.0, "disconnect_reason": null}, {"call_id": "call_campaign_1749289767_4_1749289781", "phone_number": "+919552775831", "contact_name": "<PERSON>", "notes": "Follow-up call needed", "status": "dialing", "start_time": "2025-06-07T09:49:41.058763+00:00", "end_time": null, "duration_seconds": 0, "cost_usd": 0.0, "disconnect_reason": null}, {"call_id": "call_campaign_1749289767_5_1749289781", "phone_number": "+923187661096", "contact_name": "<PERSON>", "notes": "Demo request", "status": "dialing", "start_time": "2025-06-07T09:49:41.620076+00:00", "end_time": null, "duration_seconds": 0, "cost_usd": 0.0, "disconnect_reason": null}]}