#!/usr/bin/env python3
"""
Test script that replicates the exact user scenario:
- 2 contacts named "<PERSON>" 
- Different phone numbers
- 2 Telnyx numbers
- Shared AI mode
"""

import requests
import json
import time
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:9090"

def test_user_scenario():
    """Test the exact user scenario with 2 <PERSON> contacts"""
    
    logger.info("🧪 Testing user's exact scenario...")
    
    # Exact same data as user's test
    test_campaign = {
        "name": "User Scenario Test",
        "agentId": "mia",
        "firstMessage": "Hello, how can I help you today?",
        "telnyxNumbers": ["+18162196532", "+19199256164"],
        "usePersonalizedAI": False,  # Shared AI mode
        "contacts": [
            {"name": "<PERSON>", "phone_number": "+917420068477"},
            {"name": "<PERSON>", "phone_number": "+919552775831"}
        ]
    }
    
    try:
        # 1. Create campaign
        logger.info("📝 Creating user scenario campaign...")
        response = requests.post(f"{BASE_URL}/api/campaigns", json=test_campaign)
        
        if response.status_code != 200:
            logger.error(f"❌ Failed to create campaign: {response.status_code} - {response.text}")
            return False
        
        campaign_data = response.json()
        campaign_id = campaign_data.get('campaign', {}).get('id')
        logger.info(f"✅ Campaign created: {campaign_id}")
        
        if not campaign_id:
            logger.error("❌ No campaign ID returned")
            return False
        
        # 2. Start campaign 
        logger.info("🚀 Starting campaign with shared agent pool...")
        response = requests.post(f"{BASE_URL}/api/campaigns/{campaign_id}/start")
        
        if response.status_code != 200:
            logger.error(f"❌ Failed to start campaign: {response.status_code} - {response.text}")
            return False
        
        logger.info("✅ Campaign started successfully")
        
        # 3. Wait for processing
        logger.info("⏳ Waiting for calls to process...")
        time.sleep(10)  # Give enough time for both calls
        
        # 4. Check shared agent stats
        logger.info("📊 Checking shared agent pool stats...")
        response = requests.get(f"{BASE_URL}/api/shared-agents/stats")
        
        if response.status_code == 200:
            stats = response.json()
            logger.info(f"📊 Shared agent stats: {json.dumps(stats, indent=2)}")
            
            if campaign_id in stats:
                campaign_stats = stats[campaign_id]
                total_agents = campaign_stats.get('total_agents', 0)
                busy_agents = campaign_stats.get('busy_agents', 0)
                available_agents = campaign_stats.get('available_agents', 0)
                
                logger.info(f"✅ Agent pool status: {total_agents} total, {busy_agents} busy, {available_agents} available")
                
                # Check if we have multiple agents
                if total_agents >= 2:
                    logger.info("✅ Multiple agents created for concurrent calls")
                    return True
                else:
                    logger.error(f"❌ Expected at least 2 agents, got {total_agents}")
                    return False
            else:
                logger.warning(f"⚠️ No agent pool found for campaign {campaign_id} (calls may have completed)")
                return True  # Calls might have finished
        else:
            logger.error(f"❌ Failed to get shared agent stats: {response.status_code}")
            return False
        
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {str(e)}")
        return False

def test_sip_distribution():
    """Test SIP trunk distribution for the two numbers"""
    
    logger.info("🧪 Testing SIP trunk distribution...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/debug/sip-numbers")
        
        if response.status_code == 200:
            sip_data = response.json()
            logger.info(f"📊 SIP configuration: {json.dumps(sip_data, indent=2)}")
            
            # Check if both numbers have SIP trunks
            mapping = sip_data.get('mapping', {})
            
            if '+18162196532' in mapping and '+19199256164' in mapping:
                logger.info("✅ Both numbers have SIP trunk mappings")
                
                trunk1 = mapping['+18162196532']
                trunk2 = mapping['+19199256164']
                
                if trunk1 != trunk2:
                    logger.info(f"✅ Different SIP trunks: {trunk1} vs {trunk2}")
                    return True
                else:
                    logger.warning(f"⚠️ Same SIP trunk for both numbers: {trunk1}")
                    return True  # Still valid, just not optimal
            else:
                logger.error("❌ Missing SIP trunk mappings for one or both numbers")
                return False
        else:
            logger.error(f"❌ Failed to get SIP data: {response.status_code}")
            return False
        
    except Exception as e:
        logger.error(f"❌ SIP test failed with exception: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("🔬 Testing user's exact scenario...")
    
    # Wait a bit for backend to be ready
    time.sleep(2)
    
    # Test 1: User scenario
    test1_result = test_user_scenario()
    logger.info(f"Test 1 (User Scenario): {'✅ PASSED' if test1_result else '❌ FAILED'}")
    
    # Test 2: SIP distribution
    test2_result = test_sip_distribution()
    logger.info(f"Test 2 (SIP Distribution): {'✅ PASSED' if test2_result else '❌ FAILED'}")
    
    # Overall result
    overall_success = test1_result and test2_result
    logger.info(f"\n🏁 Overall Test Result: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    if overall_success:
        logger.info("🎉 Your scenario should now work with concurrent calls!")
    else:
        logger.info("🔧 There are still issues to fix.") 
"""
Test script that replicates the exact user scenario:
- 2 contacts named "Mike Johnson" 
- Different phone numbers
- 2 Telnyx numbers
- Shared AI mode
"""

import requests
import json
import time
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:9090"

def test_user_scenario():
    """Test the exact user scenario with 2 Mike Johnson contacts"""
    
    logger.info("🧪 Testing user's exact scenario...")
    
    # Exact same data as user's test
    test_campaign = {
        "name": "User Scenario Test",
        "agentId": "mia",
        "firstMessage": "Hello, how can I help you today?",
        "telnyxNumbers": ["+18162196532", "+19199256164"],
        "usePersonalizedAI": False,  # Shared AI mode
        "contacts": [
            {"name": "Mike Johnson", "phone_number": "+917420068477"},
            {"name": "Mike Johnson", "phone_number": "+919552775831"}
        ]
    }
    
    try:
        # 1. Create campaign
        logger.info("📝 Creating user scenario campaign...")
        response = requests.post(f"{BASE_URL}/api/campaigns", json=test_campaign)
        
        if response.status_code != 200:
            logger.error(f"❌ Failed to create campaign: {response.status_code} - {response.text}")
            return False
        
        campaign_data = response.json()
        campaign_id = campaign_data.get('campaign', {}).get('id')
        logger.info(f"✅ Campaign created: {campaign_id}")
        
        if not campaign_id:
            logger.error("❌ No campaign ID returned")
            return False
        
        # 2. Start campaign 
        logger.info("🚀 Starting campaign with shared agent pool...")
        response = requests.post(f"{BASE_URL}/api/campaigns/{campaign_id}/start")
        
        if response.status_code != 200:
            logger.error(f"❌ Failed to start campaign: {response.status_code} - {response.text}")
            return False
        
        logger.info("✅ Campaign started successfully")
        
        # 3. Wait for processing
        logger.info("⏳ Waiting for calls to process...")
        time.sleep(10)  # Give enough time for both calls
        
        # 4. Check shared agent stats
        logger.info("📊 Checking shared agent pool stats...")
        response = requests.get(f"{BASE_URL}/api/shared-agents/stats")
        
        if response.status_code == 200:
            stats = response.json()
            logger.info(f"📊 Shared agent stats: {json.dumps(stats, indent=2)}")
            
            if campaign_id in stats:
                campaign_stats = stats[campaign_id]
                total_agents = campaign_stats.get('total_agents', 0)
                busy_agents = campaign_stats.get('busy_agents', 0)
                available_agents = campaign_stats.get('available_agents', 0)
                
                logger.info(f"✅ Agent pool status: {total_agents} total, {busy_agents} busy, {available_agents} available")
                
                # Check if we have multiple agents
                if total_agents >= 2:
                    logger.info("✅ Multiple agents created for concurrent calls")
                    return True
                else:
                    logger.error(f"❌ Expected at least 2 agents, got {total_agents}")
                    return False
            else:
                logger.warning(f"⚠️ No agent pool found for campaign {campaign_id} (calls may have completed)")
                return True  # Calls might have finished
        else:
            logger.error(f"❌ Failed to get shared agent stats: {response.status_code}")
            return False
        
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {str(e)}")
        return False

def test_sip_distribution():
    """Test SIP trunk distribution for the two numbers"""
    
    logger.info("🧪 Testing SIP trunk distribution...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/debug/sip-numbers")
        
        if response.status_code == 200:
            sip_data = response.json()
            logger.info(f"📊 SIP configuration: {json.dumps(sip_data, indent=2)}")
            
            # Check if both numbers have SIP trunks
            mapping = sip_data.get('mapping', {})
            
            if '+18162196532' in mapping and '+19199256164' in mapping:
                logger.info("✅ Both numbers have SIP trunk mappings")
                
                trunk1 = mapping['+18162196532']
                trunk2 = mapping['+19199256164']
                
                if trunk1 != trunk2:
                    logger.info(f"✅ Different SIP trunks: {trunk1} vs {trunk2}")
                    return True
                else:
                    logger.warning(f"⚠️ Same SIP trunk for both numbers: {trunk1}")
                    return True  # Still valid, just not optimal
            else:
                logger.error("❌ Missing SIP trunk mappings for one or both numbers")
                return False
        else:
            logger.error(f"❌ Failed to get SIP data: {response.status_code}")
            return False
        
    except Exception as e:
        logger.error(f"❌ SIP test failed with exception: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("🔬 Testing user's exact scenario...")
    
    # Wait a bit for backend to be ready
    time.sleep(2)
    
    # Test 1: User scenario
    test1_result = test_user_scenario()
    logger.info(f"Test 1 (User Scenario): {'✅ PASSED' if test1_result else '❌ FAILED'}")
    
    # Test 2: SIP distribution
    test2_result = test_sip_distribution()
    logger.info(f"Test 2 (SIP Distribution): {'✅ PASSED' if test2_result else '❌ FAILED'}")
    
    # Overall result
    overall_success = test1_result and test2_result
    logger.info(f"\n🏁 Overall Test Result: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    if overall_success:
        logger.info("🎉 Your scenario should now work with concurrent calls!")
    else:
        logger.info("🔧 There are still issues to fix.") 