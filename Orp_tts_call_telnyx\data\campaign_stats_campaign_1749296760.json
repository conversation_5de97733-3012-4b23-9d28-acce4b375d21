{"campaign_id": "campaign_1749296760", "created_at": "2025-06-07T11:46:17.551419+00:00", "total_calls": 14, "completed_calls": 0, "failed_calls": 0, "no_answer_calls": 0, "busy_calls": 0, "total_duration_seconds": 0, "total_cost_usd": 0.0, "calls": [{"call_id": "call_campaign_1749296760_1_1749296777", "phone_number": "+917420068477", "contact_name": "<PERSON>", "notes": "Demo request", "status": "dialing", "start_time": "2025-06-07T11:46:17.569422+00:00", "end_time": null, "duration_seconds": 0, "cost_usd": 0.0, "disconnect_reason": null}, {"call_id": "call_campaign_1749296760_2_1749296778", "phone_number": "+919552775831", "contact_name": "<PERSON>", "notes": "Demo request", "status": "dialing", "start_time": "2025-06-07T11:46:18.206673+00:00", "end_time": null, "duration_seconds": 0, "cost_usd": 0.0, "disconnect_reason": null}, {"call_id": "call_campaign_1749296760_3_1749298560", "phone_number": "+917420068477", "contact_name": "<PERSON>", "notes": "Demo request", "status": "dialing", "start_time": "2025-06-07T12:16:00.901849+00:00", "end_time": null, "duration_seconds": 0, "cost_usd": 0.0, "disconnect_reason": null}, {"call_id": "call_campaign_1749296760_4_1749298560", "phone_number": "+919552775831", "contact_name": "<PERSON>", "notes": "Demo request", "status": "dialing", "start_time": "2025-06-07T12:16:00.931850+00:00", "end_time": null, "duration_seconds": 0, "cost_usd": 0.0, "disconnect_reason": null}, {"call_id": "call_campaign_1749296760_5_1749298817", "phone_number": "+919552775831", "contact_name": "<PERSON>", "notes": "Demo request", "status": "dialing", "start_time": "2025-06-07T12:20:17.990698+00:00", "end_time": null, "duration_seconds": 0, "cost_usd": 0.0, "disconnect_reason": null}, {"call_id": "call_campaign_1749296760_6_1749298819", "phone_number": "+917420068477", "contact_name": "<PERSON>", "notes": "Demo request", "status": "dialing", "start_time": "2025-06-07T12:20:19.006948+00:00", "end_time": null, "duration_seconds": 0, "cost_usd": 0.0, "disconnect_reason": null}, {"call_id": "call_campaign_1749296760_7_1749300175", "phone_number": "+917420068477", "contact_name": "<PERSON>", "notes": "Demo request", "status": "dialing", "start_time": "2025-06-07T12:42:55.905358+00:00", "end_time": null, "duration_seconds": 0, "cost_usd": 0.0, "disconnect_reason": null}, {"call_id": "call_campaign_1749296760_8_1749300176", "phone_number": "+919552775831", "contact_name": "<PERSON>", "notes": "Demo request", "status": "dialing", "start_time": "2025-06-07T12:42:56.140403+00:00", "end_time": null, "duration_seconds": 0, "cost_usd": 0.0, "disconnect_reason": null}, {"call_id": "call_campaign_1749296760_9_1749301062", "phone_number": "+919552775831", "contact_name": "<PERSON>", "notes": "Demo request", "status": "dialing", "start_time": "2025-06-07T12:57:42.052548+00:00", "end_time": null, "duration_seconds": 0, "cost_usd": 0.0, "disconnect_reason": null}, {"call_id": "call_campaign_1749296760_10_1749301062", "phone_number": "+917420068477", "contact_name": "<PERSON>", "notes": "Demo request", "status": "dialing", "start_time": "2025-06-07T12:57:42.854400+00:00", "end_time": null, "duration_seconds": 0, "cost_usd": 0.0, "disconnect_reason": null}, {"call_id": "call_campaign_1749296760_11_1749301581", "phone_number": "+917420068477", "contact_name": "<PERSON>", "notes": "Demo request", "status": "dialing", "start_time": "2025-06-07T13:06:21.915067+00:00", "end_time": null, "duration_seconds": 0, "cost_usd": 0.0, "disconnect_reason": null}, {"call_id": "call_campaign_1749296760_12_1749301582", "phone_number": "+919552775831", "contact_name": "<PERSON>", "notes": "Demo request", "status": "dialing", "start_time": "2025-06-07T13:06:22.904467+00:00", "end_time": null, "duration_seconds": 0, "cost_usd": 0.0, "disconnect_reason": null}, {"call_id": "call_campaign_1749296760_13_1749301691", "phone_number": "+917420068477", "contact_name": "<PERSON>", "notes": "Demo request", "status": "dialing", "start_time": "2025-06-07T13:08:11.530910+00:00", "end_time": null, "duration_seconds": 0, "cost_usd": 0.0, "disconnect_reason": null}, {"call_id": "call_campaign_1749296760_14_1749301691", "phone_number": "+919552775831", "contact_name": "<PERSON>", "notes": "Demo request", "status": "dialing", "start_time": "2025-06-07T13:08:11.657633+00:00", "end_time": null, "duration_seconds": 0, "cost_usd": 0.0, "disconnect_reason": null}]}