"""
Telnyx Call Analyzer
Fetches real call status information from Telnyx API for accurate campaign statistics
"""

import os
import json
import requests
import telnyx
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

class TelnyxCallAnalyzer:
    def __init__(self, api_key: str = None, data_dir: str = "data"):
        self.api_key = api_key or os.getenv('TELNYX_API_KEY')
        self.data_dir = Path(data_dir)
        self.base_url = "https://api.telnyx.com/v2"
        
        if not self.api_key:
            raise ValueError("Telnyx API key is required")
        
        # Initialize Telnyx SDK
        telnyx.api_key = self.api_key
        
    def get_call_status(self, call_control_id: str) -> Dict[str, Any]:
        """Get call status from Telnyx API"""
        try:
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(
                f"{self.base_url}/calls/{call_control_id}",
                headers=headers
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to get call status: {response.status_code} - {response.text}")
                return {'error': f'API error: {response.status_code}'}
                
        except Exception as e:
            logger.error(f"Error fetching call status: {e}")
            return {'error': str(e)}
    
    def load_call_logs(self) -> List[Dict]:
        """Load call logs from local JSON file"""
        call_logs_file = self.data_dir / "call_logs.json"
        if call_logs_file.exists():
            try:
                with open(call_logs_file, 'r') as f:
                    return json.load(f)
            except:
                return []
        return []
    
    def save_call_logs(self, logs: List[Dict]):
        """Save updated call logs"""
        call_logs_file = self.data_dir / "call_logs.json"
        try:
            with open(call_logs_file, 'w') as f:
                json.dump(logs, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Error saving call logs: {e}")
    
    def update_call_with_telnyx_status(self, call_log: Dict) -> Dict:
        """Update a call log with real Telnyx status"""
        telnyx_call_id = call_log.get('telnyx_call_id')
        if not telnyx_call_id:
            return call_log
        
        # Get real status from Telnyx
        telnyx_data = self.get_call_status(telnyx_call_id)
        
        if 'error' not in telnyx_data and 'data' in telnyx_data:
            call_data = telnyx_data['data']
            
            # Update call log with real Telnyx data
            call_log.update({
                'telnyx_status': call_data.get('record_type'),
                'is_alive': call_data.get('is_alive', False),
                'real_call_duration': call_data.get('call_duration', 0),
                'start_time': call_data.get('start_time'),
                'end_time': call_data.get('end_time'),
                'telnyx_updated': datetime.now().isoformat()
            })
            
            # Map Telnyx status to our categories
            # Note: Since we're using call control, we need to check if call is alive
            # and duration to determine actual outcome
            is_alive = call_data.get('is_alive', False)
            duration = call_data.get('call_duration', 0)
            
            if is_alive:
                call_log['accurate_status'] = 'in_progress'
            elif duration > 0:
                # Call had duration - it was answered
                if duration < 5:
                    call_log['accurate_status'] = 'short_call'
                else:
                    call_log['accurate_status'] = 'completed'
            else:
                # No duration - call was not answered
                call_log['accurate_status'] = 'no_answer'
        
        return call_log
    
    def analyze_campaign_with_telnyx(self, campaign_id: str) -> Dict[str, Any]:
        """Analyze campaign calls using real Telnyx data"""
        all_logs = self.load_call_logs()
        campaign_logs = [log for log in all_logs if log.get('campaign_id') == campaign_id]
        
        if not campaign_logs:
            return {
                'campaign_id': campaign_id,
                'error': 'No call logs found for this campaign',
                'total_calls': 0
            }
        
        # Update each call with real Telnyx status
        updated_logs = []
        for call_log in campaign_logs:
            updated_call = self.update_call_with_telnyx_status(call_log)
            updated_logs.append(updated_call)
        
        # Analyze the updated call logs
        outcomes = {
            'completed': 0,
            'short_call': 0,
            'no_answer': 0,
            'failed': 0,
            'in_progress': 0,
            'unknown': 0
        }
        
        total_duration = 0
        call_details = []
        
        for call in updated_logs:
            accurate_status = call.get('accurate_status', 'unknown')
            if accurate_status in outcomes:
                outcomes[accurate_status] += 1
            else:
                outcomes['unknown'] += 1
            
            # Use real duration if available
            duration = call.get('real_call_duration', call.get('call_duration', 0))
            total_duration += duration
            
            call_details.append({
                'contact_name': call.get('contact_name', 'Unknown'),
                'contact_phone': call.get('contact_phone', ''),
                'duration': duration,
                'original_status': call.get('status', 'unknown'),
                'accurate_status': accurate_status,
                'telnyx_status': call.get('telnyx_status', 'N/A'),
                'timestamp': call.get('timestamp', ''),
                'is_alive': call.get('is_alive', False)
            })
        
        # Update the call logs file with Telnyx data
        self.update_call_logs_with_telnyx_data(campaign_logs, updated_logs)
        
        return {
            'campaign_id': campaign_id,
            'total_calls': len(updated_logs),
            'call_statistics': {
                'total_calls': len(updated_logs),
                'completed': outcomes['completed'] + outcomes['short_call'],
                'failed': outcomes['failed'],
                'no_answer': outcomes['no_answer'],
                'in_progress': outcomes['in_progress'],
                'unknown': outcomes['unknown']
            },
            'detailed_outcomes': outcomes,
            'total_duration': total_duration,
            'call_details': call_details,
            'data_source': 'telnyx_api'
        }
    
    def update_call_logs_with_telnyx_data(self, original_logs: List[Dict], updated_logs: List[Dict]):
        """Update the main call logs file with Telnyx data"""
        try:
            all_logs = self.load_call_logs()
            
            # Create a mapping of updated logs by call ID
            updated_map = {}
            for updated_call in updated_logs:
                call_id = updated_call.get('id')
                if call_id:
                    updated_map[call_id] = updated_call
            
            # Update the main logs
            for i, log in enumerate(all_logs):
                log_id = log.get('id')
                if log_id in updated_map:
                    all_logs[i] = updated_map[log_id]
            
            # Save updated logs
            self.save_call_logs(all_logs)
            logger.info(f"Updated {len(updated_logs)} call logs with Telnyx data")
            
        except Exception as e:
            logger.error(f"Error updating call logs: {e}")
    
    def print_telnyx_report(self, campaign_id: str):
        """Print detailed report using Telnyx data"""
        analysis = self.analyze_campaign_with_telnyx(campaign_id)
        
        if 'error' in analysis:
            print(f"❌ {analysis['error']}")
            return
        
        print(f"📊 TELNYX-VERIFIED Campaign Analysis: {campaign_id}")
        print("=" * 70)
        print(f"📞 Total Calls: {analysis['total_calls']}")
        print(f"✅ Completed: {analysis['call_statistics']['completed']}")
        print(f"❌ Failed: {analysis['call_statistics']['failed']}")
        print(f"📵 No Answer: {analysis['call_statistics']['no_answer']}")
        print(f"🔄 In Progress: {analysis['call_statistics']['in_progress']}")
        print(f"❓ Unknown: {analysis['call_statistics']['unknown']}")
        print(f"⏱️ Total Duration: {analysis['total_duration']}s")
        print(f"🔍 Data Source: {analysis['data_source']}")
        print()
        
        print("📋 Call Details (with Telnyx verification):")
        for call in analysis['call_details']:
            print(f"  📞 {call['contact_name']} ({call['contact_phone']})")
            print(f"     Duration: {call['duration']}s")
            print(f"     Original Status: {call['original_status']}")
            print(f"     ✅ Accurate Status: {call['accurate_status']}")
            print(f"     🔍 Telnyx Status: {call['telnyx_status']}")
            print(f"     Time: {call['timestamp']}")
            print()

def main():
    """Main function for command line usage"""
    import sys
    
    try:
        analyzer = TelnyxCallAnalyzer()
        
        if len(sys.argv) > 1:
            campaign_id = sys.argv[1]
            analyzer.print_telnyx_report(campaign_id)
        else:
            print("Usage: python telnyx_call_analyzer.py <campaign_id>")
            
    except ValueError as e:
        print(f"❌ Configuration Error: {e}")
        print("Please set TELNYX_API_KEY environment variable")

if __name__ == "__main__":
    main() 