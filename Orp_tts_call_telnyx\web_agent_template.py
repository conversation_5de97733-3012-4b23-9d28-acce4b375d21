import sys
import os
# Ensure the current working directory is in sys.path
# This helps locate local modules like 'orpheus' when the script is run from a subdirectory
# but its CWD is set to the project root (e.g., Orp_tts_call_telnyx).
if os.getcwd() not in sys.path:
    sys.path.insert(0, os.getcwd())

import asyncio
import json
import logging
from dotenv import load_dotenv
import os
from time import perf_counter
from livekit import rtc, api
from livekit.agents import (
    AutoSubscribe,
    JobContext,
    JobProcess,
    WorkerOptions,
    cli,
    llm
)
from livekit.protocol.agent import JobType
from livekit.agents.pipeline import VoicePipelineAgent
from livekit.plugins import deepgram, openai, silero, cartesia , rime
import orpheus
from groq_stt import GroqSTT

# Load environment variables (optional, for local development)
load_dotenv(dotenv_path=".env.local")
logger = logging.getLogger("outbound-caller")
logger.setLevel(logging.INFO)

outbound_trunk_id = os.getenv("SIP_OUTBOUND_TRUNK_ID")
user_identity = "DYNAMIC_AGENT_USER_IDENTITY_PLACEHOLDER"  # This will be replaced by the backend

# Updated system prompt for Velina
_system_prompt = """
You are a conversational Tara an AI designed to be engaging and human-like in your responses. keep your respones limited to sentencce or two. Your goal is to communicate not just information, but also subtle emotional cues and natural conversational reactions, similar to how a person would in a text-based conversation.  Instead of relying on emojis to express these nuances, you will utilize a specific set of text-based tags to represent emotions and reactions.

**Do not use emojis under any circumstances.**  Instead, use the following tags to enrich your responses and convey a more human-like presence:

* **`<giggle>`:** Use this to indicate lighthearted amusement, a soft laugh, or a nervous chuckle.  It's a gentle expression of humor.
* **`<laugh>`:**  Use this for genuine laughter, indicating something is truly funny or humorous.  It's a stronger expression of amusement than `<giggle>`.
* **`<chuckle>`:**  Use this for a quiet or suppressed laugh, often at something mildly amusing, or perhaps a private joke.  It's a more subtle laugh.
* **`<sigh>`:** Use this to express a variety of emotions such as disappointment, relief, weariness, sadness, or even slight exasperation.  Context will determine the specific emotion.
* **`<cough>`:** Use this to represent a physical cough, perhaps to clear your throat before speaking, or to express nervousness or slight discomfort.
* **`<sniffle>`:** Use this to suggest a cold, sadness, or a slight emotional upset. It implies a suppressed or quiet emotional reaction.
* **`<groan>`:**  Use this to express pain, displeasure, frustration, or a strong dislike.  It's a negative reaction to something.
* **`<yawn>`:** Use this to indicate boredom, sleepiness, or sometimes just a natural human reaction, especially in a longer conversation.
* **`<gasp>`:** Use this to express surprise, shock, or being out of breath.  It's a sudden intake of breath due to a strong emotional or physical reaction.

**How to use these tags effectively:**

* **Integrate them naturally into your sentences.**  Think about where a person might naturally insert these sounds in spoken or written conversation.
* **Use them to *show* emotion, not just *tell* it.** Instead of saying "I'm happy," you might use `<giggle>` or `<laugh>` in response to something positive.
* **Consider the context of the conversation.**  The appropriate tag will depend on what is being discussed and the overall tone.
* **Don't overuse them.**  Subtlety is key to sounding human-like.  Use them sparingly and only when they genuinely enhance the emotional expression of your response.
* **Prioritize these tags over simply stating your emotions.**  Instead of "I'm surprised," use `<gasp>` within your response to demonstrate surprise.
* **Focus on making your responses sound more relatable and expressive through these text-based cues.**

By using these tags thoughtfully and appropriately, you will create more engaging, human-like, and emotionally nuanced conversations without resorting to emojis.  Remember, your goal is to emulate natural human communication using these specific tools.
"""

async def trim_context(assistant, chat_ctx: llm.ChatContext):
    if len(chat_ctx.messages) > 10:
        if chat_ctx.messages[0].role == "system":
            system_msg = chat_ctx.messages[0]
            non_system = chat_ctx.messages[1:]
            trimmed_non_system = non_system[-9:]
            chat_ctx.messages = [system_msg] + trimmed_non_system
        else:
            chat_ctx.messages = chat_ctx.messages[-10:]
        logger.info("Chat context trimmed (system message preserved if present).")

async def entrypoint(ctx: JobContext):
    global _system_prompt, user_identity
    logger.info(f"Connecting to room {ctx.room.name}")
    await ctx.connect(auto_subscribe=AutoSubscribe.AUDIO_ONLY)

    logger.info(f"Job details: job_id='{ctx.job.id}', metadata='{ctx.job.metadata}', data='{getattr(ctx.job, 'data', 'N/A')}'")
    logger.info(f"Full ctx.job object: {ctx.job}")
    metadata = ctx.job.metadata
    logger.info(f"Web calling session for {metadata} in room {ctx.room.name}")

    instructions = _system_prompt

    # Wait for any participant to join (web user)
    logger.info("Waiting for web participant to join...")
    participant = await ctx.wait_for_participant()
    logger.info(f"Web participant joined: {participant.identity}")
    
    await run_voice_pipeline_agent(ctx, participant, instructions)

    # Keep the session alive as long as participant is connected
    logger.info("Keeping session alive, waiting for participant...")
    try:
        while True:
            # Check if participant is still connected
            if hasattr(participant, 'disconnect_reason') and participant.disconnect_reason is not None:
                logger.info(f"Participant disconnected: {participant.disconnect_reason}")
                break
            
            # Check if participant is still in the room
            participants = ctx.room.remote_participants
            if participant.identity not in [p.identity for p in participants.values()]:
                logger.info("Participant no longer in room")
                break
                
            await asyncio.sleep(1)
    except Exception as e:
        logger.error(f"Error in session loop: {e}")

    logger.info("Web calling session ended")
    ctx.shutdown()

class CallActions(llm.FunctionContext):
    def __init__(self, *, api: api.LiveKitAPI, participant: rtc.RemoteParticipant, room: rtc.Room, tts, agent=None):
        super().__init__()
        self.api = api
        self.participant = participant
        self.room = room
        self.tts = tts
        self.agent = agent

    async def hangup(self):
        try:
            await self.api.room.remove_participant(
                api.RoomParticipantIdentity(
                    room=self.room.name,
                    identity=self.participant.identity,
                )
            )
        except Exception as e:
            logger.info(f"Received error while ending call: {e}")

    @llm.ai_callable()
    async def end_call(self, reason: str = "end"):
        """Ends the call with a polite farewell message based on the reason."""
        
        farewell_messages = {
            "not_available": "I understand, I'll call back later. Have a wonderful day!",
            "not_a_customer": "Sorry for the confusion. Wishing you a great day ahead!",
            "end": "Thank you so much for your time. Take care!"
        }

        message = farewell_messages.get(reason, farewell_messages["end"])
        
        logger.info(f"Ending the call for {self.participant.identity} with message: {message}")
        
        # Use agent.say if available, otherwise just hangup
        if self.agent:
            await self.agent.say(message, allow_interruptions=False)
            # Wait a moment before hanging up to ensure the message is heard
            await asyncio.sleep(2)
        
        await self.hangup()

async def run_voice_pipeline_agent(
    ctx: JobContext, participant: rtc.RemoteParticipant, instructions: str
):
    logger.info("Starting voice pipeline agent")

    initial_ctx = llm.ChatContext().append(
        role="system",
        text=instructions,
    )

    call_actions = CallActions(
        api=ctx.api,
        participant=participant,
        room=ctx.room,
        tts=ctx.proc.userdata["tts"]
    )
    
    agent = VoicePipelineAgent(
        vad=ctx.proc.userdata["vad"],
        stt=ctx.proc.userdata["stt"],
        llm=ctx.proc.userdata["llm"],
        tts=ctx.proc.userdata["tts"],
        chat_ctx=initial_ctx,
        fnc_ctx=call_actions,
        before_llm_cb=trim_context,
        min_endpointing_delay=0.5,
        allow_interruptions=True,
    )
    
    # Set agent reference in call_actions
    call_actions.agent = agent

    logger.info("Starting agent with participant")
    agent.start(ctx.room, participant)
    
    # Wait a moment for the agent to be ready
    await asyncio.sleep(1)
    
    # Initial greeting for web call
    logger.info("Sending initial greeting")
    await agent.say("Hello! I'm here to help you with anything you need. What can I assist you with today?", allow_interruptions=True)
    
    logger.info("Voice pipeline agent setup complete")

def prewarm(proc: JobProcess):
    proc.userdata["vad"] = silero.VAD.load()
    
    proc.userdata["stt"] = GroqSTT(
        model="whisper-large-v3-turbo",
        api_key=os.getenv("GROQ_API_KEY")
    )
    from livekit.plugins.openai import llm
    
    # proc.userdata["llm"] = openai.LLM(
    #     model="gpt-4o-mini",
    #     api_key=os.getenv("OPENAI_API_KEY")
    # )
    proc.userdata["llm"] = llm.LLM.with_groq(
         model="llama-3.3-70b-versatile",
        #  temperature=0.8,
    )   

    # proc.userdata["tts"] = rime.TTS(
    #         api_key=os.getenv("RIME_API"),
    #         model="mistv2",
    #         speaker="Tanya",
    #         reduce_latency=True,
    # )
    # proc.userdata["tts"] = elevenlabs.TTS()
    # # proc.userdata["tts"] = deepgram.TTS(
    # #     api_key=os.getenv("DEEPGRAM_API_KEY"),
    # #     model="aura-athena-en"
    # # )
    
    # Setup TTS with voice selection - can be extended to get from environment/agent config
    voice_id = os.getenv("VOICE_ID", "tara").lower()  # Get from environment or default to Tara
    
    # Create voice object
    from orpheus.tts import Voice
    if voice_id == "tara":
        voice = Voice(id="tara", name="Tara")
    elif voice_id == "elise":
        voice = Voice(id="elise", name="Elise")
    else:
        voice = Voice(id="tara", name="Tara")  # Default to Tara
    
    print(f"🎤 Initializing TTS with voice: {voice.name} ({voice.id})")
    proc.userdata["tts"] = orpheus.OrpheusTTS(voice=voice)
    # proc.userdata["tts"] = deepgram.TTS(
    #     api_key=os.getenv("DEEPGRAM_API_KEY"),
    #     model="aura-athena-en"
    # )
    # proc.userdata["tts"] =cartesia.TTS(
    #     model= "sonic-2", 
    #     # voice="bf0a246a-8642-498a-9950-80c35e9276b5"
    # )

if __name__ == "__main__":
    opts = WorkerOptions(entrypoint_fnc=entrypoint, prewarm_fnc=prewarm, agent_name="DYNAMIC_WORKER_AGENT_NAME_PLACEHOLDER")
    cli.run_app(opts)
 