"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { 
  Phone, CheckCircle, XCircle, Clock, Pause, Play, RefreshCw, Users, 
  BarChart3, AlertTriangle, DollarSign, Timer, Recycle, RotateCcw,
  TrendingUp, Target, PhoneCall, Activity, Zap
} from "lucide-react"

interface EnhancedCampaignMonitorProps {
  campaignId: string
  onClose?: () => void
}

interface ButtonState {
  enabled: boolean
  tooltip: string
}

interface EnhancedStatistics {
  data: {
    summary: {
      total_calls: number
      answered_calls: number
      success_rate: number
      total_cost: number
      total_duration_minutes: number
      calls_remaining: number
    }
    progress: {
      progress_percentage: number
      contacts_processed: number
      total_contacts: number
    }
    call_analysis: {
      calls_by_status: {
        answered: number
        completed: number
        failed: number
        no_answer: number
        busy: number
        in_progress: number
      }
      latest_calls: Array<{
        contact_name: string
        contact_phone: string
        status: string
        timestamp: string
        duration?: number
        cost?: number
      }>
    }
    button_states: {
      start: ButtonState
      pause: ButtonState
      restart: ButtonState
      recycle: ButtonState
    }
    recyclable_contacts: {
      count: number
      contacts: Array<{
        name: string
        phone: string
        last_status: string
      }>
    }
  }
  success: boolean
  message?: string
}

interface CampaignBasicInfo {
  id: string
  name: string
  status: string
  started_at?: string
  completed_at?: string
  agent_name?: string
  total_contacts: number
}

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:9090'

export function EnhancedCampaignMonitor({ campaignId, onClose }: EnhancedCampaignMonitorProps) {
  const [enhancedStats, setEnhancedStats] = useState<EnhancedStatistics | null>(null)
  const [campaignInfo, setCampaignInfo] = useState<CampaignBasicInfo | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null)
  const [actionLoading, setActionLoading] = useState<string | null>(null)

  const fetchEnhancedData = async () => {
    try {
      // Fetch enhanced statistics
      const statsResponse = await fetch(`${API_URL}/api/campaigns/${campaignId}/statistics/enhanced`)
      if (!statsResponse.ok) {
        throw new Error(`Stats API failed: ${statsResponse.status}`)
      }
      const statsData = await statsResponse.json()
      setEnhancedStats(statsData)

      // Fetch basic campaign info
      const campaignsResponse = await fetch(`${API_URL}/api/campaigns`)
      if (campaignsResponse.ok) {
        const campaignsData = await campaignsResponse.json()
        const campaign = Object.values(campaignsData).find((c: any) => 
          c.id === campaignId || c.campaign_id === campaignId
        ) as CampaignBasicInfo
        if (campaign) {
          setCampaignInfo(campaign)
        }
      }

      setError(null)
    } catch (err) {
      console.error('Error fetching enhanced data:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  const performAction = async (action: string) => {
    setActionLoading(action)
    try {
      const response = await fetch(`${API_URL}/api/campaigns/${campaignId}/${action}`, {
        method: 'POST'
      })
      if (response.ok) {
        await fetchEnhancedData() // Refresh data
      } else {
        throw new Error(`Action ${action} failed: ${response.statusText}`)
      }
    } catch (err) {
      console.error(`Error performing ${action}:`, err)
      setError(`Failed to ${action}: ${err instanceof Error ? err.message : 'Unknown error'}`)
    } finally {
      setActionLoading(null)
    }
  }

  useEffect(() => {
    fetchEnhancedData()
    
    // Set up auto-refresh every 15 seconds
    const interval = setInterval(() => {
      if (campaignInfo?.status === 'running') {
        fetchEnhancedData()
      }
    }, 15000)
    
    setRefreshInterval(interval)
    
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [campaignId])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-green-500'
      case 'completed': return 'bg-blue-500'
      case 'paused': return 'bg-yellow-500'
      case 'failed': return 'bg-red-500'
      default: return 'bg-gray-500'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <Play className="h-4 w-4" />
      case 'completed': return <CheckCircle className="h-4 w-4" />
      case 'paused': return <Pause className="h-4 w-4" />
      case 'failed': return <XCircle className="h-4 w-4" />
      default: return <Clock className="h-4 w-4" />
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 4
    }).format(amount)
  }

  const formatTime = (isoString?: string) => {
    if (!isoString) return 'N/A'
    return new Date(isoString).toLocaleTimeString()
  }

  const getCallStatusColor = (status: string) => {
    switch (status) {
      case 'answered':
      case 'completed': return 'bg-green-100 text-green-800 border-green-200'
      case 'failed': return 'bg-red-100 text-red-800 border-red-200'
      case 'no_answer': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'busy': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'in_progress': return 'bg-blue-100 text-blue-800 border-blue-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  if (loading) {
    return (
      <Card className="w-full max-w-6xl">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className="h-5 w-5 animate-spin" />
            Loading Enhanced Campaign Monitor...
          </CardTitle>
        </CardHeader>
      </Card>
    )
  }

  if (error || !enhancedStats || !enhancedStats.data) {
    return (
      <Card className="w-full max-w-6xl">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <XCircle className="h-5 w-5" />
            Error Loading Enhanced Campaign Data
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-600 mb-4">{error || 'Enhanced statistics not available'}</p>
          <Button onClick={onClose} variant="outline">Close</Button>
        </CardContent>
      </Card>
    )
  }

  const { data } = enhancedStats
  const status = campaignInfo?.status || 'unknown'

  return (
    <div className="w-full max-w-7xl space-y-6">
      {/* Enhanced Header with Action Buttons */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Badge className={`${getStatusColor(status)} text-white`}>
                {getStatusIcon(status)}
                <span className="ml-1 capitalize">{status}</span>
              </Badge>
              <div>
                <CardTitle className="text-xl">{campaignInfo?.name || 'Unknown Campaign'}</CardTitle>
                <p className="text-sm text-gray-600">Campaign ID: {campaignId}</p>
                {campaignInfo?.agent_name && (
                  <p className="text-sm text-gray-600">Agent: {campaignInfo.agent_name}</p>
                )}
              </div>
            </div>
            
            {/* Enhanced Action Buttons */}
            <div className="flex gap-2">
              {Object.entries(data.button_states).map(([action, state]) => {
                const isLoading = actionLoading === action
                const icons = {
                  start: <Play className="h-4 w-4" />,
                  pause: <Pause className="h-4 w-4" />,
                  restart: <RotateCcw className="h-4 w-4" />,
                  recycle: <Recycle className="h-4 w-4" />
                }
                
                return (
                  <Button
                    key={action}
                    onClick={() => performAction(action)}
                    disabled={!state.enabled || isLoading}
                    variant={action === 'start' ? 'default' : 'outline'}
                    size="sm"
                    title={state.tooltip}
                  >
                    {isLoading ? (
                      <RefreshCw className="h-4 w-4 animate-spin mr-1" />
                    ) : (
                      <span className="mr-1">{icons[action as keyof typeof icons]}</span>
                    )}
                    {action.charAt(0).toUpperCase() + action.slice(1)}
                  </Button>
                )
              })}
              
              <Button onClick={fetchEnhancedData} variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-1" />
                Refresh
              </Button>
              
              {onClose && (
                <Button onClick={onClose} variant="outline" size="sm">
                  Close
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Enhanced Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium">Progress</span>
            </div>
            <p className="text-2xl font-bold text-blue-700">{data.progress.progress_percentage.toFixed(1)}%</p>
            <p className="text-sm text-gray-600">{data.progress.contacts_processed}/{data.progress.total_contacts} contacts</p>
          </CardContent>
        </Card>

        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <Target className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium">Success Rate</span>
            </div>
            <p className="text-2xl font-bold text-green-700">{data.summary.success_rate.toFixed(1)}%</p>
            <p className="text-sm text-gray-600">{data.summary.answered_calls} answered</p>
          </CardContent>
        </Card>

        <Card className="border-purple-200 bg-purple-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <DollarSign className="h-4 w-4 text-purple-600" />
              <span className="text-sm font-medium">Total Cost</span>
            </div>
            <p className="text-2xl font-bold text-purple-700">{formatCurrency(data.summary.total_cost)}</p>
            <p className="text-sm text-gray-600">Live tracking</p>
          </CardContent>
        </Card>

        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <Timer className="h-4 w-4 text-orange-600" />
              <span className="text-sm font-medium">Duration</span>
            </div>
            <p className="text-2xl font-bold text-orange-700">{data.summary.total_duration_minutes.toFixed(1)}m</p>
            <p className="text-sm text-gray-600">Active time</p>
          </CardContent>
        </Card>

        <Card className="border-gray-200 bg-gray-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <PhoneCall className="h-4 w-4 text-gray-600" />
              <span className="text-sm font-medium">Remaining</span>
            </div>
            <p className="text-2xl font-bold text-gray-700">{data.summary.calls_remaining}</p>
            <p className="text-sm text-gray-600">calls left</p>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Progress Bar */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Campaign Progress & Status Breakdown
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <div className="flex justify-between text-sm mb-2">
              <span>Progress: {data.progress.contacts_processed}/{data.progress.total_contacts} contacts</span>
              <span className="font-medium">{data.progress.progress_percentage.toFixed(1)}% Complete</span>
            </div>
            <Progress value={data.progress.progress_percentage} className="h-3" />
          </div>

          {/* Call Status Breakdown */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3 mt-4">
            {Object.entries(data.call_analysis.calls_by_status).map(([status, count]) => (
              <div key={status} className="text-center p-3 bg-gray-50 rounded-lg">
                <p className="text-lg font-bold">{count}</p>
                <p className="text-xs text-gray-600 capitalize">{status.replace('_', ' ')}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Tabs */}
      <Tabs defaultValue="calls" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="calls">Recent Calls</TabsTrigger>
          <TabsTrigger value="recycle">
            <Recycle className="h-4 w-4 mr-1" />
            Recyclable ({data.recyclable_contacts.count})
          </TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="calls" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Call Activity</CardTitle>
            </CardHeader>
            <CardContent>
              {data.call_analysis.latest_calls.length === 0 ? (
                <p className="text-gray-600 text-center py-4">No recent calls</p>
              ) : (
                <div className="space-y-2">
                  {data.call_analysis.latest_calls.map((call, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <Badge className={`${getCallStatusColor(call.status)} border`}>
                          {call.status.replace('_', ' ')}
                        </Badge>
                        <div>
                          <p className="font-medium">{call.contact_name}</p>
                          <p className="text-sm text-gray-600">{call.contact_phone}</p>
                        </div>
                      </div>
                      <div className="text-right text-sm">
                        <p className="text-gray-600">{formatTime(call.timestamp)}</p>
                        {call.duration && <p className="text-xs text-gray-500">{call.duration}s</p>}
                        {call.cost && <p className="text-xs text-green-600">{formatCurrency(call.cost)}</p>}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recycle" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Recycle className="h-5 w-5 text-green-500" />
                Recyclable Contacts ({data.recyclable_contacts.count})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {data.recyclable_contacts.count === 0 ? (
                <div className="text-center py-4">
                  <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-2" />
                  <p className="text-green-600 font-medium">No failed calls to recycle</p>
                  <p className="text-sm text-gray-600">All contacts have been successfully processed</p>
                </div>
              ) : (
                <div className="space-y-4">
                  <p className="text-sm text-gray-600 mb-4">
                    These contacts had failed or no-answer calls and can be retried:
                  </p>
                  <div className="space-y-2">
                    {data.recyclable_contacts.contacts.map((contact, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div>
                          <p className="font-medium">{contact.name}</p>
                          <p className="text-sm text-gray-600">{contact.phone}</p>
                        </div>
                        <Badge variant="outline" className="text-yellow-700 border-yellow-300">
                          {contact.last_status}
                        </Badge>
                      </div>
                    ))}
                  </div>
                  {data.button_states.recycle.enabled && (
                    <Button 
                      onClick={() => performAction('recycle')} 
                      className="w-full mt-4"
                      disabled={actionLoading === 'recycle'}
                    >
                      <Recycle className="h-4 w-4 mr-2" />
                      Recycle All Failed Contacts
                    </Button>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Enhanced Analytics
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <h4 className="font-medium">Performance Metrics</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Total Calls:</span>
                    <span className="font-medium">{data.summary.total_calls}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Success Rate:</span>
                    <span className="font-medium text-green-600">{data.summary.success_rate.toFixed(1)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Cost per Call:</span>
                    <span className="font-medium">{formatCurrency(data.summary.total_cost / Math.max(data.summary.total_calls, 1))}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Avg Duration:</span>
                    <span className="font-medium">{(data.summary.total_duration_minutes / Math.max(data.summary.total_calls, 1)).toFixed(1)}m</span>
                  </div>
                </div>
              </div>
              
              <div className="space-y-3">
                <h4 className="font-medium">Status Distribution</h4>
                <div className="space-y-2 text-sm">
                  {Object.entries(data.call_analysis.calls_by_status).map(([status, count]) => (
                    <div key={status} className="flex justify-between">
                      <span className="capitalize">{status.replace('_', ' ')}:</span>
                      <span className="font-medium">{count}</span>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Campaign Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">Campaign Details</h4>
                  <div className="space-y-1 text-sm">
                    <p><span className="text-gray-600">ID:</span> {campaignId}</p>
                    <p><span className="text-gray-600">Status:</span> {status}</p>
                    <p><span className="text-gray-600">Total Contacts:</span> {data.progress.total_contacts}</p>
                    <p><span className="text-gray-600">Started:</span> {formatTime(campaignInfo?.started_at)}</p>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-2">Action States</h4>
                  <div className="space-y-1 text-sm">
                    {Object.entries(data.button_states).map(([action, state]) => (
                      <div key={action} className="flex items-center gap-2">
                        <Badge variant={state.enabled ? "default" : "secondary"}>
                          {action}
                        </Badge>
                        <span className="text-xs text-gray-600">{state.tooltip}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 