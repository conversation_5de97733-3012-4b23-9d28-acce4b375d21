#!/usr/bin/env python3
"""
Test the fully automated SIP trunk integration in simple_backend.py

This demonstrates how the system automatically:
1. Fetches numbers from Telnyx
2. Creates SIP trunks for new numbers
3. Updates the mapping automatically
4. Cleans up removed numbers
"""

import requests
import json
import time

BACKEND_URL = "http://localhost:9090"

def test_auto_integration():
    """Test the automatic SIP trunk integration"""
    print("🎯 Testing Fully Automated SIP Trunk Integration")
    print("=" * 60)
    print("This tests the integration built into simple_backend.py")
    print("=" * 60)
    
    try:
        print("\n🚀 Step 1: Fetching Telnyx numbers (triggers auto-creation)")
        response = requests.get(f"{BACKEND_URL}/api/telnyx/numbers", timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ Successfully fetched {data.get('count', 0)} numbers")
            
            # Show auto-created trunks
            created_trunks = data.get('auto_created_trunks', [])
            if created_trunks:
                print(f"\n🎉 Auto-created {len(created_trunks)} SIP trunks:")
                for trunk in created_trunks:
                    status_icon = "✅" if trunk['status'] == 'created' else "❌"
                    print(f"   {status_icon} {trunk['phone_number']} → {trunk.get('sip_trunk_id', 'FAILED')}")
            else:
                print("\n📋 No new SIP trunks needed (all numbers already have trunks)")
            
            # Show removed numbers
            removed_numbers = data.get('removed_numbers', [])
            if removed_numbers:
                print(f"\n🧹 Cleaned up {len(removed_numbers)} removed numbers:")
                for removed in removed_numbers:
                    print(f"   🗑️ {removed['phone_number']} (was {removed['sip_trunk_id']})")
            else:
                print("\n📋 No numbers were removed")
            
            # Show current mapping
            print(f"\n📞 Current phone numbers with SIP trunks:")
            for number in data.get('numbers', []):
                trunk_icon = "🎯" if number.get('has_custom_trunk') else "🔧"
                print(f"   {trunk_icon} {number['phone_number']} → {number.get('sip_trunk_id')}")
            
        else:
            print(f"❌ Failed to fetch numbers: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    print(f"\n🎯 Testing SIP trunk mapping endpoint...")
    try:
        response = requests.get(f"{BACKEND_URL}/api/sip-trunks", timeout=10)
        if response.status_code == 200:
            mapping = response.json()
            print(f"✅ Current SIP trunk mapping:")
            print(json.dumps(mapping.get('mapping', {}), indent=2))
        else:
            print(f"❌ Failed to get mapping: {response.status_code}")
    except Exception as e:
        print(f"❌ Error getting mapping: {str(e)}")
    
    print(f"\n🚀 Summary: Automatic SIP Trunk Management")
    print("=" * 50)
    print("✅ The simple_backend.py now automatically:")
    print("   • Fetches numbers from Telnyx when /api/telnyx/numbers is called")
    print("   • Creates SIP trunks for any new numbers found")
    print("   • Updates data/sip_trunks.json with new mappings")
    print("   • Removes mappings for numbers no longer in Telnyx")
    print("   • Uses your real Telnyx credentials (itscb.sip.telnyx.com)")
    print("\n🎉 Your multi-number calling system is fully automated!")
    print("   Just run campaigns and each call will use the correct SIP trunk!")

if __name__ == "__main__":
    test_auto_integration() 