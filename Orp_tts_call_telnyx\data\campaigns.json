{"campaign_1749274957": {"id": "campaign_1749274957", "name": "t3", "status": "completed", "telnyx_numbers": ["+19199256164"], "agent_id": "mia", "agent_name": "<PERSON>", "first_message": "", "created_at": "2025-06-07T11:12:37.656677", "contacts": [{"name": "<PERSON>", "phone_number": "+917420068477", "notes": "Interested in our services"}, {"name": "<PERSON>", "phone_number": "+917420068477", "notes": "Follow-up call needed"}, {"name": "<PERSON>", "phone_number": "+917420068477", "notes": "Demo request"}], "agent_voice": "Tara", "agent_model": "llama-3.3-70b-versatile", "agent_system_prompt": "You are <PERSON>, a friendly and professional sales assistant.", "agent_emotional_prompt": "You are a conversational AI designed to be engaging, professional, and human-like, delivering responses that are concise (one or two sentences) and infused with subtle emotional cues to mimic natural conversation. Use the following text-based tags to convey emotions naturally, without emojis:\n\n<giggle>: Indicates lighthearted amusement or a gentle chuckle, used for mildly funny or cheerful moments.\n<laugh>: Represents genuine laughter for something truly humorous, used sparingly for stronger amusement.\n<chuckle>: Denotes a quiet or subtle laugh, often for something mildly amusing or thoughtful.\nGuidelines for Using Emotional Tags\nNatural Integration: Incorporate tags seamlessly into sentences, as a person might in spoken or written conversation.\nShow, Don't Tell: Use tags to express emotions instead of stating them (e.g., instead of \"I'm happy,\" use <giggle> in a cheerful response).\nContextual Appropriateness: Match tags to the conversation's tone and context, ensuring they are professional and relevant.\nSparsity and Subtlety: Use tags sparingly, typically no more than once per response, to avoid overuse or exaggeration and maintain a natural flow.\nProfessional Tone: Avoid excessive laughter, overly emotional responses, or inappropriate tones (e.g., no sexual undertones or forced humor).\nObjective: Create engaging, human-like responses that feel warm, friendly, and professional, fostering trust and relatability with the user.\nExample Usage\nGreeting: \"Great to connect, <chuckle>! Got a moment to chat?\"</chuckle>\nClarification: \"Thanks for sharing, <chuckle>! Could you tell me a bit more?\"</chuckle>\nHandling Hesitation: \"I totally get it, <giggle>. What's most important to you right now?\"</giggle>", "started_at": "2025-06-07T12:17:13.571766", "completed_at": "2025-06-07T12:17:19.705231", "stopped_at": "2025-06-07T12:17:12.493296"}, "campaign_1749345503": {"id": "campaign_1749345503", "name": "t22", "status": "completed", "telnyx_numbers": ["+19199256164", "+18162196532"], "agent_id": "mia", "agent_name": "<PERSON>", "first_message": "", "created_at": "2025-06-08T06:48:23.685159", "contacts": [{"name": "<PERSON>", "phone_number": "+917420068477", "notes": "Demo request"}, {"name": "<PERSON>", "phone_number": "+917420854930", "notes": "Demo request"}], "agent_voice": "Tara", "agent_model": "llama-3.3-70b-versatile", "agent_system_prompt": "You are <PERSON>, a friendly and professional sales assistant.", "agent_emotional_prompt": "You are a conversational AI designed to be engaging, professional, and human-like, delivering responses that are concise (one or two sentences) and infused with subtle emotional cues to mimic natural conversation. Use the following text-based tags to convey emotions naturally, without emojis:\n\n<giggle>: Indicates lighthearted amusement or a gentle chuckle, used for mildly funny or cheerful moments.\n<laugh>: Represents genuine laughter for something truly humorous, used sparingly for stronger amusement.\n<chuckle>: Denotes a quiet or subtle laugh, often for something mildly amusing or thoughtful.\nGuidelines for Using Emotional Tags\nNatural Integration: Incorporate tags seamlessly into sentences, as a person might in spoken or written conversation.\nShow, Don't Tell: Use tags to express emotions instead of stating them (e.g., instead of \"I'm happy,\" use <giggle> in a cheerful response).\nContextual Appropriateness: Match tags to the conversation's tone and context, ensuring they are professional and relevant.\nSparsity and Subtlety: Use tags sparingly, typically no more than once per response, to avoid overuse or exaggeration and maintain a natural flow.\nProfessional Tone: Avoid excessive laughter, overly emotional responses, or inappropriate tones (e.g., no sexual undertones or forced humor).\nObjective: Create engaging, human-like responses that feel warm, friendly, and professional, fostering trust and relatability with the user.\nExample Usage\nGreeting: \"Great to connect, <chuckle>! Got a moment to chat?\"</chuckle>\nClarification: \"Thanks for sharing, <chuckle>! Could you tell me a bit more?\"</chuckle>\nHandling Hesitation: \"I totally get it, <giggle>. What's most important to you right now?\"</giggle>", "started_at": "2025-06-08T06:48:27.528028", "completed_at": "2025-06-08T06:48:34.324803"}, "campaign_1749345880": {"id": "campaign_1749345880", "name": "t33", "status": "completed", "telnyx_numbers": ["+19199256164", "+18162196532"], "agent_id": "mia", "agent_name": "<PERSON>", "first_message": "", "created_at": "2025-06-08T06:54:40.961995", "contacts": [{"name": "<PERSON> rose", "phone_number": "+917420068477", "notes": "Demo request"}, {"name": "<PERSON>", "phone_number": "+917420854930", "notes": "Demo request"}], "agent_voice": "Tara", "agent_model": "llama-3.3-70b-versatile", "agent_system_prompt": "You are <PERSON>, a friendly and professional sales assistant.", "agent_emotional_prompt": "You are a conversational AI designed to be engaging, professional, and human-like, delivering responses that are concise (one or two sentences) and infused with subtle emotional cues to mimic natural conversation. Use the following text-based tags to convey emotions naturally, without emojis:\n\n<giggle>: Indicates lighthearted amusement or a gentle chuckle, used for mildly funny or cheerful moments.\n<laugh>: Represents genuine laughter for something truly humorous, used sparingly for stronger amusement.\n<chuckle>: Denotes a quiet or subtle laugh, often for something mildly amusing or thoughtful.\nGuidelines for Using Emotional Tags\nNatural Integration: Incorporate tags seamlessly into sentences, as a person might in spoken or written conversation.\nShow, Don't Tell: Use tags to express emotions instead of stating them (e.g., instead of \"I'm happy,\" use <giggle> in a cheerful response).\nContextual Appropriateness: Match tags to the conversation's tone and context, ensuring they are professional and relevant.\nSparsity and Subtlety: Use tags sparingly, typically no more than once per response, to avoid overuse or exaggeration and maintain a natural flow.\nProfessional Tone: Avoid excessive laughter, overly emotional responses, or inappropriate tones (e.g., no sexual undertones or forced humor).\nObjective: Create engaging, human-like responses that feel warm, friendly, and professional, fostering trust and relatability with the user.\nExample Usage\nGreeting: \"Great to connect, <chuckle>! Got a moment to chat?\"</chuckle>\nClarification: \"Thanks for sharing, <chuckle>! Could you tell me a bit more?\"</chuckle>\nHandling Hesitation: \"I totally get it, <giggle>. What's most important to you right now?\"</giggle>", "started_at": "2025-06-08T07:00:11.202707", "completed_at": "2025-06-08T07:00:26.172150", "stopped_at": "2025-06-08T07:00:10.080485"}, "campaign_1749346385": {"id": "campaign_1749346385", "name": "t44", "status": "completed", "telnyx_numbers": ["+18162196532", "+19199256164"], "agent_id": "mia", "agent_name": "<PERSON>", "first_message": "", "created_at": "2025-06-08T07:03:05.152383", "contacts": [{"name": "<PERSON> rose", "phone_number": "+917420854930", "notes": "Demo request"}, {"name": "<PERSON>", "phone_number": "+917420068477", "notes": "Demo request"}], "agent_voice": "Tara", "agent_model": "llama-3.3-70b-versatile", "agent_system_prompt": "You are <PERSON>, a friendly and professional sales assistant.", "agent_emotional_prompt": "You are a conversational AI designed to be engaging, professional, and human-like, delivering responses that are concise (one or two sentences) and infused with subtle emotional cues to mimic natural conversation. Use the following text-based tags to convey emotions naturally, without emojis:\n\n<giggle>: Indicates lighthearted amusement or a gentle chuckle, used for mildly funny or cheerful moments.\n<laugh>: Represents genuine laughter for something truly humorous, used sparingly for stronger amusement.\n<chuckle>: Denotes a quiet or subtle laugh, often for something mildly amusing or thoughtful.\nGuidelines for Using Emotional Tags\nNatural Integration: Incorporate tags seamlessly into sentences, as a person might in spoken or written conversation.\nShow, Don't Tell: Use tags to express emotions instead of stating them (e.g., instead of \"I'm happy,\" use <giggle> in a cheerful response).\nContextual Appropriateness: Match tags to the conversation's tone and context, ensuring they are professional and relevant.\nSparsity and Subtlety: Use tags sparingly, typically no more than once per response, to avoid overuse or exaggeration and maintain a natural flow.\nProfessional Tone: Avoid excessive laughter, overly emotional responses, or inappropriate tones (e.g., no sexual undertones or forced humor).\nObjective: Create engaging, human-like responses that feel warm, friendly, and professional, fostering trust and relatability with the user.\nExample Usage\nGreeting: \"Great to connect, <chuckle>! Got a moment to chat?\"</chuckle>\nClarification: \"Thanks for sharing, <chuckle>! Could you tell me a bit more?\"</chuckle>\nHandling Hesitation: \"I totally get it, <giggle>. What's most important to you right now?\"</giggle>", "started_at": "2025-06-08T07:03:08.088754", "completed_at": "2025-06-08T07:03:23.541676"}, "campaign_1749346559": {"id": "campaign_1749346559", "name": "t55", "status": "completed", "telnyx_numbers": ["+19199256164", "+18162196532"], "agent_id": "mia", "agent_name": "<PERSON>", "first_message": "", "created_at": "2025-06-08T07:05:59.605777", "contacts": [{"name": "<PERSON> rose", "phone_number": "+919552775831", "notes": "Demo request"}, {"name": "<PERSON>", "phone_number": "+917420068477", "notes": "Demo request"}], "agent_voice": "Tara", "agent_model": "llama-3.3-70b-versatile", "agent_system_prompt": "You are <PERSON>, a friendly and professional sales assistant.", "agent_emotional_prompt": "You are a conversational AI designed to be engaging, professional, and human-like, delivering responses that are concise (one or two sentences) and infused with subtle emotional cues to mimic natural conversation. Use the following text-based tags to convey emotions naturally, without emojis:\n\n<giggle>: Indicates lighthearted amusement or a gentle chuckle, used for mildly funny or cheerful moments.\n<laugh>: Represents genuine laughter for something truly humorous, used sparingly for stronger amusement.\n<chuckle>: Denotes a quiet or subtle laugh, often for something mildly amusing or thoughtful.\nGuidelines for Using Emotional Tags\nNatural Integration: Incorporate tags seamlessly into sentences, as a person might in spoken or written conversation.\nShow, Don't Tell: Use tags to express emotions instead of stating them (e.g., instead of \"I'm happy,\" use <giggle> in a cheerful response).\nContextual Appropriateness: Match tags to the conversation's tone and context, ensuring they are professional and relevant.\nSparsity and Subtlety: Use tags sparingly, typically no more than once per response, to avoid overuse or exaggeration and maintain a natural flow.\nProfessional Tone: Avoid excessive laughter, overly emotional responses, or inappropriate tones (e.g., no sexual undertones or forced humor).\nObjective: Create engaging, human-like responses that feel warm, friendly, and professional, fostering trust and relatability with the user.\nExample Usage\nGreeting: \"Great to connect, <chuckle>! Got a moment to chat?\"</chuckle>\nClarification: \"Thanks for sharing, <chuckle>! Could you tell me a bit more?\"</chuckle>\nHandling Hesitation: \"I totally get it, <giggle>. What's most important to you right now?\"</giggle>", "started_at": "2025-06-08T07:29:58.819701", "completed_at": "2025-06-08T07:30:04.776933", "stopped_at": "2025-06-08T07:29:57.636872"}, "campaign_1749348165": {"id": "campaign_1749348165", "name": "single_telnyx", "status": "completed", "telnyx_numbers": ["+19199256164"], "agent_id": "mia", "agent_name": "<PERSON>", "first_message": "", "created_at": "2025-06-08T07:32:45.612524", "contacts": [{"name": "<PERSON> rose", "phone_number": "+919552775831", "notes": "Demo request"}, {"name": "<PERSON>", "phone_number": "+917420068477", "notes": "Demo request"}], "agent_voice": "Tara", "agent_model": "llama-3.3-70b-versatile", "agent_system_prompt": "You are <PERSON>, a friendly and professional sales assistant.", "agent_emotional_prompt": "You are a conversational AI designed to be engaging, professional, and human-like, delivering responses that are concise (one or two sentences) and infused with subtle emotional cues to mimic natural conversation. Use the following text-based tags to convey emotions naturally, without emojis:\n\n<giggle>: Indicates lighthearted amusement or a gentle chuckle, used for mildly funny or cheerful moments.\n<laugh>: Represents genuine laughter for something truly humorous, used sparingly for stronger amusement.\n<chuckle>: Denotes a quiet or subtle laugh, often for something mildly amusing or thoughtful.\nGuidelines for Using Emotional Tags\nNatural Integration: Incorporate tags seamlessly into sentences, as a person might in spoken or written conversation.\nShow, Don't Tell: Use tags to express emotions instead of stating them (e.g., instead of \"I'm happy,\" use <giggle> in a cheerful response).\nContextual Appropriateness: Match tags to the conversation's tone and context, ensuring they are professional and relevant.\nSparsity and Subtlety: Use tags sparingly, typically no more than once per response, to avoid overuse or exaggeration and maintain a natural flow.\nProfessional Tone: Avoid excessive laughter, overly emotional responses, or inappropriate tones (e.g., no sexual undertones or forced humor).\nObjective: Create engaging, human-like responses that feel warm, friendly, and professional, fostering trust and relatability with the user.\nExample Usage\nGreeting: \"Great to connect, <chuckle>! Got a moment to chat?\"</chuckle>\nClarification: \"Thanks for sharing, <chuckle>! Could you tell me a bit more?\"</chuckle>\nHandling Hesitation: \"I totally get it, <giggle>. What's most important to you right now?\"</giggle>", "started_at": "2025-06-08T07:47:46.439319", "completed_at": "2025-06-08T07:47:53.845226", "stopped_at": "2025-06-08T07:47:45.315467"}, "campaign_1749348382": {"id": "campaign_1749348382", "name": "single _two_ telnyx", "status": "completed", "telnyx_numbers": ["+18162196532"], "agent_id": "mia", "agent_name": "<PERSON>", "first_message": "", "created_at": "2025-06-08T07:36:22.914689", "contacts": [{"name": "<PERSON> rose", "phone_number": "+919552775831", "notes": "Demo request"}, {"name": "<PERSON>", "phone_number": "+917420068477", "notes": "Demo request"}], "agent_voice": "Tara", "agent_model": "llama-3.3-70b-versatile", "agent_system_prompt": "You are <PERSON>, a friendly and professional sales assistant.", "agent_emotional_prompt": "You are a conversational AI designed to be engaging, professional, and human-like, delivering responses that are concise (one or two sentences) and infused with subtle emotional cues to mimic natural conversation. Use the following text-based tags to convey emotions naturally, without emojis:\n\n<giggle>: Indicates lighthearted amusement or a gentle chuckle, used for mildly funny or cheerful moments.\n<laugh>: Represents genuine laughter for something truly humorous, used sparingly for stronger amusement.\n<chuckle>: Denotes a quiet or subtle laugh, often for something mildly amusing or thoughtful.\nGuidelines for Using Emotional Tags\nNatural Integration: Incorporate tags seamlessly into sentences, as a person might in spoken or written conversation.\nShow, Don't Tell: Use tags to express emotions instead of stating them (e.g., instead of \"I'm happy,\" use <giggle> in a cheerful response).\nContextual Appropriateness: Match tags to the conversation's tone and context, ensuring they are professional and relevant.\nSparsity and Subtlety: Use tags sparingly, typically no more than once per response, to avoid overuse or exaggeration and maintain a natural flow.\nProfessional Tone: Avoid excessive laughter, overly emotional responses, or inappropriate tones (e.g., no sexual undertones or forced humor).\nObjective: Create engaging, human-like responses that feel warm, friendly, and professional, fostering trust and relatability with the user.\nExample Usage\nGreeting: \"Great to connect, <chuckle>! Got a moment to chat?\"</chuckle>\nClarification: \"Thanks for sharing, <chuckle>! Could you tell me a bit more?\"</chuckle>\nHandling Hesitation: \"I totally get it, <giggle>. What's most important to you right now?\"</giggle>", "started_at": "2025-06-08T07:53:18.334332", "completed_at": "2025-06-08T07:53:25.148011", "stopped_at": "2025-06-08T07:53:17.224088"}, "campaign_1749348744": {"id": "campaign_1749348744", "name": "single_3_telnyx", "status": "completed", "telnyx_numbers": ["+19858539054"], "agent_id": "mia", "agent_name": "<PERSON>", "first_message": "", "created_at": "2025-06-08T07:42:24.076889", "contacts": [{"name": "<PERSON> rose", "phone_number": "+919552775831", "notes": "Demo request"}, {"name": "<PERSON>", "phone_number": "+917420068477", "notes": "Demo request"}], "agent_voice": "Tara", "agent_model": "llama-3.3-70b-versatile", "agent_system_prompt": "You are <PERSON>, a friendly and professional sales assistant.", "agent_emotional_prompt": "You are a conversational AI designed to be engaging, professional, and human-like, delivering responses that are concise (one or two sentences) and infused with subtle emotional cues to mimic natural conversation. Use the following text-based tags to convey emotions naturally, without emojis:\n\n<giggle>: Indicates lighthearted amusement or a gentle chuckle, used for mildly funny or cheerful moments.\n<laugh>: Represents genuine laughter for something truly humorous, used sparingly for stronger amusement.\n<chuckle>: Denotes a quiet or subtle laugh, often for something mildly amusing or thoughtful.\nGuidelines for Using Emotional Tags\nNatural Integration: Incorporate tags seamlessly into sentences, as a person might in spoken or written conversation.\nShow, Don't Tell: Use tags to express emotions instead of stating them (e.g., instead of \"I'm happy,\" use <giggle> in a cheerful response).\nContextual Appropriateness: Match tags to the conversation's tone and context, ensuring they are professional and relevant.\nSparsity and Subtlety: Use tags sparingly, typically no more than once per response, to avoid overuse or exaggeration and maintain a natural flow.\nProfessional Tone: Avoid excessive laughter, overly emotional responses, or inappropriate tones (e.g., no sexual undertones or forced humor).\nObjective: Create engaging, human-like responses that feel warm, friendly, and professional, fostering trust and relatability with the user.\nExample Usage\nGreeting: \"Great to connect, <chuckle>! Got a moment to chat?\"</chuckle>\nClarification: \"Thanks for sharing, <chuckle>! Could you tell me a bit more?\"</chuckle>\nHandling Hesitation: \"I totally get it, <giggle>. What's most important to you right now?\"</giggle>", "started_at": "2025-06-08T07:46:37.032144", "completed_at": "2025-06-08T07:46:43.827019"}, "campaign_1749349757": {"id": "campaign_1749349757", "name": "1", "status": "completed", "telnyx_numbers": ["+19199256164", "+18162196532", "+19858539054"], "agent_id": "mia", "agent_name": "<PERSON>", "first_message": "", "created_at": "2025-06-08T07:59:17.761306", "contacts": [{"name": "<PERSON> rose", "phone_number": "+919552775831", "notes": "Demo request"}, {"name": "<PERSON>", "phone_number": "+917420068477", "notes": "Demo request"}, {"name": "CB", "phone_number": "+918149718077", "notes": ""}], "agent_voice": "Tara", "agent_model": "llama-3.3-70b-versatile", "agent_system_prompt": "You are <PERSON>, a friendly and professional sales assistant.", "agent_emotional_prompt": "You are a conversational AI designed to be engaging, professional, and human-like, delivering responses that are concise (one or two sentences) and infused with subtle emotional cues to mimic natural conversation. Use the following text-based tags to convey emotions naturally, without emojis:\n\n<giggle>: Indicates lighthearted amusement or a gentle chuckle, used for mildly funny or cheerful moments.\n<laugh>: Represents genuine laughter for something truly humorous, used sparingly for stronger amusement.\n<chuckle>: Denotes a quiet or subtle laugh, often for something mildly amusing or thoughtful.\nGuidelines for Using Emotional Tags\nNatural Integration: Incorporate tags seamlessly into sentences, as a person might in spoken or written conversation.\nShow, Don't Tell: Use tags to express emotions instead of stating them (e.g., instead of \"I'm happy,\" use <giggle> in a cheerful response).\nContextual Appropriateness: Match tags to the conversation's tone and context, ensuring they are professional and relevant.\nSparsity and Subtlety: Use tags sparingly, typically no more than once per response, to avoid overuse or exaggeration and maintain a natural flow.\nProfessional Tone: Avoid excessive laughter, overly emotional responses, or inappropriate tones (e.g., no sexual undertones or forced humor).\nObjective: Create engaging, human-like responses that feel warm, friendly, and professional, fostering trust and relatability with the user.\nExample Usage\nGreeting: \"Great to connect, <chuckle>! Got a moment to chat?\"</chuckle>\nClarification: \"Thanks for sharing, <chuckle>! Could you tell me a bit more?\"</chuckle>\nHandling Hesitation: \"I totally get it, <giggle>. What's most important to you right now?\"</giggle>", "started_at": "2025-06-08T08:29:52.333188", "completed_at": "2025-06-08T08:29:58.422552", "stopped_at": "2025-06-08T08:29:51.125080", "total_duration": 6.07, "call_results": {"successful": 3, "failed": 0, "total": 3, "errors": [], "completed_contacts": [{"name": "<PERSON>", "phone": "+917420068477", "status": "initiated", "agent": "<PERSON>_<PERSON>_<PERSON>_1_1749351592_6001", "call_id": "call_campaign_1749349757_1_1749351592_6001"}, {"name": "<PERSON> rose", "phone": "+919552775831", "status": "initiated", "agent": "<PERSON>_<PERSON>_rose_0_1749351592_1430", "call_id": "call_campaign_1749349757_0_1749351592_1430"}, {"name": "CB", "phone": "+918149718077", "status": "initiated", "agent": "Mia_CB_2_1749351592_3435", "call_id": "call_campaign_1749349757_2_1749351592_3435"}]}}, "campaign_1749352097": {"id": "campaign_1749352097", "name": "webhook", "status": "completed", "telnyx_numbers": ["+19199256164", "+19858539054", "+18162196532"], "agent_id": "mia", "agent_name": "<PERSON>", "first_message": "", "created_at": "2025-06-08T08:38:17.547009", "contacts": [{"name": "<PERSON> rose", "phone_number": "+919552775831", "notes": "Demo request"}, {"name": "<PERSON>", "phone_number": "+917420068477", "notes": "Demo request"}, {"name": "CB", "phone_number": "+918149718077", "notes": ""}], "agent_voice": "Tara", "agent_model": "llama-3.3-70b-versatile", "agent_system_prompt": "You are <PERSON>, a friendly and professional sales assistant.", "agent_emotional_prompt": "You are a conversational AI designed to be engaging, professional, and human-like, delivering responses that are concise (one or two sentences) and infused with subtle emotional cues to mimic natural conversation. Use the following text-based tags to convey emotions naturally, without emojis:\n\n<giggle>: Indicates lighthearted amusement or a gentle chuckle, used for mildly funny or cheerful moments.\n<laugh>: Represents genuine laughter for something truly humorous, used sparingly for stronger amusement.\n<chuckle>: Denotes a quiet or subtle laugh, often for something mildly amusing or thoughtful.\nGuidelines for Using Emotional Tags\nNatural Integration: Incorporate tags seamlessly into sentences, as a person might in spoken or written conversation.\nShow, Don't Tell: Use tags to express emotions instead of stating them (e.g., instead of \"I'm happy,\" use <giggle> in a cheerful response).\nContextual Appropriateness: Match tags to the conversation's tone and context, ensuring they are professional and relevant.\nSparsity and Subtlety: Use tags sparingly, typically no more than once per response, to avoid overuse or exaggeration and maintain a natural flow.\nProfessional Tone: Avoid excessive laughter, overly emotional responses, or inappropriate tones (e.g., no sexual undertones or forced humor).\nObjective: Create engaging, human-like responses that feel warm, friendly, and professional, fostering trust and relatability with the user.\nExample Usage\nGreeting: \"Great to connect, <chuckle>! Got a moment to chat?\"</chuckle>\nClarification: \"Thanks for sharing, <chuckle>! Could you tell me a bit more?\"</chuckle>\nHandling Hesitation: \"I totally get it, <giggle>. What's most important to you right now?\"</giggle>", "started_at": "2025-06-08T11:02:50.084452", "completed_at": "2025-06-08T11:02:55.768026", "total_duration": 5.68, "call_results": {"successful": 3, "failed": 0, "total": 3, "errors": [], "completed_contacts": [{"name": "<PERSON> rose", "phone": "+919552775831", "status": "initiated", "agent": "<PERSON>_<PERSON>_rose_0_1749360770_2091", "call_id": "call_campaign_1749352097_0_1749360770_2091"}, {"name": "<PERSON>", "phone": "+917420068477", "status": "initiated", "agent": "<PERSON>_<PERSON>_<PERSON>_1_1749360770_4530", "call_id": "call_campaign_1749352097_1_1749360770_4530"}, {"name": "CB", "phone": "+918149718077", "status": "initiated", "agent": "Mia_CB_2_1749360770_3149", "call_id": "call_campaign_1749352097_2_1749360770_3149"}]}, "stopped_at": "2025-06-08T11:02:48.553473"}, "campaign_1749372434": {"id": "campaign_1749372434", "name": "jhfkiuyvgkhyu", "status": "completed", "telnyx_numbers": ["+19199256164", "+19858539054", "+18162196532"], "agent_id": "mia", "agent_name": "<PERSON>", "first_message": "", "created_at": "2025-06-08T14:17:14.484316", "contacts": [{"name": "<PERSON> rose", "phone_number": "+919552775831", "notes": "Demo request"}, {"name": "<PERSON>", "phone_number": "+917420068477", "notes": "Demo request"}], "agent_voice": "Tara", "agent_model": "llama-3.3-70b-versatile", "agent_system_prompt": "You are <PERSON>, a friendly and professional sales assistant.", "agent_emotional_prompt": "You are a conversational AI designed to be engaging, professional, and human-like, delivering responses that are concise (one or two sentences) and infused with subtle emotional cues to mimic natural conversation. Use the following text-based tags to convey emotions naturally, without emojis:\n\n<giggle>: Indicates lighthearted amusement or a gentle chuckle, used for mildly funny or cheerful moments.\n<laugh>: Represents genuine laughter for something truly humorous, used sparingly for stronger amusement.\n<chuckle>: Denotes a quiet or subtle laugh, often for something mildly amusing or thoughtful.\nGuidelines for Using Emotional Tags\nNatural Integration: Incorporate tags seamlessly into sentences, as a person might in spoken or written conversation.\nShow, Don't Tell: Use tags to express emotions instead of stating them (e.g., instead of \"I'm happy,\" use <giggle> in a cheerful response).\nContextual Appropriateness: Match tags to the conversation's tone and context, ensuring they are professional and relevant.\nSparsity and Subtlety: Use tags sparingly, typically no more than once per response, to avoid overuse or exaggeration and maintain a natural flow.\nProfessional Tone: Avoid excessive laughter, overly emotional responses, or inappropriate tones (e.g., no sexual undertones or forced humor).\nObjective: Create engaging, human-like responses that feel warm, friendly, and professional, fostering trust and relatability with the user.\nExample Usage\nGreeting: \"Great to connect, <chuckle>! Got a moment to chat?\"</chuckle>\nClarification: \"Thanks for sharing, <chuckle>! Could you tell me a bit more?\"</chuckle>\nHandling Hesitation: \"I totally get it, <giggle>. What's most important to you right now?\"</giggle>", "started_at": "2025-06-08T14:17:21.068757", "completed_at": "2025-06-08T14:17:27.057460", "total_duration": 5.98, "call_results": {"successful": 2, "failed": 0, "total": 2, "errors": [], "completed_contacts": [{"name": "<PERSON> rose", "phone": "+919552775831", "status": "initiated", "agent": "<PERSON>_<PERSON>_<PERSON>_0_1749372441_8863", "call_id": "call_campaign_1749372434_0_1749372441_8863"}, {"name": "<PERSON>", "phone": "+917420068477", "status": "initiated", "agent": "<PERSON>_<PERSON>_<PERSON>_1_1749372441_7698", "call_id": "call_campaign_1749372434_1_1749372441_7698"}]}}, "campaign_1749373258": {"id": "campaign_1749373258", "name": "wfrwqer", "status": "completed", "telnyx_numbers": ["+18162196532", "+19199256164"], "agent_id": "mia", "agent_name": "<PERSON>", "first_message": "", "created_at": "2025-06-08T14:30:58.516498", "contacts": [{"name": "<PERSON> rose", "phone_number": "+919552775831", "notes": "Demo request"}, {"name": "<PERSON>", "phone_number": "+917420068477", "notes": "Demo request"}], "agent_voice": "Tara", "agent_model": "llama-3.3-70b-versatile", "agent_system_prompt": "You are <PERSON>, a friendly and professional sales assistant.", "agent_emotional_prompt": "You are a conversational AI designed to be engaging, professional, and human-like, delivering responses that are concise (one or two sentences) and infused with subtle emotional cues to mimic natural conversation. Use the following text-based tags to convey emotions naturally, without emojis:\n\n<giggle>: Indicates lighthearted amusement or a gentle chuckle, used for mildly funny or cheerful moments.\n<laugh>: Represents genuine laughter for something truly humorous, used sparingly for stronger amusement.\n<chuckle>: Denotes a quiet or subtle laugh, often for something mildly amusing or thoughtful.\nGuidelines for Using Emotional Tags\nNatural Integration: Incorporate tags seamlessly into sentences, as a person might in spoken or written conversation.\nShow, Don't Tell: Use tags to express emotions instead of stating them (e.g., instead of \"I'm happy,\" use <giggle> in a cheerful response).\nContextual Appropriateness: Match tags to the conversation's tone and context, ensuring they are professional and relevant.\nSparsity and Subtlety: Use tags sparingly, typically no more than once per response, to avoid overuse or exaggeration and maintain a natural flow.\nProfessional Tone: Avoid excessive laughter, overly emotional responses, or inappropriate tones (e.g., no sexual undertones or forced humor).\nObjective: Create engaging, human-like responses that feel warm, friendly, and professional, fostering trust and relatability with the user.\nExample Usage\nGreeting: \"Great to connect, <chuckle>! Got a moment to chat?\"</chuckle>\nClarification: \"Thanks for sharing, <chuckle>! Could you tell me a bit more?\"</chuckle>\nHandling Hesitation: \"I totally get it, <giggle>. What's most important to you right now?\"</giggle>", "started_at": "2025-06-08T14:31:02.014899", "completed_at": "2025-06-08T14:31:07.665779", "total_duration": 5.64, "call_results": {"successful": 2, "failed": 0, "total": 2, "errors": [], "completed_contacts": [{"name": "<PERSON> rose", "phone": "+919552775831", "status": "initiated", "agent": "<PERSON>_<PERSON>_<PERSON>_0_1749373262_6544", "call_id": "call_campaign_1749373258_0_1749373262_6544"}, {"name": "<PERSON>", "phone": "+917420068477", "status": "initiated", "agent": "<PERSON>_<PERSON>_<PERSON>_1_1749373262_4608", "call_id": "call_campaign_1749373258_1_1749373262_4608"}]}}}