#!/usr/bin/env python3
"""
Manual SIP trunk creation test
"""

import subprocess
import json
import tempfile
import os

# Your trunk configuration
trunk_config = {
    "trunk": {
        "name": "cbtest_19858539054",
        "address": "itscb.sip.telnyx.com",
        "numbers": ["+19858539054"],
        "auth_username": "itscb",
        "auth_password": "Iamcb123"
    }
}

def test_manual_sip_creation():
    print("🧪 Testing Manual SIP Trunk Creation")
    print("=" * 50)
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(trunk_config, f, indent=2)
        temp_file = f.name
        
    print(f"📄 Created temp config file: {temp_file}")
    print(f"📋 Config: {json.dumps(trunk_config, indent=2)}")
    
    try:
        # Run lk command
        cmd = ["lk", "sip", "outbound", "create", temp_file]
        print(f"\n🚀 Running: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, check=False)
        
        print(f"\n📊 Results:")
        print(f"Return code: {result.returncode}")
        print(f"STDOUT: {result.stdout}")
        print(f"STDERR: {result.stderr}")
        
        if result.returncode == 0:
            print("\n✅ SUCCESS! SIP trunk created successfully")
            
            # Try to extract SIP trunk ID
            import re
            match = re.search(r'ST_[a-zA-Z0-9]+', result.stdout)
            if match:
                sip_trunk_id = match.group(0)
                print(f"🎯 Extracted SIP Trunk ID: {sip_trunk_id}")
                return sip_trunk_id
            else:
                print("⚠️ Could not extract SIP trunk ID from output")
        else:
            print(f"\n❌ FAILED: Command returned {result.returncode}")
            
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        
    finally:
        # Clean up
        if os.path.exists(temp_file):
            os.unlink(temp_file)
            print(f"🧹 Cleaned up temp file: {temp_file}")

if __name__ == "__main__":
    test_manual_sip_creation() 