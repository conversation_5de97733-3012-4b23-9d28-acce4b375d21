#!/usr/bin/env python3
"""
Test script for Dual AI Mode Campaign System
Tests both Personalized AI and Shared AI modes
"""

import requests
import json
import time
import sys

# Configuration
API_BASE = "http://localhost:9090"

def test_dual_ai_modes():
    """Test both AI modes for campaigns"""
    
    print("🧪 Testing Dual AI Mode Campaign System")
    print("=" * 50)
    
    # Test 1: Create campaign with Shared AI (default)
    print("\n1️⃣ Testing SHARED AI Mode (Memory Efficient)")
    shared_campaign = {
        "name": "Test Shared AI Campaign",
        "telnyxNumbers": ["+19199256164"],
        "agentId": "mia",
        "firstMessage": "Hello! This is a test call using shared AI.",
        "contacts": [
            {"name": "Test Contact 1", "phone_number": "+15551234567", "notes": "Test contact for shared AI"},
            {"name": "Test Contact 2", "phone_number": "+15551234568", "notes": "Another test contact"}
        ],
        "usePersonalizedAI": False  # Shared mode
    }
    
    try:
        response = requests.post(f"{API_BASE}/api/campaigns", json=shared_campaign)
        if response.status_code == 200:
            shared_campaign_data = response.json()
            shared_campaign_id = shared_campaign_data['campaign']['id']
            print(f"✅ Shared AI campaign created: {shared_campaign_id}")
        else:
            print(f"❌ Failed to create shared AI campaign: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error creating shared AI campaign: {e}")
        return False
    
    # Test 2: Create campaign with Personalized AI
    print("\n2️⃣ Testing PERSONALIZED AI Mode (High Memory)")
    personalized_campaign = {
        "name": "Test Personalized AI Campaign",
        "telnyxNumbers": ["+19199256164"],
        "agentId": "mia",
        "firstMessage": "Hello! This is a test call using personalized AI.",
        "contacts": [
            {"name": "Test Contact 3", "phone_number": "+15551234569", "notes": "Test contact for personalized AI"},
            {"name": "Test Contact 4", "phone_number": "+15551234570", "notes": "Another personalized test"}
        ],
        "usePersonalizedAI": True  # Personalized mode
    }
    
    try:
        response = requests.post(f"{API_BASE}/api/campaigns", json=personalized_campaign)
        if response.status_code == 200:
            personalized_campaign_data = response.json()
            personalized_campaign_id = personalized_campaign_data['campaign']['id']
            print(f"✅ Personalized AI campaign created: {personalized_campaign_id}")
        else:
            print(f"❌ Failed to create personalized AI campaign: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error creating personalized AI campaign: {e}")
        return False
    
    # Test 3: Verify campaigns list shows AI mode
    print("\n3️⃣ Verifying Campaign List Shows AI Modes")
    try:
        response = requests.get(f"{API_BASE}/api/campaigns")
        if response.status_code == 200:
            campaigns = response.json()
            
            for campaign in campaigns:
                if campaign['id'] in [shared_campaign_id, personalized_campaign_id]:
                    ai_mode = campaign.get('aiMode', 'Unknown')
                    use_personalized = campaign.get('usePersonalizedAI', False)
                    print(f"📋 Campaign: {campaign['name']}")
                    print(f"   AI Mode: {ai_mode}")
                    print(f"   Personalized: {use_personalized}")
                    print(f"   Contacts: {campaign['contactsCount']}")
                    
        else:
            print(f"❌ Failed to fetch campaigns: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error fetching campaigns: {e}")
        return False
    
    # Test 4: Test campaign start (dry run - don't actually make calls)
    print("\n4️⃣ Testing Campaign Start Logic (Dry Run)")
    print("Note: This would normally start actual calls. In production, ensure you have:")
    print("- Valid phone numbers")
    print("- Sufficient memory for personalized mode")
    print("- Proper SIP trunk configuration")
    
    print(f"\n📊 Summary:")
    print(f"✅ Shared AI Campaign: {shared_campaign_id}")
    print(f"   - Memory efficient: 1 agent for all calls")
    print(f"   - Recommended for large campaigns")
    print(f"✅ Personalized AI Campaign: {personalized_campaign_id}")
    print(f"   - High memory usage: 1 agent per call")
    print(f"   - Better for personalized conversations")
    
    return True

def test_backend_connection():
    """Test if backend is running"""
    try:
        response = requests.get(f"{API_BASE}/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend connection successful")
            return True
        else:
            print(f"❌ Backend returned error: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend. Make sure it's running on port 9090.")
        return False
    except Exception as e:
        print(f"❌ Backend connection error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Dual AI Mode Test Suite")
    print("Testing Shared vs Personalized AI for campaigns")
    
    # Test backend connection first
    if not test_backend_connection():
        print("\n💡 Start the backend with: python simple_backend.py")
        sys.exit(1)
    
    # Run the dual AI mode tests
    if test_dual_ai_modes():
        print("\n🎉 All tests passed! Dual AI mode system is working correctly.")
        print("\n💡 Usage Tips:")
        print("- Use Shared AI for large campaigns (memory efficient)")
        print("- Use Personalized AI for small, high-touch campaigns")
        print("- Monitor memory usage when using Personalized AI")
    else:
        print("\n❌ Some tests failed. Check the backend logs for details.")
        sys.exit(1) 
"""
Test script for Dual AI Mode Campaign System
Tests both Personalized AI and Shared AI modes
"""

import requests
import json
import time
import sys

# Configuration
API_BASE = "http://localhost:9090"

def test_dual_ai_modes():
    """Test both AI modes for campaigns"""
    
    print("🧪 Testing Dual AI Mode Campaign System")
    print("=" * 50)
    
    # Test 1: Create campaign with Shared AI (default)
    print("\n1️⃣ Testing SHARED AI Mode (Memory Efficient)")
    shared_campaign = {
        "name": "Test Shared AI Campaign",
        "telnyxNumbers": ["+19199256164"],
        "agentId": "mia",
        "firstMessage": "Hello! This is a test call using shared AI.",
        "contacts": [
            {"name": "Test Contact 1", "phone_number": "+15551234567", "notes": "Test contact for shared AI"},
            {"name": "Test Contact 2", "phone_number": "+15551234568", "notes": "Another test contact"}
        ],
        "usePersonalizedAI": False  # Shared mode
    }
    
    try:
        response = requests.post(f"{API_BASE}/api/campaigns", json=shared_campaign)
        if response.status_code == 200:
            shared_campaign_data = response.json()
            shared_campaign_id = shared_campaign_data['campaign']['id']
            print(f"✅ Shared AI campaign created: {shared_campaign_id}")
        else:
            print(f"❌ Failed to create shared AI campaign: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error creating shared AI campaign: {e}")
        return False
    
    # Test 2: Create campaign with Personalized AI
    print("\n2️⃣ Testing PERSONALIZED AI Mode (High Memory)")
    personalized_campaign = {
        "name": "Test Personalized AI Campaign",
        "telnyxNumbers": ["+19199256164"],
        "agentId": "mia",
        "firstMessage": "Hello! This is a test call using personalized AI.",
        "contacts": [
            {"name": "Test Contact 3", "phone_number": "+15551234569", "notes": "Test contact for personalized AI"},
            {"name": "Test Contact 4", "phone_number": "+15551234570", "notes": "Another personalized test"}
        ],
        "usePersonalizedAI": True  # Personalized mode
    }
    
    try:
        response = requests.post(f"{API_BASE}/api/campaigns", json=personalized_campaign)
        if response.status_code == 200:
            personalized_campaign_data = response.json()
            personalized_campaign_id = personalized_campaign_data['campaign']['id']
            print(f"✅ Personalized AI campaign created: {personalized_campaign_id}")
        else:
            print(f"❌ Failed to create personalized AI campaign: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error creating personalized AI campaign: {e}")
        return False
    
    # Test 3: Verify campaigns list shows AI mode
    print("\n3️⃣ Verifying Campaign List Shows AI Modes")
    try:
        response = requests.get(f"{API_BASE}/api/campaigns")
        if response.status_code == 200:
            campaigns = response.json()
            
            for campaign in campaigns:
                if campaign['id'] in [shared_campaign_id, personalized_campaign_id]:
                    ai_mode = campaign.get('aiMode', 'Unknown')
                    use_personalized = campaign.get('usePersonalizedAI', False)
                    print(f"📋 Campaign: {campaign['name']}")
                    print(f"   AI Mode: {ai_mode}")
                    print(f"   Personalized: {use_personalized}")
                    print(f"   Contacts: {campaign['contactsCount']}")
                    
        else:
            print(f"❌ Failed to fetch campaigns: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error fetching campaigns: {e}")
        return False
    
    # Test 4: Test campaign start (dry run - don't actually make calls)
    print("\n4️⃣ Testing Campaign Start Logic (Dry Run)")
    print("Note: This would normally start actual calls. In production, ensure you have:")
    print("- Valid phone numbers")
    print("- Sufficient memory for personalized mode")
    print("- Proper SIP trunk configuration")
    
    print(f"\n📊 Summary:")
    print(f"✅ Shared AI Campaign: {shared_campaign_id}")
    print(f"   - Memory efficient: 1 agent for all calls")
    print(f"   - Recommended for large campaigns")
    print(f"✅ Personalized AI Campaign: {personalized_campaign_id}")
    print(f"   - High memory usage: 1 agent per call")
    print(f"   - Better for personalized conversations")
    
    return True

def test_backend_connection():
    """Test if backend is running"""
    try:
        response = requests.get(f"{API_BASE}/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend connection successful")
            return True
        else:
            print(f"❌ Backend returned error: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend. Make sure it's running on port 9090.")
        return False
    except Exception as e:
        print(f"❌ Backend connection error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Dual AI Mode Test Suite")
    print("Testing Shared vs Personalized AI for campaigns")
    
    # Test backend connection first
    if not test_backend_connection():
        print("\n💡 Start the backend with: python simple_backend.py")
        sys.exit(1)
    
    # Run the dual AI mode tests
    if test_dual_ai_modes():
        print("\n🎉 All tests passed! Dual AI mode system is working correctly.")
        print("\n💡 Usage Tips:")
        print("- Use Shared AI for large campaigns (memory efficient)")
        print("- Use Personalized AI for small, high-touch campaigns")
        print("- Monitor memory usage when using Personalized AI")
    else:
        print("\n❌ Some tests failed. Check the backend logs for details.")
        sys.exit(1) 