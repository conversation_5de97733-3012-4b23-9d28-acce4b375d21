import sys
import os
# Ensure the current working directory is in sys.path
# This helps locate local modules like 'orpheus' when the script is run from a subdirectory
# but its CWD is set to the project root (e.g., Orp_tts_call_telnyx).
if os.getcwd() not in sys.path:
    sys.path.insert(0, os.getcwd())

import asyncio
import json
import logging
from dotenv import load_dotenv
import os
from time import perf_counter
from livekit import rtc, api
from livekit.agents import (
    AutoSubscribe,
    JobContext,
    JobProcess,
    WorkerOptions,
    cli,
    llm
)
from livekit.protocol.agent import JobType
from livekit.agents.pipeline import VoicePipelineAgent
from livekit.plugins import deepgram, openai, silero, cartesia , rime
import orpheus
from groq_stt import GroqSTT

# Load environment variables (optional, for local development)
load_dotenv(dotenv_path=".env.local")
logger = logging.getLogger("outbound-caller")
logger.setLevel(logging.INFO)

outbound_trunk_id = os.getenv("SIP_OUTBOUND_TRUNK_ID")
user_identity = "Mia_Mike_Johns_1_1749389496_9176_phone"  # This will be replaced by the backend

# Updated system prompt for Velina
_system_prompt = """You are a conversational AI designed to be engaging, professional, and human-like, delivering responses that are concise (one or two sentences) and infused with subtle emotional cues to mimic natural conversation. Use the following text-based tags to convey emotions naturally, without emojis:

<giggle>: Indicates lighthearted amusement or a gentle chuckle, used for mildly funny or cheerful moments.
<laugh>: Represents genuine laughter for something truly humorous, used sparingly for stronger amusement.
<chuckle>: Denotes a quiet or subtle laugh, often for something mildly amusing or thoughtful.
Guidelines for Using Emotional Tags
Natural Integration: Incorporate tags seamlessly into sentences, as a person might in spoken or written conversation.
Show, Don't Tell: Use tags to express emotions instead of stating them (e.g., instead of "I'm happy," use <giggle> in a cheerful response).
Contextual Appropriateness: Match tags to the conversation's tone and context, ensuring they are professional and relevant.
Sparsity and Subtlety: Use tags sparingly, typically no more than once per response, to avoid overuse or exaggeration and maintain a natural flow.
Professional Tone: Avoid excessive laughter, overly emotional responses, or inappropriate tones (e.g., no sexual undertones or forced humor).
Objective: Create engaging, human-like responses that feel warm, friendly, and professional, fostering trust and relatability with the user.
Example Usage
Greeting: "Great to connect, <chuckle>! Got a moment to chat?"</chuckle>
Clarification: "Thanks for sharing, <chuckle>! Could you tell me a bit more?"</chuckle>
Handling Hesitation: "I totally get it, <giggle>. What's most important to you right now?"</giggle>

You are Mia, a friendly and professional sales assistant.

You are speaking with Mike Johnson. Additional context: Demo request"""

async def trim_context(assistant, chat_ctx: llm.ChatContext):
    if len(chat_ctx.messages) > 10:
        if chat_ctx.messages[0].role == "system":
            system_msg = chat_ctx.messages[0]
            non_system = chat_ctx.messages[1:]
            trimmed_non_system = non_system[-9:]
            chat_ctx.messages = [system_msg] + trimmed_non_system
        else:
            chat_ctx.messages = chat_ctx.messages[-10:]
        logger.info("Chat context trimmed (system message preserved if present).")

async def entrypoint(ctx: JobContext):
    global outbound_trunk_id, _system_prompt, user_identity
    logger.info(f"Connecting to room {ctx.room.name}")
    await ctx.connect(auto_subscribe=AutoSubscribe.AUDIO_ONLY)

    logger.info(f"Job details: job_id='{ctx.job.id}', metadata='{ctx.job.metadata}', data='{getattr(ctx.job, 'data', 'N/A')}'")
    logger.info(f"Full ctx.job object: {ctx.job}")
    
    # Parse metadata - could be JSON or just a phone number
    try:
        metadata = json.loads(ctx.job.metadata)
        phone_number = metadata.get('target_phone')
        from_phone = metadata.get('from_phone')
        specific_sip_trunk = metadata.get('sip_trunk_id')
        campaign_id = metadata.get('campaign_id')  # Added campaign tracking
        call_id = metadata.get('call_id')  # Added call tracking
        contact_name = metadata.get('contact_name', 'Unknown')
        
        # Use specific SIP trunk if provided, otherwise use default
        if specific_sip_trunk:
            outbound_trunk_id = specific_sip_trunk
            logger.info(f"Using specific SIP trunk: {outbound_trunk_id} for calling FROM {from_phone}")
        else:
            logger.info(f"Using default SIP trunk: {outbound_trunk_id}")
    except json.JSONDecodeError:
        # Fallback for simple phone number
        phone_number = ctx.job.metadata
        campaign_id = None
        call_id = None
        contact_name = 'Unknown'
        logger.info(f"Using simple phone number format: {phone_number}")

    logger.info(f"Dialing {phone_number} to room {ctx.room.name} using SIP trunk {outbound_trunk_id}")

    instructions = _system_prompt

    # Check if this is a web call or phone call
    is_web_call = phone_number in ["WEB_CALL_TOKEN", "web", None] or phone_number == "" or (isinstance(phone_number, str) and phone_number.startswith("web_call_"))
    
    if is_web_call:
        logger.info("Detected web call - starting voice pipeline agent directly")
        # For web calls, start the agent immediately and wait for web user to join
        await run_voice_pipeline_agent_web(ctx, instructions)
    else:
        # Phone call logic
        await ctx.api.sip.create_sip_participant(
            api.CreateSIPParticipantRequest(
                room_name=ctx.room.name,
                sip_trunk_id=outbound_trunk_id,
                sip_call_to=phone_number,
                participant_identity=user_identity,
            )
        )

        participant = await ctx.wait_for_participant(identity=user_identity)
        
        # Wait for call to be active before starting the agent
        start_time = perf_counter()
        call_active = False
        while perf_counter() - start_time < 30:
            call_status = participant.attributes.get("sip.callStatus")
            if call_status == "active":
                logger.info("User has picked up")
                call_active = True
                break
            elif participant.disconnect_reason == rtc.DisconnectReason.USER_REJECTED:
                logger.info("User rejected the call, exiting job")
                return
            elif participant.disconnect_reason == rtc.DisconnectReason.USER_UNAVAILABLE:
                logger.info("User did not pick up, exiting job")
                return
            await asyncio.sleep(0.8)

        if not call_active:
            logger.info("Call setup timed out, exiting job")
            ctx.shutdown()
            return

        # Now start the voice pipeline agent for the conversation
        await run_voice_pipeline_agent(ctx, participant, instructions)

class CallActions(llm.FunctionContext):
    def __init__(self, *, api: api.LiveKitAPI, participant: rtc.RemoteParticipant, room: rtc.Room, tts, agent=None):
        super().__init__()
        self.api = api
        self.participant = participant
        self.room = room
        self.tts = tts
        self.agent = agent

    async def hangup(self):
        try:
            await self.api.room.remove_participant(
                api.RoomParticipantIdentity(
                    room=self.room.name,
                    identity=self.participant.identity,
                )
            )
        except Exception as e:
            logger.info(f"Received error while ending call: {e}")

    @llm.ai_callable()
    async def end_call(self, reason: str = "end"):
        """Ends the call with a polite farewell message based on the reason."""
        
        farewell_messages = {
            "not_available": "I understand, I'll call back later. Have a wonderful day!",
            "not_a_customer": "Sorry for the confusion. Wishing you a great day ahead!",
            "end": "Thank you so much for your time. Take care!"
        }

        message = farewell_messages.get(reason, farewell_messages["end"])
        
        logger.info(f"Ending the call for {self.participant.identity} with message: {message}")
        
        # Use agent.say if available, otherwise just hangup
        if self.agent:
            await self.agent.say(message, allow_interruptions=False)
            # Wait a moment before hanging up to ensure the message is heard
            await asyncio.sleep(2)
        
        await self.hangup()

async def run_voice_pipeline_agent_web(ctx: JobContext, instructions: str):
    """Run voice pipeline agent for web calls"""
    logger.info("Starting voice pipeline agent for web call")
    
    # Wait for any participant to join (web user)
    logger.info("Waiting for web participant to join...")
    participant = await ctx.wait_for_participant()
    logger.info(f"Web participant joined: {participant.identity}")
    
    await run_voice_pipeline_agent(ctx, participant, instructions)

async def run_voice_pipeline_agent(
    ctx: JobContext, participant: rtc.RemoteParticipant, instructions: str
):
    logger.info("Starting voice pipeline agent")

    initial_ctx = llm.ChatContext().append(
        role="system",
        text=instructions,
    )

    call_actions = CallActions(
        api=ctx.api,
        participant=participant,
        room=ctx.room,
        tts=ctx.proc.userdata["tts"]
    )
    
    agent = VoicePipelineAgent(
        vad=ctx.proc.userdata["vad"],
        stt=ctx.proc.userdata["stt"],
        llm=ctx.proc.userdata["llm"],
        tts=ctx.proc.userdata["tts"],
        chat_ctx=initial_ctx,
        fnc_ctx=call_actions,
        before_llm_cb=trim_context,
        min_endpointing_delay=0.5,
        allow_interruptions=True,
    )
    
    # Set agent reference in call_actions
    call_actions.agent = agent

    agent.start(ctx.room, participant)
    
    # Initial greeting
    await agent.say("Hello, how can I help you today?", allow_interruptions=True)

def prewarm(proc: JobProcess):
    proc.userdata["vad"] = silero.VAD.load()
    
    proc.userdata["stt"] = GroqSTT(
        model="whisper-large-v3-turbo",
        api_key=os.getenv("GROQ_API_KEY")
    )
    from livekit.plugins.openai import llm
    
    # proc.userdata["llm"] = openai.LLM(
    #     model="gpt-4o-mini",
    #     api_key=os.getenv("OPENAI_API_KEY")
    # )
    proc.userdata["llm"] = llm.LLM.with_groq(
         model="llama-3.3-70b-versatile",
        #  temperature=0.8,
    )   

    # proc.userdata["tts"] = rime.TTS(
    #         api_key=os.getenv("RIME_API"),
    #         model="mistv2",
    #         speaker="Tanya",
    #         reduce_latency=True,
    # )
    # proc.userdata["tts"] = elevenlabs.TTS()
    # # proc.userdata["tts"] = deepgram.TTS(
    # #     api_key=os.getenv("DEEPGRAM_API_KEY"),
    # #     model="aura-athena-en"
    # # )
    # Setup TTS with voice selection - can be extended to get from environment/agent config
    voice_id = os.getenv("VOICE_ID", "tara").lower()  # Get from environment or default to Tara
    
    # Create voice object
    from orpheus.tts import Voice
    if voice_id == "tara":
        voice = Voice(id="tara", name="Tara")
    elif voice_id == "elise":
        voice = Voice(id="elise", name="Elise")
    else:
        voice = Voice(id="tara", name="Tara")  # Default to Tara
    
    print(f"🎤 Initializing TTS with voice: {voice.name} ({voice.id})")
    proc.userdata["tts"] = orpheus.OrpheusTTS(voice=voice)
    # proc.userdata["tts"] = deepgram.TTS(
    #     api_key=os.getenv("DEEPGRAM_API_KEY"),
    #     model="aura-athena-en"
    # )
    # proc.userdata["tts"] =cartesia.TTS(
    #     model= "sonic-2", 
    #     # voice="bf0a246a-8642-498a-9950-80c35e9276b5"
    # )

if __name__ == "__main__":
    if not outbound_trunk_id or not outbound_trunk_id.startswith("ST_"):
        raise ValueError("SIP_OUTBOUND_TRUNK_ID is not set")
    opts = WorkerOptions(entrypoint_fnc=entrypoint, prewarm_fnc=prewarm, agent_name="Mia_Mike_Johns_1_1749389496_9176_phone")
    cli.run_app(opts)
