#!/usr/bin/env python3
"""
Shared Agent Pool System
Creates multiple shared agents per campaign for concurrent call handling
"""

import uuid
import time
import threading
from typing import Dict, List, Optional
from dataclasses import dataclass
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

@dataclass
class SharedCall:
    call_id: str
    room_name: str
    target_phone: str
    from_phone: str
    contact_name: str
    campaign_id: str
    sip_trunk_id: str
    status: str = "pending"
    created_at: datetime = None
    assigned_agent: str = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

class SharedAgentPool:
    """
    Manages a pool of shared agents for each campaign
    """
    def __init__(self):
        self.campaign_agents: Dict[str, List[str]] = {}  # campaign_id -> list of agent_ids
        self.agent_info: Dict[str, Dict] = {}  # agent_id -> agent_info
        self.agent_calls: Dict[str, List[SharedCall]] = {}  # agent_id -> active calls
        self.lock = threading.Lock()
    
    def create_shared_agent_pool(self, campaign_id: str, agent_name: str, system_prompt: str, pool_size: int = 3) -> List[str]:
        """
        Create a pool of shared agents for a campaign
        Returns list of agent IDs
        """
        with self.lock:
            if campaign_id in self.campaign_agents:
                logger.info(f"Shared agent pool for campaign {campaign_id} already exists")
                return self.campaign_agents[campaign_id]
            
            agent_ids = []
            
            for i in range(pool_size):
                agent_id = f"shared_{agent_name}_{campaign_id}_{i+1}"
                
                self.agent_info[agent_id] = {
                    'agent_id': agent_id,
                    'agent_name': f"Shared_{agent_name}_{i+1}",
                    'campaign_id': campaign_id,
                    'system_prompt': system_prompt,
                    'created_at': datetime.now(),
                    'status': 'ready',
                    'is_busy': False,
                    'current_call': None
                }
                
                self.agent_calls[agent_id] = []
                agent_ids.append(agent_id)
            
            self.campaign_agents[campaign_id] = agent_ids
            
            logger.info(f"✅ Created shared agent pool for {campaign_id}: {pool_size} agents ({agent_name})")
            return agent_ids
    
    def get_available_agent(self, campaign_id: str) -> Optional[str]:
        """
        Get an available agent from the campaign pool
        Returns agent_id or None if all busy
        """
        with self.lock:
            if campaign_id not in self.campaign_agents:
                logger.error(f"No agent pool found for campaign {campaign_id}")
                return None
            
            # Find first available agent
            for agent_id in self.campaign_agents[campaign_id]:
                if agent_id in self.agent_info and not self.agent_info[agent_id]['is_busy']:
                    return agent_id
            
            logger.warning(f"All agents busy for campaign {campaign_id}")
            return None
    
    def assign_call_to_agent(self, agent_id: str, call: SharedCall) -> bool:
        """
        Assign a call to a specific agent
        """
        with self.lock:
            if agent_id not in self.agent_info:
                logger.error(f"Agent {agent_id} not found")
                return False
            
            if self.agent_info[agent_id]['is_busy']:
                logger.warning(f"Agent {agent_id} is already busy")
                return False
            
            # Mark agent as busy
            self.agent_info[agent_id]['is_busy'] = True
            self.agent_info[agent_id]['current_call'] = call.call_id
            
            # Add call to agent's active calls
            self.agent_calls[agent_id].append(call)
            call.assigned_agent = agent_id
            call.status = "assigned"
            
            logger.info(f"📞 Assigned call {call.call_id} to agent {self.agent_info[agent_id]['agent_name']}")
            return True
    
    def complete_call(self, agent_id: str, call_id: str):
        """
        Mark a call as completed and free up the agent
        """
        with self.lock:
            if agent_id not in self.agent_info:
                return
            
            # Remove call from active calls
            self.agent_calls[agent_id] = [call for call in self.agent_calls[agent_id] if call.call_id != call_id]
            
            # Mark agent as available
            self.agent_info[agent_id]['is_busy'] = False
            self.agent_info[agent_id]['current_call'] = None
            
            logger.info(f"✅ Completed call {call_id}, agent {agent_id} now available")
    
    def get_campaign_stats(self, campaign_id: str) -> Dict:
        """
        Get statistics for a campaign's agent pool
        """
        with self.lock:
            if campaign_id not in self.campaign_agents:
                return {'error': 'Campaign not found'}
            
            stats = {
                'campaign_id': campaign_id,
                'total_agents': len(self.campaign_agents[campaign_id]),
                'available_agents': 0,
                'busy_agents': 0,
                'active_calls': 0,
                'agents': []
            }
            
            for agent_id in self.campaign_agents[campaign_id]:
                if agent_id in self.agent_info:
                    agent_info = self.agent_info[agent_id]
                    is_busy = agent_info['is_busy']
                    
                    if is_busy:
                        stats['busy_agents'] += 1
                    else:
                        stats['available_agents'] += 1
                    
                    stats['active_calls'] += len(self.agent_calls[agent_id])
                    
                    stats['agents'].append({
                        'agent_id': agent_id,
                        'agent_name': agent_info['agent_name'],
                        'is_busy': is_busy,
                        'current_call': agent_info['current_call'],
                        'active_calls': len(self.agent_calls[agent_id])
                    })
            
            return stats

# Global shared agent pool
shared_agent_pool = SharedAgentPool()

def create_shared_campaign_agent_pool(campaign_id: str, agent_name: str, system_prompt: str, pool_size: int = 3) -> List[str]:
    """
    Create a pool of shared agents for a campaign
    """
    return shared_agent_pool.create_shared_agent_pool(
        campaign_id=campaign_id,
        agent_name=agent_name,
        system_prompt=system_prompt,
        pool_size=pool_size
    )

def get_available_shared_agent(campaign_id: str) -> Optional[str]:
    """
    Get an available shared agent for a campaign
    """
    return shared_agent_pool.get_available_agent(campaign_id)

def assign_call_to_shared_agent(campaign_id: str, call_data: Dict) -> Dict:
    """
    Assign a call to an available shared agent in the campaign
    """
    # Get available agent
    agent_id = shared_agent_pool.get_available_agent(campaign_id)
    
    if not agent_id:
        return {
            'success': False,
            'error': f'No available shared agents for campaign {campaign_id}'
        }
    
    # Create call object
    call = SharedCall(
        call_id=call_data.get('call_id', str(uuid.uuid4())),
        room_name=call_data.get('room_name', f"room_{agent_id}_{int(time.time())}"),
        target_phone=call_data.get('target_phone'),
        from_phone=call_data.get('from_phone'),
        contact_name=call_data.get('contact_name', 'Unknown'),
        campaign_id=campaign_id,
        sip_trunk_id=call_data.get('sip_trunk_id')
    )
    
    # Assign to agent
    if shared_agent_pool.assign_call_to_agent(agent_id, call):
        return {
            'success': True,
            'agent_id': agent_id,
            'call_id': call.call_id,
            'room_name': call.room_name,
            'agent_name': shared_agent_pool.agent_info[agent_id]['agent_name']
        }
    else:
        return {
            'success': False,
            'error': f'Failed to assign call to agent {agent_id}'
        }

def complete_shared_agent_call(campaign_id: str, call_id: str):
    """
    Complete a call and free up the agent
    """
    # Find the agent handling this call
    for agent_id in shared_agent_pool.campaign_agents.get(campaign_id, []):
        if agent_id in shared_agent_pool.agent_info:
            if shared_agent_pool.agent_info[agent_id]['current_call'] == call_id:
                shared_agent_pool.complete_call(agent_id, call_id)
                break

def get_shared_agent_stats(campaign_id: str = None) -> Dict:
    """
    Get statistics for shared agents
    """
    if campaign_id:
        return shared_agent_pool.get_campaign_stats(campaign_id)
    else:
        # Return stats for all campaigns
        all_stats = {}
        for campaign_id in shared_agent_pool.campaign_agents:
            all_stats[campaign_id] = shared_agent_pool.get_campaign_stats(campaign_id)
        return all_stats 
"""
Shared Agent Pool System
Creates multiple shared agents per campaign for concurrent call handling
"""

import uuid
import time
import threading
from typing import Dict, List, Optional
from dataclasses import dataclass
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

@dataclass
class SharedCall:
    call_id: str
    room_name: str
    target_phone: str
    from_phone: str
    contact_name: str
    campaign_id: str
    sip_trunk_id: str
    status: str = "pending"
    created_at: datetime = None
    assigned_agent: str = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

class SharedAgentPool:
    """
    Manages a pool of shared agents for each campaign
    """
    def __init__(self):
        self.campaign_agents: Dict[str, List[str]] = {}  # campaign_id -> list of agent_ids
        self.agent_info: Dict[str, Dict] = {}  # agent_id -> agent_info
        self.agent_calls: Dict[str, List[SharedCall]] = {}  # agent_id -> active calls
        self.lock = threading.Lock()
    
    def create_shared_agent_pool(self, campaign_id: str, agent_name: str, system_prompt: str, pool_size: int = 3) -> List[str]:
        """
        Create a pool of shared agents for a campaign
        Returns list of agent IDs
        """
        with self.lock:
            if campaign_id in self.campaign_agents:
                logger.info(f"Shared agent pool for campaign {campaign_id} already exists")
                return self.campaign_agents[campaign_id]
            
            agent_ids = []
            
            for i in range(pool_size):
                agent_id = f"shared_{agent_name}_{campaign_id}_{i+1}"
                
                self.agent_info[agent_id] = {
                    'agent_id': agent_id,
                    'agent_name': f"Shared_{agent_name}_{i+1}",
                    'campaign_id': campaign_id,
                    'system_prompt': system_prompt,
                    'created_at': datetime.now(),
                    'status': 'ready',
                    'is_busy': False,
                    'current_call': None
                }
                
                self.agent_calls[agent_id] = []
                agent_ids.append(agent_id)
            
            self.campaign_agents[campaign_id] = agent_ids
            
            logger.info(f"✅ Created shared agent pool for {campaign_id}: {pool_size} agents ({agent_name})")
            return agent_ids
    
    def get_available_agent(self, campaign_id: str) -> Optional[str]:
        """
        Get an available agent from the campaign pool
        Returns agent_id or None if all busy
        """
        with self.lock:
            if campaign_id not in self.campaign_agents:
                logger.error(f"No agent pool found for campaign {campaign_id}")
                return None
            
            # Find first available agent
            for agent_id in self.campaign_agents[campaign_id]:
                if agent_id in self.agent_info and not self.agent_info[agent_id]['is_busy']:
                    return agent_id
            
            logger.warning(f"All agents busy for campaign {campaign_id}")
            return None
    
    def assign_call_to_agent(self, agent_id: str, call: SharedCall) -> bool:
        """
        Assign a call to a specific agent
        """
        with self.lock:
            if agent_id not in self.agent_info:
                logger.error(f"Agent {agent_id} not found")
                return False
            
            if self.agent_info[agent_id]['is_busy']:
                logger.warning(f"Agent {agent_id} is already busy")
                return False
            
            # Mark agent as busy
            self.agent_info[agent_id]['is_busy'] = True
            self.agent_info[agent_id]['current_call'] = call.call_id
            
            # Add call to agent's active calls
            self.agent_calls[agent_id].append(call)
            call.assigned_agent = agent_id
            call.status = "assigned"
            
            logger.info(f"📞 Assigned call {call.call_id} to agent {self.agent_info[agent_id]['agent_name']}")
            return True
    
    def complete_call(self, agent_id: str, call_id: str):
        """
        Mark a call as completed and free up the agent
        """
        with self.lock:
            if agent_id not in self.agent_info:
                return
            
            # Remove call from active calls
            self.agent_calls[agent_id] = [call for call in self.agent_calls[agent_id] if call.call_id != call_id]
            
            # Mark agent as available
            self.agent_info[agent_id]['is_busy'] = False
            self.agent_info[agent_id]['current_call'] = None
            
            logger.info(f"✅ Completed call {call_id}, agent {agent_id} now available")
    
    def get_campaign_stats(self, campaign_id: str) -> Dict:
        """
        Get statistics for a campaign's agent pool
        """
        with self.lock:
            if campaign_id not in self.campaign_agents:
                return {'error': 'Campaign not found'}
            
            stats = {
                'campaign_id': campaign_id,
                'total_agents': len(self.campaign_agents[campaign_id]),
                'available_agents': 0,
                'busy_agents': 0,
                'active_calls': 0,
                'agents': []
            }
            
            for agent_id in self.campaign_agents[campaign_id]:
                if agent_id in self.agent_info:
                    agent_info = self.agent_info[agent_id]
                    is_busy = agent_info['is_busy']
                    
                    if is_busy:
                        stats['busy_agents'] += 1
                    else:
                        stats['available_agents'] += 1
                    
                    stats['active_calls'] += len(self.agent_calls[agent_id])
                    
                    stats['agents'].append({
                        'agent_id': agent_id,
                        'agent_name': agent_info['agent_name'],
                        'is_busy': is_busy,
                        'current_call': agent_info['current_call'],
                        'active_calls': len(self.agent_calls[agent_id])
                    })
            
            return stats

# Global shared agent pool
shared_agent_pool = SharedAgentPool()

def create_shared_campaign_agent_pool(campaign_id: str, agent_name: str, system_prompt: str, pool_size: int = 3) -> List[str]:
    """
    Create a pool of shared agents for a campaign
    """
    return shared_agent_pool.create_shared_agent_pool(
        campaign_id=campaign_id,
        agent_name=agent_name,
        system_prompt=system_prompt,
        pool_size=pool_size
    )

def get_available_shared_agent(campaign_id: str) -> Optional[str]:
    """
    Get an available shared agent for a campaign
    """
    return shared_agent_pool.get_available_agent(campaign_id)

def assign_call_to_shared_agent(campaign_id: str, call_data: Dict) -> Dict:
    """
    Assign a call to an available shared agent in the campaign
    """
    # Get available agent
    agent_id = shared_agent_pool.get_available_agent(campaign_id)
    
    if not agent_id:
        return {
            'success': False,
            'error': f'No available shared agents for campaign {campaign_id}'
        }
    
    # Create call object
    call = SharedCall(
        call_id=call_data.get('call_id', str(uuid.uuid4())),
        room_name=call_data.get('room_name', f"room_{agent_id}_{int(time.time())}"),
        target_phone=call_data.get('target_phone'),
        from_phone=call_data.get('from_phone'),
        contact_name=call_data.get('contact_name', 'Unknown'),
        campaign_id=campaign_id,
        sip_trunk_id=call_data.get('sip_trunk_id')
    )
    
    # Assign to agent
    if shared_agent_pool.assign_call_to_agent(agent_id, call):
        return {
            'success': True,
            'agent_id': agent_id,
            'call_id': call.call_id,
            'room_name': call.room_name,
            'agent_name': shared_agent_pool.agent_info[agent_id]['agent_name']
        }
    else:
        return {
            'success': False,
            'error': f'Failed to assign call to agent {agent_id}'
        }

def complete_shared_agent_call(campaign_id: str, call_id: str):
    """
    Complete a call and free up the agent
    """
    # Find the agent handling this call
    for agent_id in shared_agent_pool.campaign_agents.get(campaign_id, []):
        if agent_id in shared_agent_pool.agent_info:
            if shared_agent_pool.agent_info[agent_id]['current_call'] == call_id:
                shared_agent_pool.complete_call(agent_id, call_id)
                break

def get_shared_agent_stats(campaign_id: str = None) -> Dict:
    """
    Get statistics for shared agents
    """
    if campaign_id:
        return shared_agent_pool.get_campaign_stats(campaign_id)
    else:
        # Return stats for all campaigns
        all_stats = {}
        for campaign_id in shared_agent_pool.campaign_agents:
            all_stats[campaign_id] = shared_agent_pool.get_campaign_stats(campaign_id)
        return all_stats 