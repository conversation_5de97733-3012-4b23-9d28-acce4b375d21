#!/usr/bin/env python3
"""
Direct test of the CallLogAnalyzer
"""

import sys
import os
sys.path.append('Orp_tts_call_telnyx')

from call_log_analyzer import CallLogAnalyzer

def test_analyzer():
    """Test the CallLogAnalyzer directly"""
    
    campaign_id = "campaign_1749386900"  # dh<PERSON><PERSON><PERSON>y campaign
    
    print(f"🔍 Testing CallLogAnalyzer for campaign: {campaign_id}")
    print("=" * 50)
    
    analyzer = CallLogAnalyzer("Orp_tts_call_telnyx/data")
    analysis = analyzer.analyze_campaign(campaign_id)
    
    print("📊 Analysis Result:")
    import json
    print(json.dumps(analysis, indent=2))

if __name__ == "__main__":
    test_analyzer() 