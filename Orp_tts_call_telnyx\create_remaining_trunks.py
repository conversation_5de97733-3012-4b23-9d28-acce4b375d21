#!/usr/bin/env python3
"""
Create remaining SIP trunks for the other two numbers
"""

import subprocess
import json
import tempfile
import os

# Remaining numbers to create trunks for
remaining_numbers = ["+19199256164", "+18162196532"]

def create_trunk_for_number(phone_number):
    """Create SIP trunk for a specific number"""
    trunk_config = {
        "trunk": {
            "name": f"cbtest_{phone_number.replace('+', '')}",
            "address": "itscb.sip.telnyx.com",
            "numbers": [phone_number],
            "auth_username": "itscb",
            "auth_password": "Iamcb123"
        }
    }
    
    print(f"\n🚀 Creating SIP trunk for {phone_number}")
    print(f"📋 Config: {json.dumps(trunk_config, indent=2)}")
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(trunk_config, f, indent=2)
        temp_file = f.name
    
    try:
        # Run lk command
        cmd = ["lk", "sip", "outbound", "create", temp_file]
        print(f"🏃 Running: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, check=False)
        
        print(f"📊 Return code: {result.returncode}")
        print(f"📄 STDOUT: {result.stdout}")
        if result.stderr:
            print(f"⚠️ STDERR: {result.stderr}")
        
        if result.returncode == 0:
            # Extract SIP trunk ID
            import re
            match = re.search(r'ST_[a-zA-Z0-9]+', result.stdout)
            if match:
                sip_trunk_id = match.group(0)
                print(f"✅ SUCCESS! Created {sip_trunk_id} for {phone_number}")
                return sip_trunk_id
            else:
                print(f"⚠️ Could not extract SIP trunk ID from: {result.stdout}")
                return None
        else:
            print(f"❌ FAILED: Command returned {result.returncode}")
            return None
            
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        return None
        
    finally:
        # Clean up
        if os.path.exists(temp_file):
            os.unlink(temp_file)

def main():
    print("🎯 Creating Remaining SIP Trunks")
    print("=" * 50)
    
    results = {}
    
    for phone_number in remaining_numbers:
        sip_trunk_id = create_trunk_for_number(phone_number)
        results[phone_number] = sip_trunk_id
    
    print(f"\n📊 FINAL RESULTS:")
    print("=" * 30)
    
    # Print current mapping including the one we already created
    print("+19858539054 → ST_76EdGu5Epvj5 (already created)")
    
    for phone_number, sip_trunk_id in results.items():
        if sip_trunk_id:
            print(f"{phone_number} → {sip_trunk_id}")
        else:
            print(f"{phone_number} → FAILED")
    
    print(f"\n📝 Update your data/sip_trunks.json with:")
    mapping = {
        "+19858539054": "ST_76EdGu5Epvj5"
    }
    mapping.update(results)
    
    print(json.dumps(mapping, indent=2))

if __name__ == "__main__":
    main() 