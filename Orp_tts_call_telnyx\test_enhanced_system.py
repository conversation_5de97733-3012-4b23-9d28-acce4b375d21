#!/usr/bin/env python3
"""
Test script for the Enhanced Campaign Statistics System
Inspired by CALLBOT2.1's comprehensive approach
"""

import requests
import json
import time

BASE_URL = "http://localhost:9090"

def test_enhanced_statistics():
    """Test the enhanced statistics endpoint"""
    print("🧪 Testing Enhanced Statistics System")
    print("=" * 50)
    
    # First, let's see what campaigns exist
    try:
        campaigns_response = requests.get(f"{BASE_URL}/api/campaigns")
        if campaigns_response.status_code == 200:
            campaigns = campaigns_response.json()
            print(f"📊 Found {len(campaigns)} campaigns")
            
            if campaigns:
                # Handle both dict and list formats
                if isinstance(campaigns, dict):
                    campaign_id = list(campaigns.keys())[0]
                elif isinstance(campaigns, list) and campaigns:
                    campaign_id = campaigns[0].get('id') or campaigns[0].get('campaign_id', 'unknown')
                else:
                    campaign_id = None
                
                                if campaign_id:
                    print(f"\n🔍 Testing enhanced statistics for campaign: {campaign_id}")
                    
                    enhanced_response = requests.get(f"{BASE_URL}/api/campaigns/{campaign_id}/statistics/enhanced")
                    
                    if enhanced_response.status_code == 200:
                        stats = enhanced_response.json()
                        print("\n✅ Enhanced Statistics Results:")
                        
                        # Show key metrics
                        if 'data' in stats:
                            data = stats['data']
                            print(f"\n🎯 Key Metrics Summary:")
                            print(f"  📈 Progress: {data['progress']['progress_percentage']}%")
                            print(f"  🎯 Success Rate: {data['summary']['success_rate']}%")
                            print(f"  💰 Total Cost: {data['summary']['total_cost']}")
                            print(f"  ⏱️ Duration: {data['summary']['total_duration_minutes']} minutes")
                            print(f"  📞 Calls Remaining: {data['summary']['calls_remaining']}")
                            
                            # Show button states
                            buttons = data['button_states']
                            print(f"\n🔘 Action Buttons:")
                            for action, state in buttons.items():
                                status = "✅ Enabled" if state['enabled'] else "❌ Disabled"
                                print(f"  {action.title()}: {status} - {state['tooltip']}")
                            
                    else:
                        print(f"❌ Enhanced statistics failed: {enhanced_response.status_code}")
                        print(enhanced_response.text)
                else:
                    print("⚠️ No valid campaign ID found to test")
            
            else:
                print("⚠️ No campaigns found to test")
        
        else:
            print(f"❌ Failed to get campaigns: {campaigns_response.status_code}")
    
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - is the server running on port 9090?")
    except Exception as e:
        print(f"❌ Error: {e}")

def test_webhook_handler():
    """Test the enhanced webhook handler"""
    print("\n🧪 Testing Enhanced Webhook Handler")
    print("=" * 50)
    
    # Mock webhook data similar to Telnyx
    webhook_data = {
        "CallSid": "test_call_123",
        "CallbackSource": "call-progress-events",
        "CallStatus": "completed",
        "CallDuration": "45"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/webhooks/telnyx/call-status",
            json=webhook_data
        )
        
        if response.status_code == 200:
            print("✅ Webhook processed successfully")
            print(f"Response: {response.json()}")
        else:
            print(f"❌ Webhook failed: {response.status_code}")
            print(response.text)
    
    except Exception as e:
        print(f"❌ Webhook test error: {e}")

def test_call_logs():
    """Test call logs endpoint"""
    print("\n🧪 Testing Call Logs")
    print("=" * 50)
    
    try:
        response = requests.get(f"{BASE_URL}/api/call-logs")
        
        if response.status_code == 200:
            logs = response.json()
            print(f"📊 Found {len(logs)} call logs")
            
            if logs:
                # Show recent calls safely
                recent_calls = sorted(logs, key=lambda x: x.get('timestamp', '') if isinstance(x, dict) else '', reverse=True)[:5]
                print("\n📞 Recent Calls:")
                for call in recent_calls:
                    if isinstance(call, dict):
                        contact_name = call.get('contact_name', 'Unknown')
                        contact_phone = call.get('contact_phone', '')
                        status = call.get('status', 'Unknown')
                        print(f"  {contact_name} ({contact_phone}) - {status}")
                    else:
                        print(f"  Invalid call data: {call}")
        
        else:
            print(f"❌ Call logs failed: {response.status_code}")
    
    except Exception as e:
        print(f"❌ Call logs error: {e}")

def show_system_overview():
    """Show system overview"""
    print("\n🚀 Enhanced Campaign System Overview")
    print("=" * 50)
    print("✨ Features inspired by CALLBOT2.1:")
    print("  📊 Comprehensive campaign statistics")
    print("  🎯 Accurate call status tracking")
    print("  ♻️ Smart contact recycling")
    print("  🔘 Dynamic action button states")
    print("  📈 Real-time progress tracking")
    print("  💰 Cost analysis and estimation")
    print("  📞 Enhanced webhook handling")
    print("  🔍 Advanced call filtering")
    print("\n🌟 New Endpoints:")
    print("  GET /api/campaigns/{id}/statistics/enhanced")
    print("  POST /api/webhooks/telnyx/call-status")

if __name__ == "__main__":
    show_system_overview()
    
    # Wait a moment for server to start
    print("\n⏳ Waiting for server to start...")
    time.sleep(3)
    
    # Run tests
    test_enhanced_statistics()
    test_webhook_handler()
    test_call_logs()
    
    print("\n🎉 Testing Complete!")
    print("\nTo use the enhanced system:")
    print("1. Create/start a campaign")
    print("2. Use the enhanced statistics endpoint for better insights")
    print("3. The webhook handler will update call statuses in real-time")
    print("4. Use button states to guide campaign actions") 