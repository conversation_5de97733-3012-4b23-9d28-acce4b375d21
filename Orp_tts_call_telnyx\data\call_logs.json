[{"id": "8285a82b-c45a-479e-a5ae-8921664d35dc", "timestamp": "2025-06-08T20:21:53.311408", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749394306", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "v3:hHfJvb0yD6iF6GtaXPaeGhb_f5igS-BwFCAskKPnL69amrIiwUOhbQ", "call_session_id": "1ff18650-4478-11f0-9745-02420a1f0a70", "transfer_status": "failed", "webhook_events": [{"event_type": "call.initiated", "timestamp": "2025-06-08T20:21:59.076197", "payload": {"call_control_id": "v3:RK9wWT7dAQODE3f-Azx5Tw0ifCi9PsrNj32GwipOVw8CV1uHmfkcuQ", "call_leg_id": "1ff19050-4478-11f0-a2bb-02420a1f0a70", "call_session_id": "1ff18650-4478-11f0-9745-02420a1f0a70", "caller_id_name": "+19199256164", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "direction": "incoming", "from": "+19199256164", "from_sip_uri": "+<EMAIL>:9000", "offered_codecs": "G722,PCMU,PCMA", "start_time": "2025-06-08T14:51:57.083906Z", "state": "parked", "to": "+919552775831", "to_sip_uri": "+<EMAIL>"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T20:21:59.552222", "payload": {"call_control_id": "v3:hHfJvb0yD6iF6GtaXPaeGhb_f5igS-BwFCAskKPnL69amrIiwUOhbQ", "call_leg_id": "20654176-4478-11f0-b5e2-02420a1f0b70", "call_session_id": "1ff18650-4478-11f0-9745-02420a1f0a70", "client_state": null, "connection_id": "2702376459367876227", "direction": "outgoing", "from": "+19199256164", "state": "bridging", "to": "+919552775831"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T20:22:00.142064", "payload": {"call_control_id": "v3:hHfJvb0yD6iF6GtaXPaeGhb_f5igS-BwFCAskKPnL69amrIiwUOhbQ", "call_leg_id": "20654176-4478-11f0-b5e2-02420a1f0b70", "call_session_id": "1ff18650-4478-11f0-9745-02420a1f0a70", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T14:51:57.083906Z", "to": "+919552775831"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T20:22:00.171985", "payload": {"call_control_id": "v3:RK9wWT7dAQODE3f-Azx5Tw0ifCi9PsrNj32GwipOVw8CV1uHmfkcuQ", "call_leg_id": "1ff19050-4478-11f0-a2bb-02420a1f0a70", "call_session_id": "1ff18650-4478-11f0-9745-02420a1f0a70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T14:51:57.083906Z", "to": "+919552775831"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T20:22:29.546666", "payload": {"call_control_id": "v3:hHfJvb0yD6iF6GtaXPaeGhb_f5igS-BwFCAskKPnL69amrIiwUOhbQ", "call_leg_id": "20654176-4478-11f0-b5e2-02420a1f0b70", "call_quality_stats": {"inbound": {"jitter_max_variance": "5.37", "jitter_packet_count": "0", "mos": "4.49", "packet_count": "1461", "skip_packet_count": "5"}, "outbound": {"packet_count": "0", "skip_packet_count": "0"}}, "call_session_id": "1ff18650-4478-11f0-9745-02420a1f0a70", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T14:52:28.483904Z", "from": "+19199256164", "hangup_cause": "timeout", "hangup_source": "caller", "sip_hangup_cause": "487", "start_time": "2025-06-08T14:51:58.023907Z", "to": "+919552775831"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T20:24:58.080150", "payload": {"call_control_id": "v3:RK9wWT7dAQODE3f-Azx5Tw0ifCi9PsrNj32GwipOVw8CV1uHmfkcuQ", "call_leg_id": "1ff19050-4478-11f0-a2bb-02420a1f0a70", "call_quality_stats": {"inbound": {"jitter_max_variance": "0.00", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "0", "skip_packet_count": "8952"}, "outbound": {"packet_count": "1517", "skip_packet_count": "0"}}, "call_session_id": "1ff18650-4478-11f0-9745-02420a1f0a70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T14:54:57.063905Z", "from": "+19199256164", "hangup_cause": "originator_cancel", "hangup_source": "caller", "sip_hangup_cause": "487", "start_time": "2025-06-08T14:51:57.083906Z", "to": "+919552775831"}}], "bridged_at": "2025-06-08T20:22:00.170983", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:22:14.192787", "hangup_cause": "originator_cancel", "completed_at": "2025-06-08T20:24:58.079141"}, {"id": "5f2aea85-e305-477d-89ec-dbb197961825", "timestamp": "2025-06-08T20:21:53.321409", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749394306", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "v3:HbYz-avmosZyBA2UslI7T3hwF5jju97KhHqWLtnMWSWHFrGsU-lYPw", "call_session_id": "1f6dbbae-4478-11f0-8487-02420a1f0b70", "transfer_status": "failed", "webhook_events": [{"event_type": "call.initiated", "timestamp": "2025-06-08T20:21:58.496979", "payload": {"call_control_id": "v3:WO0oKDXl0llaJmiLrgI2yaeAg1XOkUCk317Gvy0eW57GbcYfeH2olg", "call_leg_id": "1f6dc5fe-4478-11f0-827d-02420a1f0b70", "call_session_id": "1f6dbbae-4478-11f0-8487-02420a1f0b70", "caller_id_name": "+18162196532", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "direction": "incoming", "from": "+18162196532", "from_sip_uri": "+<EMAIL>:9000", "offered_codecs": "G722,PCMU,PCMA", "start_time": "2025-06-08T14:51:56.223906Z", "state": "parked", "to": "+917420068477", "to_sip_uri": "+<EMAIL>"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T20:21:59.015061", "payload": {"call_control_id": "v3:HbYz-avmosZyBA2UslI7T3hwF5jju97KhHqWLtnMWSWHFrGsU-lYPw", "call_leg_id": "2016c172-4478-11f0-88b6-02420a1f0a70", "call_quality_stats": null, "call_session_id": "1f6dbbae-4478-11f0-8487-02420a1f0b70", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T14:51:57.883906Z", "from": "+18162196532", "hangup_cause": "not_found", "hangup_source": "unknown", "sip_hangup_cause": "404", "start_time": "2025-06-08T14:51:57.463906Z", "to": "+917420068477"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T20:21:59.743193", "payload": {"call_control_id": "v3:HbYz-avmosZyBA2UslI7T3hwF5jju97KhHqWLtnMWSWHFrGsU-lYPw", "call_leg_id": "2016c172-4478-11f0-88b6-02420a1f0a70", "call_session_id": "1f6dbbae-4478-11f0-8487-02420a1f0b70", "client_state": null, "connection_id": "2702376459367876227", "direction": "outgoing", "from": "+18162196532", "state": "bridging", "to": "+917420068477"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T20:24:57.434612", "payload": {"call_control_id": "v3:WO0oKDXl0llaJmiLrgI2yaeAg1XOkUCk317Gvy0eW57GbcYfeH2olg", "call_leg_id": "1f6dc5fe-4478-11f0-827d-02420a1f0b70", "call_quality_stats": {"inbound": {"jitter_max_variance": "0.00", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "0", "skip_packet_count": "8933"}, "outbound": {"packet_count": "21", "skip_packet_count": "0"}}, "call_session_id": "1f6dbbae-4478-11f0-8487-02420a1f0b70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T14:54:56.143906Z", "from": "+18162196532", "hangup_cause": "originator_cancel", "hangup_source": "caller", "sip_hangup_cause": "404", "start_time": "2025-06-08T14:51:56.223906Z", "to": "+917420068477"}}], "hangup_cause": "originator_cancel", "completed_at": "2025-06-08T20:24:57.433448"}]