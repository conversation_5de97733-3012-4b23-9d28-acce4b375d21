#!/usr/bin/env python3
"""
Test script for the new shared agent pool system
Tests that multiple agents can handle concurrent calls
"""

import requests
import json
import time
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:9090"

def test_shared_agent_pool():
    """Test the shared agent pool with concurrent calls"""
    
    logger.info("🧪 Testing shared agent pool system...")
    
    # Test data - 5 contacts to test concurrent handling
    test_campaign = {
        "name": "Shared Agent Pool Test",
        "agentId": "mia",
        "firstMessage": "Hello! This is a test call from our shared agent pool system.",
        "telnyxNumbers": ["+18162196532", "+19199256164"],
        "usePersonalizedAI": False,  # Enable shared AI mode
        "contacts": [
            {"name": "Test Contact 1", "phone_number": "+917420068477"},
            {"name": "Test Contact 2", "phone_number": "+919552775831"},
            {"name": "Test Contact 3", "phone_number": "+917420068478"},
            {"name": "Test Contact 4", "phone_number": "+919552775832"},
            {"name": "Test Contact 5", "phone_number": "+917420068479"}
        ]
    }
    
    try:
        # 1. Create campaign
        logger.info("📝 Creating test campaign...")
        response = requests.post(f"{BASE_URL}/api/campaigns", json=test_campaign)
        
        if response.status_code != 200:
            logger.error(f"❌ Failed to create campaign: {response.status_code} - {response.text}")
            return False
        
        campaign_data = response.json()
        campaign_id = campaign_data.get('campaign', {}).get('id')
        logger.info(f"✅ Campaign created: {campaign_id}")
        
        # 2. Start campaign (this should create shared agent pool)
        logger.info("🚀 Starting campaign with shared agent pool...")
        response = requests.post(f"{BASE_URL}/api/campaigns/{campaign_id}/start")
        
        if response.status_code != 200:
            logger.error(f"❌ Failed to start campaign: {response.status_code} - {response.text}")
            return False
        
        logger.info("✅ Campaign started successfully")
        
        # 3. Wait a bit for agents to initialize
        logger.info("⏳ Waiting for shared agent pool to initialize...")
        time.sleep(5)
        
        # 4. Check shared agent stats
        logger.info("📊 Checking shared agent pool stats...")
        response = requests.get(f"{BASE_URL}/api/shared-agents/stats")
        
        if response.status_code == 200:
            stats = response.json()
            logger.info(f"📊 Shared agent stats: {json.dumps(stats, indent=2)}")
            
            # Verify pool was created
            if campaign_id in stats:
                campaign_stats = stats[campaign_id]
                total_agents = campaign_stats.get('total_agents', 0)
                available_agents = campaign_stats.get('available_agents', 0)
                
                logger.info(f"✅ Agent pool found: {total_agents} total agents, {available_agents} available")
                
                if total_agents >= 3:  # Should have created at least 3 agents
                    logger.info("✅ Shared agent pool created successfully with multiple agents")
                    return True
                else:
                    logger.error(f"❌ Expected at least 3 agents, got {total_agents}")
                    return False
            else:
                logger.error(f"❌ No agent pool found for campaign {campaign_id}")
                return False
        else:
            logger.error(f"❌ Failed to get shared agent stats: {response.status_code}")
            return False
        
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {str(e)}")
        return False

def test_concurrent_calls():
    """Test that multiple calls can be handled concurrently"""
    
    logger.info("🧪 Testing concurrent call handling...")
    
    # Make multiple direct calls to test the system
    call_data_list = [
        {
            "phone_number": "+917420068477",
            "agent_name_for_dispatch": "shared_Mia_campaign_test_1",
            "campaign_id": "test_campaign",
            "ai_mode": "shared",
            "from_phone": "+18162196532",
            "contact_name": "Concurrent Test 1"
        },
        {
            "phone_number": "+919552775831", 
            "agent_name_for_dispatch": "shared_Mia_campaign_test_1",
            "campaign_id": "test_campaign",
            "ai_mode": "shared",
            "from_phone": "+19199256164",
            "contact_name": "Concurrent Test 2"
        }
    ]
    
    try:
        results = []
        
        # Make calls concurrently
        import threading
        
        def make_call(call_data):
            try:
                response = requests.post(f"{BASE_URL}/make_call", json=call_data, timeout=10)
                results.append({
                    'call_data': call_data,
                    'status_code': response.status_code,
                    'response': response.json() if response.status_code == 200 else response.text
                })
                logger.info(f"📞 Call result for {call_data['contact_name']}: {response.status_code}")
            except Exception as e:
                logger.error(f"❌ Call failed for {call_data['contact_name']}: {str(e)}")
                results.append({
                    'call_data': call_data,
                    'status_code': 'error',
                    'response': str(e)
                })
        
        # Start threads for concurrent calls
        threads = []
        for call_data in call_data_list:
            thread = threading.Thread(target=make_call, args=(call_data,))
            threads.append(thread)
        
        logger.info("🚀 Starting concurrent calls...")
        for thread in threads:
            thread.start()
            time.sleep(0.1)  # Small delay between starts
        
        # Wait for all calls to complete
        for thread in threads:
            thread.join()
        
        # Analyze results
        successful_calls = [r for r in results if r['status_code'] == 200]
        logger.info(f"✅ {len(successful_calls)}/{len(call_data_list)} calls completed successfully")
        
        for result in results:
            contact_name = result['call_data']['contact_name']
            status = result['status_code']
            logger.info(f"📞 {contact_name}: {status}")
        
        return len(successful_calls) == len(call_data_list)
        
    except Exception as e:
        logger.error(f"❌ Concurrent call test failed: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("🔬 Starting shared agent pool tests...")
    
    # Test 1: Shared agent pool creation
    test1_result = test_shared_agent_pool()
    logger.info(f"Test 1 (Agent Pool Creation): {'✅ PASSED' if test1_result else '❌ FAILED'}")
    
    # Small delay between tests
    time.sleep(2)
    
    # Test 2: Concurrent calls
    test2_result = test_concurrent_calls()
    logger.info(f"Test 2 (Concurrent Calls): {'✅ PASSED' if test2_result else '❌ FAILED'}")
    
    # Overall result
    overall_success = test1_result and test2_result
    logger.info(f"\n🏁 Overall Test Result: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    if overall_success:
        logger.info("🎉 Shared agent pool system is working correctly!")
    else:
        logger.info("🔧 Shared agent pool system needs fixing.") 
"""
Test script for the new shared agent pool system
Tests that multiple agents can handle concurrent calls
"""

import requests
import json
import time
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:9090"

def test_shared_agent_pool():
    """Test the shared agent pool with concurrent calls"""
    
    logger.info("🧪 Testing shared agent pool system...")
    
    # Test data - 5 contacts to test concurrent handling
    test_campaign = {
        "name": "Shared Agent Pool Test",
        "agentId": "mia",
        "firstMessage": "Hello! This is a test call from our shared agent pool system.",
        "telnyxNumbers": ["+18162196532", "+19199256164"],
        "usePersonalizedAI": False,  # Enable shared AI mode
        "contacts": [
            {"name": "Test Contact 1", "phone_number": "+917420068477"},
            {"name": "Test Contact 2", "phone_number": "+919552775831"},
            {"name": "Test Contact 3", "phone_number": "+917420068478"},
            {"name": "Test Contact 4", "phone_number": "+919552775832"},
            {"name": "Test Contact 5", "phone_number": "+917420068479"}
        ]
    }
    
    try:
        # 1. Create campaign
        logger.info("📝 Creating test campaign...")
        response = requests.post(f"{BASE_URL}/api/campaigns", json=test_campaign)
        
        if response.status_code != 200:
            logger.error(f"❌ Failed to create campaign: {response.status_code} - {response.text}")
            return False
        
        campaign_data = response.json()
        campaign_id = campaign_data.get('campaign', {}).get('id')
        logger.info(f"✅ Campaign created: {campaign_id}")
        
        # 2. Start campaign (this should create shared agent pool)
        logger.info("🚀 Starting campaign with shared agent pool...")
        response = requests.post(f"{BASE_URL}/api/campaigns/{campaign_id}/start")
        
        if response.status_code != 200:
            logger.error(f"❌ Failed to start campaign: {response.status_code} - {response.text}")
            return False
        
        logger.info("✅ Campaign started successfully")
        
        # 3. Wait a bit for agents to initialize
        logger.info("⏳ Waiting for shared agent pool to initialize...")
        time.sleep(5)
        
        # 4. Check shared agent stats
        logger.info("📊 Checking shared agent pool stats...")
        response = requests.get(f"{BASE_URL}/api/shared-agents/stats")
        
        if response.status_code == 200:
            stats = response.json()
            logger.info(f"📊 Shared agent stats: {json.dumps(stats, indent=2)}")
            
            # Verify pool was created
            if campaign_id in stats:
                campaign_stats = stats[campaign_id]
                total_agents = campaign_stats.get('total_agents', 0)
                available_agents = campaign_stats.get('available_agents', 0)
                
                logger.info(f"✅ Agent pool found: {total_agents} total agents, {available_agents} available")
                
                if total_agents >= 3:  # Should have created at least 3 agents
                    logger.info("✅ Shared agent pool created successfully with multiple agents")
                    return True
                else:
                    logger.error(f"❌ Expected at least 3 agents, got {total_agents}")
                    return False
            else:
                logger.error(f"❌ No agent pool found for campaign {campaign_id}")
                return False
        else:
            logger.error(f"❌ Failed to get shared agent stats: {response.status_code}")
            return False
        
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {str(e)}")
        return False

def test_concurrent_calls():
    """Test that multiple calls can be handled concurrently"""
    
    logger.info("🧪 Testing concurrent call handling...")
    
    # Make multiple direct calls to test the system
    call_data_list = [
        {
            "phone_number": "+917420068477",
            "agent_name_for_dispatch": "shared_Mia_campaign_test_1",
            "campaign_id": "test_campaign",
            "ai_mode": "shared",
            "from_phone": "+18162196532",
            "contact_name": "Concurrent Test 1"
        },
        {
            "phone_number": "+919552775831", 
            "agent_name_for_dispatch": "shared_Mia_campaign_test_1",
            "campaign_id": "test_campaign",
            "ai_mode": "shared",
            "from_phone": "+19199256164",
            "contact_name": "Concurrent Test 2"
        }
    ]
    
    try:
        results = []
        
        # Make calls concurrently
        import threading
        
        def make_call(call_data):
            try:
                response = requests.post(f"{BASE_URL}/make_call", json=call_data, timeout=10)
                results.append({
                    'call_data': call_data,
                    'status_code': response.status_code,
                    'response': response.json() if response.status_code == 200 else response.text
                })
                logger.info(f"📞 Call result for {call_data['contact_name']}: {response.status_code}")
            except Exception as e:
                logger.error(f"❌ Call failed for {call_data['contact_name']}: {str(e)}")
                results.append({
                    'call_data': call_data,
                    'status_code': 'error',
                    'response': str(e)
                })
        
        # Start threads for concurrent calls
        threads = []
        for call_data in call_data_list:
            thread = threading.Thread(target=make_call, args=(call_data,))
            threads.append(thread)
        
        logger.info("🚀 Starting concurrent calls...")
        for thread in threads:
            thread.start()
            time.sleep(0.1)  # Small delay between starts
        
        # Wait for all calls to complete
        for thread in threads:
            thread.join()
        
        # Analyze results
        successful_calls = [r for r in results if r['status_code'] == 200]
        logger.info(f"✅ {len(successful_calls)}/{len(call_data_list)} calls completed successfully")
        
        for result in results:
            contact_name = result['call_data']['contact_name']
            status = result['status_code']
            logger.info(f"📞 {contact_name}: {status}")
        
        return len(successful_calls) == len(call_data_list)
        
    except Exception as e:
        logger.error(f"❌ Concurrent call test failed: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("🔬 Starting shared agent pool tests...")
    
    # Test 1: Shared agent pool creation
    test1_result = test_shared_agent_pool()
    logger.info(f"Test 1 (Agent Pool Creation): {'✅ PASSED' if test1_result else '❌ FAILED'}")
    
    # Small delay between tests
    time.sleep(2)
    
    # Test 2: Concurrent calls
    test2_result = test_concurrent_calls()
    logger.info(f"Test 2 (Concurrent Calls): {'✅ PASSED' if test2_result else '❌ FAILED'}")
    
    # Overall result
    overall_success = test1_result and test2_result
    logger.info(f"\n🏁 Overall Test Result: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    if overall_success:
        logger.info("🎉 Shared agent pool system is working correctly!")
    else:
        logger.info("🔧 Shared agent pool system needs fixing.") 