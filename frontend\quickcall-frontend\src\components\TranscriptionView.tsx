"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { MessageCir<PERSON>, User, <PERSON><PERSON> } from "lucide-react"

interface TranscriptionViewProps {
  selectedAgentName: string
}

interface Message {
  id: string
  text: string
  sender: "user" | "agent"
  timestamp: Date
}

export default function TranscriptionView({ selectedAgentName }: TranscriptionViewProps) {
  const [messages, setMessages] = useState<Message[]>([])

  // Mock messages for demonstration
  useEffect(() => {
    const mockMessages: Message[] = [
      {
        id: "1",
        text: "Hello! How can I help you today?",
        sender: "agent",
        timestamp: new Date(Date.now() - 60000),
      },
      {
        id: "2",
        text: "I'd like to know more about your services.",
        sender: "user",
        timestamp: new Date(Date.now() - 30000),
      },
      {
        id: "3",
        text: "I'd be happy to help you learn about our services. What specific area are you interested in?",
        sender: "agent",
        timestamp: new Date(),
      },
    ]
    setMessages(mockMessages)
  }, [])

  if (messages.length === 0) {
    return (
      <div className="h-full flex flex-col items-center justify-center p-8 text-center">
        <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mb-4">
          <MessageCircle className="w-8 h-8 text-blue-600" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Start Your Conversation</h3>
        <p className="text-gray-600 text-sm max-w-sm">
          Your conversation with {selectedAgentName} will appear here once you start talking.
        </p>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      <div className="flex-1 overflow-y-auto p-6 space-y-4">
        <AnimatePresence initial={false}>
          {messages.map((message) => (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: 20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -20, scale: 0.95 }}
              transition={{ duration: 0.3, ease: [0.16, 1, 0.3, 1] }}
              className={`flex gap-3 ${message.sender === "user" ? "flex-row-reverse" : ""}`}
            >
              <div
                className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center shadow-sm ${
                  message.sender === "user"
                    ? "bg-gradient-to-br from-blue-500 to-purple-600"
                    : "bg-gradient-to-br from-green-500 to-emerald-600"
                }`}
              >
                {message.sender === "user" ? (
                  <User className="w-4 h-4 text-white" />
                ) : (
                  <Bot className="w-4 h-4 text-white" />
                )}
              </div>

              <div className={`flex-1 max-w-xs ${message.sender === "user" ? "text-right" : ""}`}>
                <div
                  className={`inline-block p-3 rounded-2xl shadow-sm ${
                    message.sender === "user"
                      ? "bg-gradient-to-br from-blue-500 to-purple-600 text-white rounded-br-md"
                      : "bg-white border border-gray-200 text-gray-900 rounded-bl-md"
                  }`}
                >
                  <p className="text-sm leading-relaxed">{message.text}</p>
                </div>
                <p className={`text-xs text-gray-500 mt-1 ${message.sender === "user" ? "text-right" : ""}`}>
                  {message.timestamp.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                </p>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>
    </div>
  )
}