#!/usr/bin/env python3
"""
Test script for Call Transfer functionality
Tests the automatic call transfer feature
"""

import requests
import json
import time

BASE_URL = "http://localhost:9090"

def test_call_transfer():
    """Test the call transfer functionality"""
    print("🔄 Testing Call Transfer Functionality")
    print("=" * 50)
    
    # Test data - simulating a real Telnyx webhook
    test_data = {
        "call_control_id": "test_call_123456",
        "to": "+917420068477",  # <PERSON>'s number
        "from": "+19858539054"  # One of your Telnyx numbers
    }
    
    try:
        print(f"📞 Testing transfer for call: {test_data['call_control_id']}")
        print(f"   From: {test_data['from']} → To: {test_data['to']}")
        
        response = requests.post(
            f"{BASE_URL}/api/test/transfer",
            json=test_data
        )
        
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                print("✅ Transfer test SUCCESSFUL!")
                print(f"   API Response: {result['message']}")
            else:
                print("❌ Transfer test FAILED!")
                print(f"   Error: {result.get('error', 'Unknown error')}")
        else:
            print(f"❌ Transfer test failed with status: {response.status_code}")
            print(f"   Response: {response.text}")
    
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - is the server running on port 9090?")
    except Exception as e:
        print(f"❌ Transfer test error: {e}")

def test_webhook_simulation():
    """Test with a simulated Telnyx webhook"""
    print("\n📡 Testing Webhook with Transfer")
    print("=" * 50)
    
    # Simulate real Telnyx webhook data
    webhook_data = {
        "data": {
            "event_type": "call.initiated",
            "payload": {
                "call_control_id": "real_call_789",
                "to": "+917420068477",
                "from": "+19858539054",
                "direction": "outbound",
                "state": "initiated"
            }
        }
    }
    
    try:
        print(f"📞 Simulating webhook for call: {webhook_data['data']['payload']['call_control_id']}")
        
        response = requests.post(
            f"{BASE_URL}/api/telnyx/webhook",
            json=webhook_data
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Webhook processed successfully!")
            print("   Transfer should have been triggered automatically")
        else:
            print(f"❌ Webhook failed with status: {response.status_code}")
    
    except Exception as e:
        print(f"❌ Webhook test error: {e}")

def show_transfer_info():
    """Show information about the transfer functionality"""
    print("🔄 Call Transfer System Overview")
    print("=" * 50)
    print("✨ Transfer Features:")
    print("  📞 Automatic call transfer on call.initiated")
    print("  🎯 Direct transfer to target numbers")
    print("  📡 Real-time webhook processing")
    print("  🔗 Uses Telnyx Transfer API")
    print("  📋 Comprehensive logging")
    print("\n🌟 How it works:")
    print("1. Call comes in → call.initiated webhook")
    print("2. System extracts call_control_id, to, from")
    print("3. Makes Telnyx transfer API call")
    print("4. Call is transferred to target number")
    print("5. Recipient receives the call!")

if __name__ == "__main__":
    show_transfer_info()
    
    print("\n⏳ Waiting for server to start...")
    time.sleep(2)
    
    # Run tests
    test_call_transfer()
    test_webhook_simulation()
    
    print("\n🎉 Transfer Testing Complete!")
    print("\n📋 Next Steps:")
    print("1. Ensure TELNYX_API_KEY is set in environment")
    print("2. Configure Telnyx webhook URL to point to your server")
    print("3. Make test calls to see automatic transfer in action")
    print("4. Monitor logs for transfer success/failure") 