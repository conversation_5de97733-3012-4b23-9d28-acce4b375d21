// API configuration

// This can be loaded from environment variables in production
// For example: process.env.NEXT_PUBLIC_API_BASE_URL
export const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:9090/api';

// API endpoints
export const ENDPOINTS = {
  // Agent endpoints
  AGENTS: {
    LIST: `${API_BASE_URL}/agents`,
    CREATE: `${API_BASE_URL}/agents`,
    UPDATE: (id: string) => `${API_BASE_URL}/agents/${id}`,
    DELETE: (id: string) => `${API_BASE_URL}/agents/${id}`,
  },
  
  // Call endpoints
  CALLS: {
    INITIATE: `${API_BASE_URL}/calls/initiate`,
    BATCH: `${API_BASE_URL}/calls/batch`,
    HISTORY: `${API_BASE_URL}/calls/history`,
  },
  
  // Voice endpoints
  VOICES: {
    LIST: `${API_BASE_URL}/voices`,
  },
  
  // Model endpoints
  MODELS: {
    LIST: `${API_BASE_URL}/models`,
  },
  
  // Process endpoints
  PROCESSES: {
    LIST: `${API_BASE_URL}/processes`,
    CREATE: `${API_BASE_URL}/processes`,
    GET: (id: string) => `${API_BASE_URL}/processes/${id}`,
    STOP: (id: string) => `${API_BASE_URL}/processes/${id}`,
  },
};
