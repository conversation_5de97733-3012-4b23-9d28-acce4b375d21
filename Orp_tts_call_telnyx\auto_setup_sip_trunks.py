#!/usr/bin/env python3
"""
Automatic SIP Trunk Setup Script

This script will automatically create SIP trunks for all your Telnyx numbers
and update the mapping in sip_trunks.json
"""

import requests
import json
import time

# Backend URL
BACKEND_URL = "http://localhost:9090"

# Your Telnyx numbers and base configuration
PHONE_NUMBERS = ["+19199256164", "+18162196532", "+19858539054"]

BASE_TRUNK_CONFIG = {
    "trunk": {
        "name": "cbtest",  # Will be modified for each number
        "address": "itscb.sip.telnyx.com",
        "numbers": [],  # Will be set for each number
        "auth_username": "itscb",
        "auth_password": "Iamcb123"
    }
}

def create_sip_trunk_for_number(phone_number):
    """Create SIP trunk for a specific phone number"""
    print(f"🚀 Creating SIP trunk for {phone_number}...")
    
    # Create trunk config for this number
    trunk_config = json.loads(json.dumps(BASE_TRUNK_CONFIG))  # Deep copy
    trunk_config["trunk"]["name"] = f"cbtest_{phone_number.replace('+', '').replace('-', '')}"
    trunk_config["trunk"]["numbers"] = [phone_number]
    
    # Make API call to auto-create endpoint
    try:
        response = requests.post(
            f"{BACKEND_URL}/api/sip-trunks/auto-create",
            json={
                "phone_number": phone_number,
                "trunk_config": trunk_config
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ SUCCESS: Created SIP trunk {result.get('sip_trunk_id')} for {phone_number}")
                return result.get('sip_trunk_id')
            else:
                print(f"❌ FAILED: {result.get('error')}")
                print(f"   LiveKit output: {result.get('livekit_output', 'N/A')}")
                return None
        else:
            print(f"❌ HTTP ERROR {response.status_code}: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ EXCEPTION: {str(e)}")
        return None

def main():
    """Main function to set up all SIP trunks"""
    print("🎯 Automatic SIP Trunk Setup")
    print("=" * 50)
    print("This script will create separate SIP trunks for each Telnyx number")
    print("=" * 50)
    
    results = {}
    
    for phone_number in PHONE_NUMBERS:
        sip_trunk_id = create_sip_trunk_for_number(phone_number)
        results[phone_number] = sip_trunk_id
        
        # Wait a moment between requests
        time.sleep(2)
    
    print("\n📊 FINAL RESULTS:")
    print("=" * 50)
    
    success_count = 0
    for phone_number, sip_trunk_id in results.items():
        if sip_trunk_id:
            print(f"✅ {phone_number} → {sip_trunk_id}")
            success_count += 1
        else:
            print(f"❌ {phone_number} → FAILED")
    
    print(f"\n🎉 Successfully created {success_count}/{len(PHONE_NUMBERS)} SIP trunks")
    
    if success_count == len(PHONE_NUMBERS):
        print("\n🚀 ALL DONE! Your calls will now originate from the correct phone numbers!")
        print("\nNext steps:")
        print("1. Your campaigns will automatically use the correct SIP trunks")
        print("2. Each call will appear to come from the right Telnyx number")
        print("3. The mapping has been automatically updated in data/sip_trunks.json")
    else:
        print(f"\n⚠️  {len(PHONE_NUMBERS) - success_count} trunks failed to create. Check the errors above.")

if __name__ == "__main__":
    main() 