[{"id": "bf8d4fa9-72b0-4622-8cb4-1c318b3aab01", "timestamp": "2025-06-08T19:31:31.505232", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749391194", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "v3:LOX3DwJkOsaCeT3_1HO9g9mlc13rK2zYQKqup7qAulQosmTjD0Xmlw", "call_session_id": "26f142ac-4477-11f0-ae69-02420a1f0a70", "transfer_status": "failed", "webhook_events": [{"event_type": "call.initiated", "timestamp": "2025-06-08T19:57:59.960947", "payload": {"call_control_id": "v3:ioOueX70z8PfESiwdMnKEyAJidar-8v8lWmz2BG-BxpPbp5ELw8wtA", "call_leg_id": "c5df6dec-4474-11f0-a9f2-02420a1f0d70", "call_session_id": "c5df6310-4474-11f0-b44d-02420a1f0d70", "caller_id_name": "+19199256164", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "direction": "incoming", "from": "+19199256164", "from_sip_uri": "+<EMAIL>:9000", "offered_codecs": "G722,PCMU,PCMA", "start_time": "2025-06-08T14:27:57.480517Z", "state": "parked", "to": "+919552775831", "to_sip_uri": "+<EMAIL>"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T19:58:00.590284", "payload": {"call_control_id": "v3:-sS8vv4N--8N5PAxdnk-pY-JsCIL7T0bDxB9tKldt4Ugnk0Yj4VV5A", "call_leg_id": "c68ee790-4474-11f0-90a6-02420a1f0a70", "call_session_id": "c5df6310-4474-11f0-b44d-02420a1f0d70", "client_state": null, "connection_id": "2702376459367876227", "direction": "outgoing", "from": "+19199256164", "state": "bridging", "to": "+919552775831"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T19:58:01.061927", "payload": {"call_control_id": "v3:ioOueX70z8PfESiwdMnKEyAJidar-8v8lWmz2BG-BxpPbp5ELw8wtA", "call_leg_id": "c5df6dec-4474-11f0-a9f2-02420a1f0d70", "call_session_id": "c5df6310-4474-11f0-b44d-02420a1f0d70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T14:27:57.480517Z", "to": "+919552775831"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T19:58:01.457182", "payload": {"call_control_id": "v3:-sS8vv4N--8N5PAxdnk-pY-JsCIL7T0bDxB9tKldt4Ugnk0Yj4VV5A", "call_leg_id": "c68ee790-4474-11f0-90a6-02420a1f0a70", "call_session_id": "c5df6310-4474-11f0-b44d-02420a1f0d70", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T14:27:57.480517Z", "to": "+919552775831"}}, {"event_type": "call.answered", "timestamp": "2025-06-08T19:58:22.284112", "payload": {"call_control_id": "v3:ioOueX70z8PfESiwdMnKEyAJidar-8v8lWmz2BG-BxpPbp5ELw8wtA", "call_leg_id": "c5df6dec-4474-11f0-a9f2-02420a1f0d70", "call_session_id": "c5df6310-4474-11f0-b44d-02420a1f0d70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T14:27:57.480517Z", "to": "+919552775831"}}, {"event_type": "call.answered", "timestamp": "2025-06-08T19:58:22.467180", "payload": {"call_control_id": "v3:-sS8vv4N--8N5PAxdnk-pY-JsCIL7T0bDxB9tKldt4Ugnk0Yj4VV5A", "call_leg_id": "c68ee790-4474-11f0-90a6-02420a1f0a70", "call_session_id": "c5df6310-4474-11f0-b44d-02420a1f0d70", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T14:27:58.920526Z", "to": "+919552775831"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T19:58:40.759367", "payload": {"call_control_id": "v3:-sS8vv4N--8N5PAxdnk-pY-JsCIL7T0bDxB9tKldt4Ugnk0Yj4VV5A", "call_leg_id": "c68ee790-4474-11f0-90a6-02420a1f0a70", "call_quality_stats": {"inbound": {"jitter_max_variance": "0.62", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "1971", "skip_packet_count": "15"}, "outbound": {"packet_count": "881", "skip_packet_count": "0"}}, "call_session_id": "c5df6310-4474-11f0-b44d-02420a1f0d70", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T14:28:39.620510Z", "from": "+19199256164", "hangup_cause": "normal_clearing", "hangup_source": "callee", "sip_hangup_cause": "200", "start_time": "2025-06-08T14:27:58.920526Z", "to": "+919552775831"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T19:58:41.142782", "payload": {"call_control_id": "v3:ioOueX70z8PfESiwdMnKEyAJidar-8v8lWmz2BG-BxpPbp5ELw8wtA", "call_leg_id": "c5df6dec-4474-11f0-a9f2-02420a1f0d70", "call_quality_stats": {"inbound": {"jitter_max_variance": "350.29", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "892", "skip_packet_count": "1150"}, "outbound": {"packet_count": "1990", "skip_packet_count": "0"}}, "call_session_id": "c5df6310-4474-11f0-b44d-02420a1f0d70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T14:28:39.620510Z", "from": "+19199256164", "hangup_cause": "normal_clearing", "hangup_source": "callee", "sip_hangup_cause": "200", "start_time": "2025-06-08T14:27:57.480517Z", "to": "+919552775831"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T20:12:57.821048", "payload": {"call_control_id": "v3:tM4tVRoGsrljrbZxb-NzLVOVEvoU2PYUJ0s8_GzBRLTeRW-BZi6EXA", "call_leg_id": "dd2b4898-4476-11f0-b127-02420a1f0b70", "call_session_id": "dd2b414a-4476-11f0-a3e6-02420a1f0b70", "caller_id_name": "+19199256164", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "direction": "incoming", "from": "+19199256164", "from_sip_uri": "+<EMAIL>:9000", "offered_codecs": "G722,PCMU,PCMA", "start_time": "2025-06-08T14:42:55.568359Z", "state": "parked", "to": "+919552775831", "to_sip_uri": "+<EMAIL>"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T20:12:59.257755", "payload": {"call_control_id": "v3:srt2GJtIBWKeXqn2_aopyNQn2Aldpe_IoXghtsssMVPxlNr7RETIAg", "call_leg_id": "ddd22546-4476-11f0-9465-02420a1f0a70", "call_session_id": "dd2b414a-4476-11f0-a3e6-02420a1f0b70", "client_state": null, "connection_id": "2702376459367876227", "direction": "outgoing", "from": "+19199256164", "state": "bridging", "to": "+919552775831"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T20:12:59.382625", "payload": {"call_control_id": "v3:srt2GJtIBWKeXqn2_aopyNQn2Aldpe_IoXghtsssMVPxlNr7RETIAg", "call_leg_id": "ddd22546-4476-11f0-9465-02420a1f0a70", "call_session_id": "dd2b414a-4476-11f0-a3e6-02420a1f0b70", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T14:42:55.568359Z", "to": "+919552775831"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T20:12:59.642281", "payload": {"call_control_id": "v3:tM4tVRoGsrljrbZxb-NzLVOVEvoU2PYUJ0s8_GzBRLTeRW-BZi6EXA", "call_leg_id": "dd2b4898-4476-11f0-b127-02420a1f0b70", "call_session_id": "dd2b414a-4476-11f0-a3e6-02420a1f0b70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T14:42:55.568359Z", "to": "+919552775831"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T20:13:27.487650", "payload": {"call_control_id": "v3:srt2GJtIBWKeXqn2_aopyNQn2Aldpe_IoXghtsssMVPxlNr7RETIAg", "call_leg_id": "ddd22546-4476-11f0-9465-02420a1f0a70", "call_quality_stats": {"inbound": {"jitter_max_variance": "0.65", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "1424", "skip_packet_count": "2"}, "outbound": {"packet_count": "0", "skip_packet_count": "0"}}, "call_session_id": "dd2b414a-4476-11f0-a3e6-02420a1f0b70", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T14:43:26.488366Z", "from": "+19199256164", "hangup_cause": "timeout", "hangup_source": "caller", "sip_hangup_cause": "487", "start_time": "2025-06-08T14:42:56.788341Z", "to": "+919552775831"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T20:15:02.012738", "payload": {"call_control_id": "v3:dC7bRUA6s-OoAYKwoGlOQBjqUvwaZl6TgZXwTkp-XF13A4CJiaRhfA", "call_leg_id": "26f14fea-4477-11f0-b2e8-02420a1f0a70", "call_session_id": "26f142ac-4477-11f0-ae69-02420a1f0a70", "caller_id_name": "+19199256164", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "direction": "incoming", "from": "+19199256164", "from_sip_uri": "+<EMAIL>:9000", "offered_codecs": "G722,PCMU,PCMA", "start_time": "2025-06-08T14:44:59.343731Z", "state": "parked", "to": "+919552775831", "to_sip_uri": "+<EMAIL>"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T20:15:03.296576", "payload": {"call_control_id": "v3:dC7bRUA6s-OoAYKwoGlOQBjqUvwaZl6TgZXwTkp-XF13A4CJiaRhfA", "call_leg_id": "26f14fea-4477-11f0-b2e8-02420a1f0a70", "call_session_id": "26f142ac-4477-11f0-ae69-02420a1f0a70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T14:44:59.343731Z", "to": "+919552775831"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T20:15:03.324507", "payload": {"call_control_id": "v3:LOX3DwJkOsaCeT3_1HO9g9mlc13rK2zYQKqup7qAulQosmTjD0Xmlw", "call_leg_id": "27d8c3fc-4477-11f0-b531-02420a1f0a70", "call_session_id": "26f142ac-4477-11f0-ae69-02420a1f0a70", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T14:44:59.343731Z", "to": "+919552775831"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T20:15:03.560017", "payload": {"call_control_id": "v3:LOX3DwJkOsaCeT3_1HO9g9mlc13rK2zYQKqup7qAulQosmTjD0Xmlw", "call_leg_id": "27d8c3fc-4477-11f0-b531-02420a1f0a70", "call_session_id": "26f142ac-4477-11f0-ae69-02420a1f0a70", "client_state": null, "connection_id": "2702376459367876227", "direction": "outgoing", "from": "+19199256164", "state": "bridging", "to": "+919552775831"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T20:15:31.264166", "payload": {"call_control_id": "v3:LOX3DwJkOsaCeT3_1HO9g9mlc13rK2zYQKqup7qAulQosmTjD0Xmlw", "call_leg_id": "27d8c3fc-4477-11f0-b531-02420a1f0a70", "call_quality_stats": {"inbound": {"jitter_max_variance": "0.78", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "1384", "skip_packet_count": "2"}, "outbound": {"packet_count": "0", "skip_packet_count": "0"}}, "call_session_id": "26f142ac-4477-11f0-ae69-02420a1f0a70", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T14:45:30.003724Z", "from": "+19199256164", "hangup_cause": "timeout", "hangup_source": "caller", "sip_hangup_cause": "487", "start_time": "2025-06-08T14:45:00.963728Z", "to": "+919552775831"}}], "bridged_at": "2025-06-08T20:15:03.313493", "answered_at": "2025-06-08T19:58:22.457133", "hangup_cause": "timeout", "completed_at": "2025-06-08T20:15:31.252100", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:15:14.213532"}, {"id": "fe6ffe79-bcc4-4852-9cae-e23e36d1c67f", "timestamp": "2025-06-08T19:31:31.380484", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749391194", "status": "answered", "notes": "Demo request", "call_duration": 175, "recording_url": "", "telnyx_call_id": "v3:UoErORpcjmdtF9Is8SLB3oAmnBgmbPw_CgAXYjmA8vbheCukT0Cf1Q", "call_session_id": "26fc6e98-4477-11f0-8d8b-02420a1f0a70", "transfer_status": "failed", "webhook_events": [{"event_type": "call.initiated", "timestamp": "2025-06-08T19:31:39.206698", "payload": {"call_control_id": "v3:VlYDzbAH-BcKtnvMqwQCoC3_JdzlDuKV5Utmtf20OhjhnhelLTY8VA", "call_leg_id": "17614ff4-4471-11f0-9f83-02420a1f1070", "call_session_id": "176145e0-4471-11f0-9fca-02420a1f1070", "caller_id_name": "+18162196532", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "direction": "incoming", "from": "+18162196532", "from_sip_uri": "+<EMAIL>:9000", "offered_codecs": "G722,PCMU,PCMA", "start_time": "2025-06-08T14:01:36.240364Z", "state": "parked", "to": "+917420068477", "to_sip_uri": "+<EMAIL>"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T19:31:40.437721", "payload": {"call_control_id": "v3:ty5mQhTDhkXMhR2ziRZKm9X6hezKYQzO2HZvAEfD0e9_xIIbrZCAJQ", "call_leg_id": "1867702c-4471-11f0-b157-02420a1f0a70", "call_session_id": "176145e0-4471-11f0-9fca-02420a1f1070", "client_state": null, "connection_id": "2702376459367876227", "direction": "outgoing", "from": "+18162196532", "state": "bridging", "to": "+917420068477"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T19:31:40.550338", "payload": {"call_control_id": "v3:VlYDzbAH-BcKtnvMqwQCoC3_JdzlDuKV5Utmtf20OhjhnhelLTY8VA", "call_leg_id": "17614ff4-4471-11f0-9f83-02420a1f1070", "call_session_id": "176145e0-4471-11f0-9fca-02420a1f1070", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+18162196532", "start_time": "2025-06-08T14:01:36.240364Z", "to": "+917420068477"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T19:31:40.574529", "payload": {"call_control_id": "v3:ty5mQhTDhkXMhR2ziRZKm9X6hezKYQzO2HZvAEfD0e9_xIIbrZCAJQ", "call_leg_id": "1867702c-4471-11f0-b157-02420a1f0a70", "call_session_id": "176145e0-4471-11f0-9fca-02420a1f1070", "client_state": null, "connection_id": "2702376459367876227", "from": "+18162196532", "start_time": "2025-06-08T14:01:36.240364Z", "to": "+917420068477"}}, {"event_type": "call.answered", "timestamp": "2025-06-08T19:31:49.073495", "payload": {"call_control_id": "v3:VlYDzbAH-BcKtnvMqwQCoC3_JdzlDuKV5Utmtf20OhjhnhelLTY8VA", "call_leg_id": "17614ff4-4471-11f0-9f83-02420a1f1070", "call_session_id": "176145e0-4471-11f0-9fca-02420a1f1070", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+18162196532", "start_time": "2025-06-08T14:01:36.240364Z", "to": "+917420068477"}}, {"event_type": "call.answered", "timestamp": "2025-06-08T19:31:49.218357", "payload": {"call_control_id": "v3:ty5mQhTDhkXMhR2ziRZKm9X6hezKYQzO2HZvAEfD0e9_xIIbrZCAJQ", "call_leg_id": "1867702c-4471-11f0-b157-02420a1f0a70", "call_session_id": "176145e0-4471-11f0-9fca-02420a1f1070", "client_state": null, "connection_id": "2702376459367876227", "from": "+18162196532", "start_time": "2025-06-08T14:01:38.180355Z", "to": "+917420068477"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T19:34:44.314751", "payload": {"call_control_id": "v3:ty5mQhTDhkXMhR2ziRZKm9X6hezKYQzO2HZvAEfD0e9_xIIbrZCAJQ", "call_leg_id": "1867702c-4471-11f0-b157-02420a1f0a70", "call_quality_stats": {"inbound": {"jitter_max_variance": "211.24", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "9166", "skip_packet_count": "13"}, "outbound": {"packet_count": "8716", "skip_packet_count": "0"}}, "call_session_id": "176145e0-4471-11f0-9fca-02420a1f1070", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T14:04:43.000361Z", "from": "+18162196532", "hangup_cause": "normal_clearing", "hangup_source": "callee", "sip_hangup_cause": "200", "start_time": "2025-06-08T14:01:38.180355Z", "to": "+917420068477"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T19:34:44.727103", "payload": {"call_control_id": "v3:VlYDzbAH-BcKtnvMqwQCoC3_JdzlDuKV5Utmtf20OhjhnhelLTY8VA", "call_leg_id": "17614ff4-4471-11f0-9f83-02420a1f1070", "call_quality_stats": {"inbound": {"jitter_max_variance": "433.33", "jitter_packet_count": "0", "mos": "4.49", "packet_count": "8734", "skip_packet_count": "514"}, "outbound": {"packet_count": "9192", "skip_packet_count": "0"}}, "call_session_id": "176145e0-4471-11f0-9fca-02420a1f1070", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T14:04:43.000361Z", "from": "+18162196532", "hangup_cause": "normal_clearing", "hangup_source": "callee", "sip_hangup_cause": "200", "start_time": "2025-06-08T14:01:36.240364Z", "to": "+917420068477"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T19:58:00.568871", "payload": {"call_control_id": "v3:22WTRQBqtLosvln27ONQaP1u2NOwlGqi2mSU_fZBTgZVj3GtEAwlIg", "call_leg_id": "c623fd18-4474-11f0-82be-02420a1f0d70", "call_session_id": "c623f156-4474-11f0-aa68-02420a1f0d70", "caller_id_name": "+18162196532", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "direction": "incoming", "from": "+18162196532", "from_sip_uri": "+<EMAIL>:9000", "offered_codecs": "G722,PCMU,PCMA", "start_time": "2025-06-08T14:27:57.940549Z", "state": "parked", "to": "+917420068477", "to_sip_uri": "+<EMAIL>"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T19:58:01.749607", "payload": {"call_control_id": "v3:w6oateaX4HqVPJrHGXMHA1H5XkL8EtIyCueSh2PaH71hNVay0rLUKg", "call_leg_id": "c6f3c110-4474-11f0-ae6a-02420a1f0a70", "call_session_id": "c623f156-4474-11f0-aa68-02420a1f0d70", "client_state": null, "connection_id": "2702376459367876227", "direction": "outgoing", "from": "+18162196532", "state": "bridging", "to": "+917420068477"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T19:58:01.927693", "payload": {"call_control_id": "v3:22WTRQBqtLosvln27ONQaP1u2NOwlGqi2mSU_fZBTgZVj3GtEAwlIg", "call_leg_id": "c623fd18-4474-11f0-82be-02420a1f0d70", "call_session_id": "c623f156-4474-11f0-aa68-02420a1f0d70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+18162196532", "start_time": "2025-06-08T14:27:57.940549Z", "to": "+917420068477"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T19:58:02.218414", "payload": {"call_control_id": "v3:w6oateaX4HqVPJrHGXMHA1H5XkL8EtIyCueSh2PaH71hNVay0rLUKg", "call_leg_id": "c6f3c110-4474-11f0-ae6a-02420a1f0a70", "call_session_id": "c623f156-4474-11f0-aa68-02420a1f0d70", "client_state": null, "connection_id": "2702376459367876227", "from": "+18162196532", "start_time": "2025-06-08T14:27:57.940549Z", "to": "+917420068477"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T19:58:30.288901", "payload": {"call_control_id": "v3:w6oateaX4HqVPJrHGXMHA1H5XkL8EtIyCueSh2PaH71hNVay0rLUKg", "call_leg_id": "c6f3c110-4474-11f0-ae6a-02420a1f0a70", "call_quality_stats": {"inbound": {"jitter_max_variance": "7.21", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "1416", "skip_packet_count": "2"}, "outbound": {"packet_count": "0", "skip_packet_count": "0"}}, "call_session_id": "c623f156-4474-11f0-aa68-02420a1f0d70", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T14:28:29.280517Z", "from": "+18162196532", "hangup_cause": "timeout", "hangup_source": "caller", "sip_hangup_cause": "487", "start_time": "2025-06-08T14:27:59.500515Z", "to": "+917420068477"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T20:00:59.135539", "payload": {"call_control_id": "v3:22WTRQBqtLosvln27ONQaP1u2NOwlGqi2mSU_fZBTgZVj3GtEAwlIg", "call_leg_id": "c623fd18-4474-11f0-82be-02420a1f0d70", "call_quality_stats": {"inbound": {"jitter_max_variance": "0.00", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "0", "skip_packet_count": "8914"}, "outbound": {"packet_count": "1486", "skip_packet_count": "0"}}, "call_session_id": "c623f156-4474-11f0-aa68-02420a1f0d70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T14:30:57.800569Z", "from": "+18162196532", "hangup_cause": "originator_cancel", "hangup_source": "caller", "sip_hangup_cause": "487", "start_time": "2025-06-08T14:27:57.940549Z", "to": "+917420068477"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T20:12:58.186696", "payload": {"call_control_id": "v3:SoNHLFh_NUApLmEcrXaJuv-VQ-awXeEYWov45vZSOPVWpQ6zMZyy4g", "call_leg_id": "dccc421c-4476-11f0-9b2d-02420a1f0b70", "call_session_id": "dccc37d6-4476-11f0-8580-02420a1f0b70", "caller_id_name": "+18162196532", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "direction": "incoming", "from": "+18162196532", "from_sip_uri": "+<EMAIL>:9000", "offered_codecs": "G722,PCMU,PCMA", "start_time": "2025-06-08T14:42:54.948365Z", "state": "parked", "to": "+917420068477", "to_sip_uri": "+<EMAIL>"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T20:12:58.817885", "payload": {"call_control_id": "v3:txHAdD8yzncD-BwOPnQF8ufxA3ZTvvjEjX5JrD8MGeL8kLToMpj_Hg", "call_leg_id": "de05c1da-4476-11f0-9b7f-02420a1f0b70", "call_quality_stats": null, "call_session_id": "dccc37d6-4476-11f0-8580-02420a1f0b70", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T14:42:57.568269Z", "from": "+18162196532", "hangup_cause": "not_found", "hangup_source": "unknown", "sip_hangup_cause": "404", "start_time": "2025-06-08T14:42:57.128367Z", "to": "+917420068477"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T20:12:59.035060", "payload": {"call_control_id": "v3:txHAdD8yzncD-BwOPnQF8ufxA3ZTvvjEjX5JrD8MGeL8kLToMpj_Hg", "call_leg_id": "de05c1da-4476-11f0-9b7f-02420a1f0b70", "call_session_id": "dccc37d6-4476-11f0-8580-02420a1f0b70", "client_state": null, "connection_id": "2702376459367876227", "direction": "outgoing", "from": "+18162196532", "state": "bridging", "to": "+917420068477"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T20:15:01.637726", "payload": {"call_control_id": "v3:dvG-IZIeQ9ITk1hPlPXAurCpiMtCJgENfwvyrG_1f25V0DcKwznT-w", "call_leg_id": "26fc7d84-4477-11f0-b488-02420a1f0a70", "call_session_id": "26fc6e98-4477-11f0-8d8b-02420a1f0a70", "caller_id_name": "+18162196532", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "direction": "incoming", "from": "+18162196532", "from_sip_uri": "+<EMAIL>:9000", "offered_codecs": "G722,PCMU,PCMA", "start_time": "2025-06-08T14:44:59.408269Z", "state": "parked", "to": "+917420068477", "to_sip_uri": "+<EMAIL>"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T20:15:02.028832", "payload": {"call_control_id": "v3:UoErORpcjmdtF9Is8SLB3oAmnBgmbPw_CgAXYjmA8vbheCukT0Cf1Q", "call_leg_id": "279ce3e6-4477-11f0-bb15-02420a1f0a70", "call_quality_stats": null, "call_session_id": "26fc6e98-4477-11f0-8d8b-02420a1f0a70", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T14:45:01.008270Z", "from": "+18162196532", "hangup_cause": "not_found", "hangup_source": "unknown", "sip_hangup_cause": "404", "start_time": "2025-06-08T14:45:00.608270Z", "to": "+917420068477"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T20:15:02.413595", "payload": {"call_control_id": "v3:UoErORpcjmdtF9Is8SLB3oAmnBgmbPw_CgAXYjmA8vbheCukT0Cf1Q", "call_leg_id": "279ce3e6-4477-11f0-bb15-02420a1f0a70", "call_session_id": "26fc6e98-4477-11f0-8d8b-02420a1f0a70", "client_state": null, "connection_id": "2702376459367876227", "direction": "outgoing", "from": "+18162196532", "state": "bridging", "to": "+917420068477"}}], "bridged_at": "2025-06-08T19:58:02.208350", "answered_at": "2025-06-08T19:31:49.209348", "hangup_cause": "not_found", "completed_at": "2025-06-08T20:15:02.005736", "telnyx_status": "call", "is_alive": false, "real_call_duration": 175, "start_time": "2025-06-08T14:01:48Z", "end_time": "2025-06-08T14:04:43Z", "telnyx_updated": "2025-06-08T19:36:27.108045", "accurate_status": "completed", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:15:14.214534"}, {"id": "b5a6943c-3db8-4025-bb60-3ba4302c114b", "timestamp": "2025-06-08T19:24:21.222936", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19858539054", "agent_name": "<PERSON>", "campaign_id": "campaign_1749390536", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.182281"}, {"id": "3300cba1-a99c-4dce-9c1c-cf392913f942", "timestamp": "2025-06-08T19:24:20.835883", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749390536", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.182281"}, {"id": "7f78099e-a7e3-40b4-8608-90c06f612fed", "timestamp": "2025-06-08T19:19:13.695884", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749390536", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.182281"}, {"id": "172da5c1-655f-4064-b8db-6b7493c8fb3f", "timestamp": "2025-06-08T19:19:06.814746", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19858539054", "agent_name": "<PERSON>", "campaign_id": "campaign_1749390536", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.182281"}, {"id": "862c4ddd-b342-4c63-9930-aa0780fa2e03", "timestamp": "2025-06-08T19:03:47.848745", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749389618", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.183283"}, {"id": "69ab79c6-9f4a-4671-ba50-7a5dbb336c98", "timestamp": "2025-06-08T19:03:46.921523", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749389618", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.183283"}, {"id": "6c9ee30c-50b6-4128-8095-e2ce7615792c", "timestamp": "2025-06-08T19:02:51.198864", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19858539054", "agent_name": "<PERSON>", "campaign_id": "campaign_1749389494", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.183283"}, {"id": "e033281f-01c4-4921-a544-d44108e2add1", "timestamp": "2025-06-08T19:02:51.195871", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749389494", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.183283"}, {"id": "1a37c70b-5b55-4096-9099-3a672fa19476", "timestamp": "2025-06-08T19:01:40.933208", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749389494", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "v3:All_2EVk0_UhLzYBlaynLT1d1nABWQxixLb2PVY-5u7CJj5AuOFImw", "call_session_id": "176201e2-4471-11f0-8441-02420a1f1070", "transfer_status": "failed", "webhook_events": [{"event_type": "call.initiated", "timestamp": "2025-06-08T19:31:38.506975", "payload": {"call_control_id": "v3:SsJ8FnVtBinoWmbLL5isCTrdNO-0X68MTxWmwk_iK6NoWPR_ClUIlQ", "call_leg_id": "176207dc-4471-11f0-91dc-02420a1f1070", "call_session_id": "176201e2-4471-11f0-8441-02420a1f1070", "caller_id_name": "+19199256164", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "direction": "incoming", "from": "+19199256164", "from_sip_uri": "+<EMAIL>:9000", "offered_codecs": "G722,PCMU,PCMA", "start_time": "2025-06-08T14:01:36.263693Z", "state": "parked", "to": "+919552775831", "to_sip_uri": "+<EMAIL>"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T19:31:39.800044", "payload": {"call_control_id": "v3:All_2EVk0_UhLzYBlaynLT1d1nABWQxixLb2PVY-5u7CJj5AuOFImw", "call_leg_id": "17ffdf0c-4471-11f0-bf7f-02420a1f0a70", "call_session_id": "176201e2-4471-11f0-8441-02420a1f1070", "client_state": null, "connection_id": "2702376459367876227", "direction": "outgoing", "from": "+19199256164", "state": "bridging", "to": "+919552775831"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T19:31:39.850077", "payload": {"call_control_id": "v3:SsJ8FnVtBinoWmbLL5isCTrdNO-0X68MTxWmwk_iK6NoWPR_ClUIlQ", "call_leg_id": "176207dc-4471-11f0-91dc-02420a1f1070", "call_session_id": "176201e2-4471-11f0-8441-02420a1f1070", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T14:01:36.263693Z", "to": "+919552775831"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T19:31:40.082244", "payload": {"call_control_id": "v3:All_2EVk0_UhLzYBlaynLT1d1nABWQxixLb2PVY-5u7CJj5AuOFImw", "call_leg_id": "17ffdf0c-4471-11f0-bf7f-02420a1f0a70", "call_session_id": "176201e2-4471-11f0-8441-02420a1f1070", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T14:01:36.263693Z", "to": "+919552775831"}}, {"event_type": "call.answered", "timestamp": "2025-06-08T19:31:53.163202", "payload": {"call_control_id": "v3:SsJ8FnVtBinoWmbLL5isCTrdNO-0X68MTxWmwk_iK6NoWPR_ClUIlQ", "call_leg_id": "176207dc-4471-11f0-91dc-02420a1f1070", "call_session_id": "176201e2-4471-11f0-8441-02420a1f1070", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T14:01:36.263693Z", "to": "+919552775831"}}, {"event_type": "call.answered", "timestamp": "2025-06-08T19:31:53.351710", "payload": {"call_control_id": "v3:All_2EVk0_UhLzYBlaynLT1d1nABWQxixLb2PVY-5u7CJj5AuOFImw", "call_leg_id": "17ffdf0c-4471-11f0-bf7f-02420a1f0a70", "call_session_id": "176201e2-4471-11f0-8441-02420a1f1070", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T14:01:37.463701Z", "to": "+919552775831"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T19:31:56.944403", "payload": {"call_control_id": "v3:All_2EVk0_UhLzYBlaynLT1d1nABWQxixLb2PVY-5u7CJj5AuOFImw", "call_leg_id": "17ffdf0c-4471-11f0-bf7f-02420a1f0a70", "call_quality_stats": {"inbound": {"jitter_max_variance": "800.00", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "834", "skip_packet_count": "14"}, "outbound": {"packet_count": "128", "skip_packet_count": "0"}}, "call_session_id": "176201e2-4471-11f0-8441-02420a1f1070", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T14:01:55.623704Z", "from": "+19199256164", "hangup_cause": "normal_clearing", "hangup_source": "callee", "sip_hangup_cause": "200", "start_time": "2025-06-08T14:01:37.463701Z", "to": "+919552775831"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T19:31:56.968989", "payload": {"call_control_id": "v3:SsJ8FnVtBinoWmbLL5isCTrdNO-0X68MTxWmwk_iK6NoWPR_ClUIlQ", "call_leg_id": "176207dc-4471-11f0-91dc-02420a1f1070", "call_quality_stats": {"inbound": {"jitter_max_variance": "110.83", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "139", "skip_packet_count": "774"}, "outbound": {"packet_count": "857", "skip_packet_count": "0"}}, "call_session_id": "176201e2-4471-11f0-8441-02420a1f1070", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T14:01:55.643691Z", "from": "+19199256164", "hangup_cause": "normal_clearing", "hangup_source": "callee", "sip_hangup_cause": "200", "start_time": "2025-06-08T14:01:36.263693Z", "to": "+919552775831"}}], "bridged_at": "2025-06-08T19:31:40.072248", "answered_at": "2025-06-08T19:31:53.343164", "hangup_cause": "normal_clearing", "completed_at": "2025-06-08T19:31:56.957967"}, {"id": "4bdf00a6-b492-496c-ac91-53abb9949cce", "timestamp": "2025-06-08T19:01:40.910340", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19858539054", "agent_name": "<PERSON>", "campaign_id": "campaign_1749389494", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.183283"}, {"id": "95bc803b-3134-4016-9959-0aea7bd51a1e", "timestamp": "2025-06-08T18:18:27.686751", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749386900", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "v3:iXQZ95GEQ1cgi7mPv8ZVCroVrypC96kmq-AT0f-8ZmgHUDOJ_4sIeg", "call_session_id": "e4366506-4466-11f0-8ec4-02420a1f0b70", "transfer_status": "failed", "webhook_events": [{"event_type": "call.initiated", "timestamp": "2025-06-08T18:18:35.945002", "payload": {"call_control_id": "v3:iXQZ95GEQ1cgi7mPv8ZVCroVrypC96kmq-AT0f-8ZmgHUDOJ_4sIeg", "call_leg_id": "e4367406-4466-11f0-88fd-02420a1f0b70", "call_session_id": "e4366506-4466-11f0-8ec4-02420a1f0b70", "caller_id_name": "+19199256164", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "direction": "incoming", "from": "+19199256164", "from_sip_uri": "+<EMAIL>:9000", "offered_codecs": "G722,PCMU,PCMA", "start_time": "2025-06-08T12:48:35.423737Z", "state": "parked", "to": "+919552775831", "to_sip_uri": "+<EMAIL>"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T18:18:37.050228", "payload": {"call_control_id": "v3:iXQZ95GEQ1cgi7mPv8ZVCroVrypC96kmq-AT0f-8ZmgHUDOJ_4sIeg", "call_leg_id": "e4367406-4466-11f0-88fd-02420a1f0b70", "call_session_id": "e4366506-4466-11f0-8ec4-02420a1f0b70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T12:48:35.423737Z", "to": "+919552775831"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T18:18:37.104510", "payload": {"call_control_id": "v3:mBhjv6JrxBJuxBGJEvAcdiTWwEYqr1__eJNcwdTf20w-KIx52lLCTA", "call_leg_id": "e4da59b8-4466-11f0-bc7a-02420a1f0a70", "call_session_id": "e4366506-4466-11f0-8ec4-02420a1f0b70", "client_state": null, "connection_id": "2702376459367876227", "direction": "outgoing", "from": "+19199256164", "state": "bridging", "to": "+919552775831"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T18:18:37.353732", "payload": {"call_control_id": "v3:mBhjv6JrxBJuxBGJEvAcdiTWwEYqr1__eJNcwdTf20w-KIx52lLCTA", "call_leg_id": "e4da59b8-4466-11f0-bc7a-02420a1f0a70", "call_session_id": "e4366506-4466-11f0-8ec4-02420a1f0b70", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T12:48:35.423737Z", "to": "+919552775831"}}, {"event_type": "call.answered", "timestamp": "2025-06-08T18:18:52.456204", "payload": {"call_control_id": "v3:mBhjv6JrxBJuxBGJEvAcdiTWwEYqr1__eJNcwdTf20w-KIx52lLCTA", "call_leg_id": "e4da59b8-4466-11f0-bc7a-02420a1f0a70", "call_session_id": "e4366506-4466-11f0-8ec4-02420a1f0b70", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T12:48:36.663725Z", "to": "+919552775831"}}, {"event_type": "call.answered", "timestamp": "2025-06-08T18:18:52.465468", "payload": {"call_control_id": "v3:iXQZ95GEQ1cgi7mPv8ZVCroVrypC96kmq-AT0f-8ZmgHUDOJ_4sIeg", "call_leg_id": "e4367406-4466-11f0-88fd-02420a1f0b70", "call_session_id": "e4366506-4466-11f0-8ec4-02420a1f0b70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T12:48:35.423737Z", "to": "+919552775831"}}], "bridged_at": "2025-06-08T18:18:37.316649", "answered_at": "2025-06-08T18:18:52.445830", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.183283"}, {"id": "83cedae8-946a-47c6-9759-a9940df3d4b1", "timestamp": "2025-06-08T18:18:27.661844", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749386900", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "v3:pCN8qi0y1gtoyTwPa8So2k78wVbHfEyS7GOpwFB8Eo4IU2AVY8r_yw", "call_session_id": "e4878300-4466-11f0-a55d-02420aef9920", "transfer_status": "failed", "webhook_events": [{"event_type": "call.initiated", "timestamp": "2025-06-08T18:18:37.230683", "payload": {"call_control_id": "v3:wX1qO_Vvrx31ik6d7CGBso4dqowlHJ3GdrnBXHtoRxe2iK7QBh7_nA", "call_leg_id": "e4878990-4466-11f0-9299-02420aef9920", "call_session_id": "e4878300-4466-11f0-a55d-02420aef9920", "caller_id_name": "+18162196532", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "direction": "incoming", "from": "+18162196532", "from_sip_uri": "+<EMAIL>:9000", "offered_codecs": "G722,PCMU,PCMA", "start_time": "2025-06-08T12:48:35.978626Z", "state": "parked", "to": "+917420068477", "to_sip_uri": "+<EMAIL>"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T18:18:38.681790", "payload": {"call_control_id": "v3:wX1qO_Vvrx31ik6d7CGBso4dqowlHJ3GdrnBXHtoRxe2iK7QBh7_nA", "call_leg_id": "e4878990-4466-11f0-9299-02420aef9920", "call_session_id": "e4878300-4466-11f0-a55d-02420aef9920", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+18162196532", "start_time": "2025-06-08T12:48:35.978626Z", "to": "+917420068477"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T18:18:38.701775", "payload": {"call_control_id": "v3:pCN8qi0y1gtoyTwPa8So2k78wVbHfEyS7GOpwFB8Eo4IU2AVY8r_yw", "call_leg_id": "e5424a64-4466-11f0-8598-02420aef97a1", "call_session_id": "e4878300-4466-11f0-a55d-02420aef9920", "client_state": null, "connection_id": "2702376459367876227", "from": "+18162196532", "start_time": "2025-06-08T12:48:35.978626Z", "to": "+917420068477"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T18:18:38.722745", "payload": {"call_control_id": "v3:pCN8qi0y1gtoyTwPa8So2k78wVbHfEyS7GOpwFB8Eo4IU2AVY8r_yw", "call_leg_id": "e5424a64-4466-11f0-8598-02420aef97a1", "call_session_id": "e4878300-4466-11f0-a55d-02420aef9920", "client_state": null, "connection_id": "2702376459367876227", "direction": "outgoing", "from": "+18162196532", "state": "bridging", "to": "+917420068477"}}, {"event_type": "call.answered", "timestamp": "2025-06-08T18:18:45.453092", "payload": {"call_control_id": "v3:wX1qO_Vvrx31ik6d7CGBso4dqowlHJ3GdrnBXHtoRxe2iK7QBh7_nA", "call_leg_id": "e4878990-4466-11f0-9299-02420aef9920", "call_session_id": "e4878300-4466-11f0-a55d-02420aef9920", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+18162196532", "start_time": "2025-06-08T12:48:35.978626Z", "to": "+917420068477"}}, {"event_type": "call.answered", "timestamp": "2025-06-08T18:18:45.477812", "payload": {"call_control_id": "v3:pCN8qi0y1gtoyTwPa8So2k78wVbHfEyS7GOpwFB8Eo4IU2AVY8r_yw", "call_leg_id": "e5424a64-4466-11f0-8598-02420aef97a1", "call_session_id": "e4878300-4466-11f0-a55d-02420aef9920", "client_state": null, "connection_id": "2702376459367876227", "from": "+18162196532", "start_time": "2025-06-08T12:48:37.818626Z", "to": "+917420068477"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T18:19:01.850486", "payload": {"call_control_id": "v3:pCN8qi0y1gtoyTwPa8So2k78wVbHfEyS7GOpwFB8Eo4IU2AVY8r_yw", "call_leg_id": "e5424a64-4466-11f0-8598-02420aef97a1", "call_quality_stats": {"inbound": {"jitter_max_variance": "2.56", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "1157", "skip_packet_count": "12"}, "outbound": {"packet_count": "789", "skip_packet_count": "0"}}, "call_session_id": "e4878300-4466-11f0-a55d-02420aef9920", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T12:49:02.318611Z", "from": "+18162196532", "hangup_cause": "normal_clearing", "hangup_source": "callee", "sip_hangup_cause": "200", "start_time": "2025-06-08T12:48:37.818626Z", "to": "+917420068477"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T18:19:01.989355", "payload": {"call_control_id": "v3:wX1qO_Vvrx31ik6d7CGBso4dqowlHJ3GdrnBXHtoRxe2iK7QBh7_nA", "call_leg_id": "e4878990-4466-11f0-9299-02420aef9920", "call_quality_stats": {"inbound": {"jitter_max_variance": "42.67", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "795", "skip_packet_count": "436"}, "outbound": {"packet_count": "1188", "skip_packet_count": "0"}}, "call_session_id": "e4878300-4466-11f0-a55d-02420aef9920", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T12:49:02.338625Z", "from": "+18162196532", "hangup_cause": "normal_clearing", "hangup_source": "callee", "sip_hangup_cause": "200", "start_time": "2025-06-08T12:48:35.978626Z", "to": "+917420068477"}}], "bridged_at": "2025-06-08T18:18:38.678790", "answered_at": "2025-06-08T18:18:45.467694", "hangup_cause": "normal_clearing", "completed_at": "2025-06-08T18:19:01.975829", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.183283"}, {"id": "cf93691c-5633-4a44-9a18-4b61c4a87e7f", "timestamp": "2025-06-08T17:51:45.250392", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749373258", "status": "no_answer", "notes": "Retry: initiated | Original: Retry: initiated | Original: ", "call_duration": 6, "recording_url": "", "telnyx_call_id": "v3:l3JUmgu3RDFthuotEKFrRYbC-j65kYqegJYOs8oyxnBcsIXaOtmD7w", "call_session_id": "29447042-4463-11f0-9437-02420a1f0b70", "transfer_status": "failed", "webhook_events": [{"event_type": "call.initiated", "timestamp": "2025-06-08T17:51:54.423657", "payload": {"call_control_id": "v3:ys7teFlthBl-ZsrToAMjBknWlBTR_LslWdyIuMFfKEGkEFNtUVNEnw", "call_leg_id": "29447e7a-4463-11f0-bbfe-02420a1f0b70", "call_session_id": "29447042-4463-11f0-9437-02420a1f0b70", "caller_id_name": "+18162196532", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "direction": "incoming", "from": "+18162196532", "from_sip_uri": "+<EMAIL>:9000", "offered_codecs": "G722,PCMU,PCMA", "start_time": "2025-06-08T12:21:53.303908Z", "state": "parked", "to": "+919552775831", "to_sip_uri": "+<EMAIL>"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T17:51:55.845766", "payload": {"call_control_id": "v3:l3JUmgu3RDFthuotEKFrRYbC-j65kYqegJYOs8oyxnBcsIXaOtmD7w", "call_leg_id": "2a415a5a-4463-11f0-9d95-02420a1f0a70", "call_session_id": "29447042-4463-11f0-9437-02420a1f0b70", "client_state": null, "connection_id": "2702376459367876227", "direction": "outgoing", "from": "+18162196532", "state": "bridging", "to": "+919552775831"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T17:51:56.544028", "payload": {"call_control_id": "v3:l3JUmgu3RDFthuotEKFrRYbC-j65kYqegJYOs8oyxnBcsIXaOtmD7w", "call_leg_id": "2a415a5a-4463-11f0-9d95-02420a1f0a70", "call_session_id": "29447042-4463-11f0-9437-02420a1f0b70", "client_state": null, "connection_id": "2702376459367876227", "from": "+18162196532", "start_time": "2025-06-08T12:21:53.303908Z", "to": "+919552775831"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T17:51:56.618550", "payload": {"call_control_id": "v3:ys7teFlthBl-ZsrToAMjBknWlBTR_LslWdyIuMFfKEGkEFNtUVNEnw", "call_leg_id": "29447e7a-4463-11f0-bbfe-02420a1f0b70", "call_session_id": "29447042-4463-11f0-9437-02420a1f0b70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+18162196532", "start_time": "2025-06-08T12:21:53.303908Z", "to": "+919552775831"}}, {"event_type": "call.answered", "timestamp": "2025-06-08T17:52:14.072091", "payload": {"call_control_id": "v3:ys7teFlthBl-ZsrToAMjBknWlBTR_LslWdyIuMFfKEGkEFNtUVNEnw", "call_leg_id": "29447e7a-4463-11f0-bbfe-02420a1f0b70", "call_session_id": "29447042-4463-11f0-9437-02420a1f0b70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+18162196532", "start_time": "2025-06-08T12:21:53.303908Z", "to": "+919552775831"}}, {"event_type": "call.answered", "timestamp": "2025-06-08T17:52:14.112500", "payload": {"call_control_id": "v3:l3JUmgu3RDFthuotEKFrRYbC-j65kYqegJYOs8oyxnBcsIXaOtmD7w", "call_leg_id": "2a415a5a-4463-11f0-9d95-02420a1f0a70", "call_session_id": "29447042-4463-11f0-9437-02420a1f0b70", "client_state": null, "connection_id": "2702376459367876227", "from": "+18162196532", "start_time": "2025-06-08T12:21:55.103907Z", "to": "+919552775831"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T17:52:19.828458", "payload": {"call_control_id": "v3:ys7teFlthBl-ZsrToAMjBknWlBTR_LslWdyIuMFfKEGkEFNtUVNEnw", "call_leg_id": "29447e7a-4463-11f0-bbfe-02420a1f0b70", "call_quality_stats": {"inbound": {"jitter_max_variance": "300.00", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "274", "skip_packet_count": "1006"}, "outbound": {"packet_count": "1226", "skip_packet_count": "0"}}, "call_session_id": "29447042-4463-11f0-9437-02420a1f0b70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T12:22:20.543906Z", "from": "+18162196532", "hangup_cause": "normal_clearing", "hangup_source": "callee", "sip_hangup_cause": "200", "start_time": "2025-06-08T12:21:53.303908Z", "to": "+919552775831"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T17:52:19.834500", "payload": {"call_control_id": "v3:l3JUmgu3RDFthuotEKFrRYbC-j65kYqegJYOs8oyxnBcsIXaOtmD7w", "call_leg_id": "2a415a5a-4463-11f0-9d95-02420a1f0a70", "call_quality_stats": {"inbound": {"jitter_max_variance": "7.69", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "1167", "skip_packet_count": "16"}, "outbound": {"packet_count": "260", "skip_packet_count": "0"}}, "call_session_id": "29447042-4463-11f0-9437-02420a1f0b70", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T12:22:20.543906Z", "from": "+18162196532", "hangup_cause": "normal_clearing", "hangup_source": "callee", "sip_hangup_cause": "200", "start_time": "2025-06-08T12:21:55.103907Z", "to": "+919552775831"}}], "bridged_at": "2025-06-08T17:51:56.608863", "answered_at": "2025-06-08T17:52:14.078347", "hangup_cause": "normal_clearing", "completed_at": "2025-06-08T17:52:19.800605", "telnyx_status": "call", "is_alive": false, "real_call_duration": 6, "start_time": "2025-06-08T12:22:14Z", "end_time": "2025-06-08T12:22:20Z", "telnyx_updated": "2025-06-08T18:08:21.673412", "accurate_status": "completed", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.184282"}, {"id": "676dbe44-ad38-4216-8ecb-4fb2a0676367", "timestamp": "2025-06-08T17:51:45.119596", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749373258", "status": "no_answer", "notes": "Retry: initiated | Original: Retry: initiated | Original: ", "call_duration": 0, "recording_url": "", "telnyx_call_id": "v3:AbZQ_dy81rVOPE3wm-JBH1dZWQCttZiaQxQ5Jn-Fdv0FrB7GIfkPmw", "call_session_id": "294294a2-4463-11f0-ba9d-02420a1f0b70", "transfer_status": "failed", "webhook_events": [{"event_type": "call.initiated", "timestamp": "2025-06-08T17:51:53.918961", "payload": {"call_control_id": "v3:L5jXlidADQj52yAsQwJStPCvbwAsBm-6l27v3CAZsZH9V2kTLF8JsA", "call_leg_id": "2942a366-4463-11f0-bf0f-02420a1f0b70", "call_session_id": "294294a2-4463-11f0-ba9d-02420a1f0b70", "caller_id_name": "+19199256164", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "direction": "incoming", "from": "+19199256164", "from_sip_uri": "+<EMAIL>:9000", "offered_codecs": "G722,PCMU,PCMA", "start_time": "2025-06-08T12:21:53.288270Z", "state": "parked", "to": "+917420068477", "to_sip_uri": "+<EMAIL>"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T17:51:54.905216", "payload": {"call_control_id": "v3:AbZQ_dy81rVOPE3wm-JBH1dZWQCttZiaQxQ5Jn-Fdv0FrB7GIfkPmw", "call_leg_id": "29f7340c-4463-11f0-b501-02420a1f0b70", "call_session_id": "294294a2-4463-11f0-ba9d-02420a1f0b70", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T12:21:53.288270Z", "to": "+917420068477"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T17:51:55.209784", "payload": {"call_control_id": "v3:L5jXlidADQj52yAsQwJStPCvbwAsBm-6l27v3CAZsZH9V2kTLF8JsA", "call_leg_id": "2942a366-4463-11f0-bf0f-02420a1f0b70", "call_session_id": "294294a2-4463-11f0-ba9d-02420a1f0b70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T12:21:53.288270Z", "to": "+917420068477"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T17:51:55.759973", "payload": {"call_control_id": "v3:AbZQ_dy81rVOPE3wm-JBH1dZWQCttZiaQxQ5Jn-Fdv0FrB7GIfkPmw", "call_leg_id": "29f7340c-4463-11f0-b501-02420a1f0b70", "call_session_id": "294294a2-4463-11f0-ba9d-02420a1f0b70", "client_state": null, "connection_id": "2702376459367876227", "direction": "outgoing", "from": "+19199256164", "state": "bridging", "to": "+917420068477"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T17:52:20.553788", "payload": {"call_control_id": "v3:AbZQ_dy81rVOPE3wm-JBH1dZWQCttZiaQxQ5Jn-Fdv0FrB7GIfkPmw", "call_leg_id": "29f7340c-4463-11f0-b501-02420a1f0b70", "call_quality_stats": {"inbound": {"jitter_max_variance": "0.00", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "1275", "skip_packet_count": "8"}, "outbound": {"packet_count": "0", "skip_packet_count": "0"}}, "call_session_id": "294294a2-4463-11f0-ba9d-02420a1f0b70", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T12:22:21.308271Z", "from": "+19199256164", "hangup_cause": "unspecified", "hangup_source": "unknown", "sip_hangup_cause": "480", "start_time": "2025-06-08T12:21:54.608269Z", "to": "+917420068477"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T17:54:52.644956", "payload": {"call_control_id": "v3:L5jXlidADQj52yAsQwJStPCvbwAsBm-6l27v3CAZsZH9V2kTLF8JsA", "call_leg_id": "2942a366-4463-11f0-bf0f-02420a1f0b70", "call_quality_stats": {"inbound": {"jitter_max_variance": "0.00", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "0", "skip_packet_count": "8924"}, "outbound": {"packet_count": "1327", "skip_packet_count": "0"}}, "call_session_id": "294294a2-4463-11f0-ba9d-02420a1f0b70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T12:24:53.088271Z", "from": "+19199256164", "hangup_cause": "originator_cancel", "hangup_source": "caller", "sip_hangup_cause": "480", "start_time": "2025-06-08T12:21:53.288270Z", "to": "+917420068477"}}], "bridged_at": "2025-06-08T17:51:55.199205", "hangup_cause": "originator_cancel", "completed_at": "2025-06-08T17:54:52.634952", "telnyx_status": "call", "is_alive": false, "real_call_duration": 0, "start_time": null, "end_time": null, "telnyx_updated": "2025-06-08T18:08:19.912243", "accurate_status": "no_answer", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.184282"}, {"id": "849410ba-0d2f-48fe-8b39-b629dd4c2f24", "timestamp": "2025-06-08T15:29:10.433235", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749373258", "status": "no_answer", "notes": "Retry: initiated | Original: ", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.184282"}, {"id": "d928abdd-1e89-48d2-b815-1699a3d07861", "timestamp": "2025-06-08T15:29:09.477702", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749373258", "status": "no_answer", "notes": "Retry: initiated | Original: ", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.184282"}, {"id": "624c0854-f79a-49a6-8a5c-d8f8c88fd771", "timestamp": "2025-06-08T15:28:08.826834", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749373258", "status": "no_answer", "notes": "Retry: no_answer | Original: ", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "hangup_cause": "originator_cancel", "completed_at": "2025-06-08T15:32:23.907675", "webhook_events": [{"event_type": "call.hangup", "timestamp": "2025-06-08T15:32:23.927818", "payload": {"call_control_id": "v3:Tm04rDUhQj7QnFrUNm35Ki4bGf6dS-d3BE8Jb1VY79EsWLKwRHA4PQ", "call_leg_id": "41db40ea-444f-11f0-af08-02420a1f0d70", "call_quality_stats": {"inbound": {"jitter_max_variance": "0.00", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "0", "skip_packet_count": "8940"}, "outbound": {"packet_count": "1263", "skip_packet_count": "0"}}, "call_session_id": "41db3636-444f-11f0-87da-02420a1f0d70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T10:02:24.600517Z", "from": "+19199256164", "hangup_cause": "originator_cancel", "hangup_source": "caller", "sip_hangup_cause": "480", "start_time": "2025-06-08T09:59:24.620517Z", "to": "+917420068477"}}], "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.184282"}, {"id": "28f9440d-a59c-4a21-9c88-78b07484e6c8", "timestamp": "2025-06-08T15:28:08.225533", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749373258", "status": "no_answer", "notes": "Retry: no_answer | Original: ", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "hangup_cause": "originator_cancel", "completed_at": "2025-06-08T15:32:24.115022", "webhook_events": [{"event_type": "call.hangup", "timestamp": "2025-06-08T15:31:21.011700", "payload": {"call_control_id": "v3:6Uwxp3-lnuqBHrt28eo-YxzQgOA77K8Ozw5hC2wQUsrF027M5wor_w", "call_leg_id": "1c2f4b34-444f-11f0-aea2-02420a1f0d70", "call_quality_stats": {"inbound": {"jitter_max_variance": "0.00", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "0", "skip_packet_count": "8632"}, "outbound": {"packet_count": "1479", "skip_packet_count": "0"}}, "call_session_id": "1c2f3e1e-444f-11f0-bb82-02420a1f0d70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T10:01:21.340516Z", "from": "+18162196532", "hangup_cause": "originator_cancel", "hangup_source": "caller", "sip_hangup_cause": "487", "start_time": "2025-06-08T09:58:21.420520Z", "to": "+919552775831"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T15:32:24.138298", "payload": {"call_control_id": "v3:_eGiHtvFixWeGMrnVtPK3WhDKizE5nRTueQj_gj7-MNRaZKMckHakQ", "call_leg_id": "41d973e6-444f-11f0-87c2-02420a1f1070", "call_quality_stats": {"inbound": {"jitter_max_variance": "0.00", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "0", "skip_packet_count": "8931"}, "outbound": {"packet_count": "22", "skip_packet_count": "0"}}, "call_session_id": "41d9655e-444f-11f0-a8cd-02420a1f1070", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T10:02:24.600517Z", "from": "+18162196532", "hangup_cause": "originator_cancel", "hangup_source": "caller", "sip_hangup_cause": "404", "start_time": "2025-06-08T09:59:24.600517Z", "to": "+919552775831"}}], "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.184282"}, {"id": "be413719-7152-4b75-9992-42f53c27c073", "timestamp": "2025-06-08T14:31:07.640531", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749373258", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "v3:U3-n8WCr48V6fefho1wu1eazkAuYjfqbFYHFGf_mxxmBB4oCZWnHKQ", "call_session_id": "41db3636-444f-11f0-87da-02420a1f0d70", "transfer_status": "failed", "webhook_events": [{"event_type": "call.initiated", "timestamp": "2025-06-08T14:31:24.261605", "payload": {"call_control_id": "v3:5GU9J3BS_bpBLLAHVzqymR2OXh5eCKdmXAo_jsIQf3TpOnkBN4y7yg", "call_leg_id": "27195024-4447-11f0-aa96-02420a1f0a70", "call_session_id": "271947a0-4447-11f0-a28e-02420a1f0a70", "caller_id_name": "+19199256164", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "direction": "incoming", "from": "+19199256164", "from_sip_uri": "+<EMAIL>:9000", "offered_codecs": "G722,PCMU,PCMA", "start_time": "2025-06-08T09:01:23.763907Z", "state": "parked", "to": "+917420068477", "to_sip_uri": "+<EMAIL>"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T14:31:25.586493", "payload": {"call_control_id": "v3:5GU9J3BS_bpBLLAHVzqymR2OXh5eCKdmXAo_jsIQf3TpOnkBN4y7yg", "call_leg_id": "27195024-4447-11f0-aa96-02420a1f0a70", "call_session_id": "271947a0-4447-11f0-a28e-02420a1f0a70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T09:01:23.763907Z", "to": "+917420068477"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T14:31:25.637376", "payload": {"call_control_id": "v3:msBmBCADGgkMtE65GmbnucBYRxuGZeLcmWdUVlyPXPoc0y0MhqRPjQ", "call_leg_id": "27c9e48e-4447-11f0-915f-02420a1f0a70", "call_session_id": "271947a0-4447-11f0-a28e-02420a1f0a70", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T09:01:23.763907Z", "to": "+917420068477"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T14:31:26.127437", "payload": {"call_control_id": "v3:msBmBCADGgkMtE65GmbnucBYRxuGZeLcmWdUVlyPXPoc0y0MhqRPjQ", "call_leg_id": "27c9e48e-4447-11f0-915f-02420a1f0a70", "call_session_id": "271947a0-4447-11f0-a28e-02420a1f0a70", "client_state": null, "connection_id": "2702376459367876227", "direction": "outgoing", "from": "+19199256164", "state": "bridging", "to": "+917420068477"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T14:31:54.533752", "payload": {"call_control_id": "v3:msBmBCADGgkMtE65GmbnucBYRxuGZeLcmWdUVlyPXPoc0y0MhqRPjQ", "call_leg_id": "27c9e48e-4447-11f0-915f-02420a1f0a70", "call_quality_stats": {"inbound": {"jitter_max_variance": "36.36", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "1448", "skip_packet_count": "2"}, "outbound": {"packet_count": "0", "skip_packet_count": "0"}}, "call_session_id": "271947a0-4447-11f0-a28e-02420a1f0a70", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T09:01:55.383905Z", "from": "+19199256164", "hangup_cause": "timeout", "hangup_source": "caller", "sip_hangup_cause": "487", "start_time": "2025-06-08T09:01:25.023906Z", "to": "+917420068477"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T15:28:23.888991", "payload": {"call_control_id": "v3:mehI9P1cskDh-pVgK2gy592712O84hbTf1jOppzOcapSfXSXLuJ_xQ", "call_leg_id": "1d5b33ba-444f-11f0-941c-02420a1f0d70", "call_session_id": "1d5b26ae-444f-11f0-85cc-02420a1f0d70", "caller_id_name": "+19199256164", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "direction": "incoming", "from": "+19199256164", "from_sip_uri": "+<EMAIL>:9000", "offered_codecs": "G722,PCMU,PCMA", "start_time": "2025-06-08T09:58:23.383691Z", "state": "parked", "to": "+917420068477", "to_sip_uri": "+<EMAIL>"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T15:28:24.740937", "payload": {"call_control_id": "v3:fOU2--dOOapIQJY4fiQp0lxIy0SFNlmDvhStGirkZfMgxPHE2yVZUg", "call_leg_id": "1df4bb84-444f-11f0-8251-02420a1f0a70", "call_session_id": "1d5b26ae-444f-11f0-85cc-02420a1f0d70", "client_state": null, "connection_id": "2702376459367876227", "direction": "outgoing", "from": "+19199256164", "state": "bridging", "to": "+917420068477"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T15:28:25.223820", "payload": {"call_control_id": "v3:mehI9P1cskDh-pVgK2gy592712O84hbTf1jOppzOcapSfXSXLuJ_xQ", "call_leg_id": "1d5b33ba-444f-11f0-941c-02420a1f0d70", "call_session_id": "1d5b26ae-444f-11f0-85cc-02420a1f0d70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T09:58:23.383691Z", "to": "+917420068477"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T15:28:25.228826", "payload": {"call_control_id": "v3:fOU2--dOOapIQJY4fiQp0lxIy0SFNlmDvhStGirkZfMgxPHE2yVZUg", "call_leg_id": "1df4bb84-444f-11f0-8251-02420a1f0a70", "call_session_id": "1d5b26ae-444f-11f0-85cc-02420a1f0d70", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T09:58:23.383691Z", "to": "+917420068477"}}, {"event_type": "call.answered", "timestamp": "2025-06-08T15:28:32.011453", "payload": {"call_control_id": "v3:mehI9P1cskDh-pVgK2gy592712O84hbTf1jOppzOcapSfXSXLuJ_xQ", "call_leg_id": "1d5b33ba-444f-11f0-941c-02420a1f0d70", "call_session_id": "1d5b26ae-444f-11f0-85cc-02420a1f0d70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T09:58:23.383691Z", "to": "+917420068477"}}, {"event_type": "call.answered", "timestamp": "2025-06-08T15:28:32.406234", "payload": {"call_control_id": "v3:fOU2--dOOapIQJY4fiQp0lxIy0SFNlmDvhStGirkZfMgxPHE2yVZUg", "call_leg_id": "1df4bb84-444f-11f0-8251-02420a1f0a70", "call_session_id": "1d5b26ae-444f-11f0-85cc-02420a1f0d70", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T09:58:24.603765Z", "to": "+917420068477"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T15:28:36.735516", "payload": {"call_control_id": "v3:fOU2--dOOapIQJY4fiQp0lxIy0SFNlmDvhStGirkZfMgxPHE2yVZUg", "call_leg_id": "1df4bb84-444f-11f0-8251-02420a1f0a70", "call_quality_stats": {"inbound": {"jitter_max_variance": "269.00", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "573", "skip_packet_count": "15"}, "outbound": {"packet_count": "198", "skip_packet_count": "0"}}, "call_session_id": "1d5b26ae-444f-11f0-85cc-02420a1f0d70", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T09:58:37.523689Z", "from": "+19199256164", "hangup_cause": "normal_clearing", "hangup_source": "callee", "sip_hangup_cause": "200", "start_time": "2025-06-08T09:58:24.603765Z", "to": "+917420068477"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T15:28:36.806832", "payload": {"call_control_id": "v3:mehI9P1cskDh-pVgK2gy592712O84hbTf1jOppzOcapSfXSXLuJ_xQ", "call_leg_id": "1d5b33ba-444f-11f0-941c-02420a1f0d70", "call_quality_stats": {"inbound": {"jitter_max_variance": "132.69", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "216", "skip_packet_count": "435"}, "outbound": {"packet_count": "598", "skip_packet_count": "0"}}, "call_session_id": "1d5b26ae-444f-11f0-85cc-02420a1f0d70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T09:58:37.543690Z", "from": "+19199256164", "hangup_cause": "normal_clearing", "hangup_source": "callee", "sip_hangup_cause": "200", "start_time": "2025-06-08T09:58:23.383691Z", "to": "+917420068477"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T15:29:25.079969", "payload": {"call_control_id": "v3:Tm04rDUhQj7QnFrUNm35Ki4bGf6dS-d3BE8Jb1VY79EsWLKwRHA4PQ", "call_leg_id": "41db40ea-444f-11f0-af08-02420a1f0d70", "call_session_id": "41db3636-444f-11f0-87da-02420a1f0d70", "caller_id_name": "+19199256164", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "direction": "incoming", "from": "+19199256164", "from_sip_uri": "+<EMAIL>:9000", "offered_codecs": "G722,PCMU,PCMA", "start_time": "2025-06-08T09:59:24.620517Z", "state": "parked", "to": "+917420068477", "to_sip_uri": "+<EMAIL>"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T15:29:26.438664", "payload": {"call_control_id": "v3:U3-n8WCr48V6fefho1wu1eazkAuYjfqbFYHFGf_mxxmBB4oCZWnHKQ", "call_leg_id": "426851a6-444f-11f0-840b-02420a1f0b70", "call_session_id": "41db3636-444f-11f0-87da-02420a1f0d70", "client_state": null, "connection_id": "2702376459367876227", "direction": "outgoing", "from": "+19199256164", "state": "bridging", "to": "+917420068477"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T15:29:26.562383", "payload": {"call_control_id": "v3:Tm04rDUhQj7QnFrUNm35Ki4bGf6dS-d3BE8Jb1VY79EsWLKwRHA4PQ", "call_leg_id": "41db40ea-444f-11f0-af08-02420a1f0d70", "call_session_id": "41db3636-444f-11f0-87da-02420a1f0d70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T09:59:24.620517Z", "to": "+917420068477"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T15:29:26.607170", "payload": {"call_control_id": "v3:U3-n8WCr48V6fefho1wu1eazkAuYjfqbFYHFGf_mxxmBB4oCZWnHKQ", "call_leg_id": "426851a6-444f-11f0-840b-02420a1f0b70", "call_session_id": "41db3636-444f-11f0-87da-02420a1f0d70", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T09:59:24.620517Z", "to": "+917420068477"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T15:29:50.445187", "payload": {"call_control_id": "v3:U3-n8WCr48V6fefho1wu1eazkAuYjfqbFYHFGf_mxxmBB4oCZWnHKQ", "call_leg_id": "426851a6-444f-11f0-840b-02420a1f0b70", "call_quality_stats": {"inbound": {"jitter_max_variance": "0.00", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "1190", "skip_packet_count": "7"}, "outbound": {"packet_count": "0", "skip_packet_count": "0"}}, "call_session_id": "41db3636-444f-11f0-87da-02420a1f0d70", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T09:59:51.180517Z", "from": "+19199256164", "hangup_cause": "unspecified", "hangup_source": "unknown", "sip_hangup_cause": "480", "start_time": "2025-06-08T09:59:25.780527Z", "to": "+917420068477"}}], "bridged_at": "2025-06-08T15:29:26.567589", "hangup_cause": "unspecified", "completed_at": "2025-06-08T15:29:50.423275", "telnyx_status": "call", "is_alive": false, "real_call_duration": 0, "start_time": null, "end_time": null, "telnyx_updated": "2025-06-08T18:08:18.679549", "accurate_status": "no_answer", "answered_at": "2025-06-08T15:28:32.350619", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.184282"}, {"id": "21d147a9-5643-4dc6-9d04-36b8c3add40b", "timestamp": "2025-06-08T14:31:07.637527", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749373258", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "v3:TfMnqT0ZsDBheMqBrSYNT6Mr0hUu1GbbkowWGg83hRzTkG8gG_zILQ", "call_session_id": "41d9655e-444f-11f0-a8cd-02420a1f1070", "transfer_status": "failed", "webhook_events": [{"event_type": "call.initiated", "timestamp": "2025-06-08T14:31:25.041694", "payload": {"call_control_id": "v3:0_ucF0LUvWv1ek7z2R6whaHENheTocR5HegmMIz0IDzkf6tUOX2OfA", "call_leg_id": "27b5be8c-4447-11f0-8d7d-02420a1f0a70", "call_session_id": "27b5b568-4447-11f0-8150-02420a1f0a70", "caller_id_name": "+18162196532", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "direction": "incoming", "from": "+18162196532", "from_sip_uri": "+<EMAIL>:9000", "offered_codecs": "G722,PCMU,PCMA", "start_time": "2025-06-08T09:01:24.783905Z", "state": "parked", "to": "+919552775831", "to_sip_uri": "+<EMAIL>"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T14:31:26.069590", "payload": {"call_control_id": "v3:GE-SxblMrInl_ufGM_NjmwO9aU8cpCRtp6GCgXCTxVvGhvBKvdsOIA", "call_leg_id": "283e4cfc-4447-11f0-bca1-02420a1f0b70", "call_session_id": "27b5b568-4447-11f0-8150-02420a1f0a70", "client_state": null, "connection_id": "2702376459367876227", "from": "+18162196532", "start_time": "2025-06-08T09:01:24.783905Z", "to": "+919552775831"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T14:31:26.124879", "payload": {"call_control_id": "v3:0_ucF0LUvWv1ek7z2R6whaHENheTocR5HegmMIz0IDzkf6tUOX2OfA", "call_leg_id": "27b5be8c-4447-11f0-8d7d-02420a1f0a70", "call_session_id": "27b5b568-4447-11f0-8150-02420a1f0a70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+18162196532", "start_time": "2025-06-08T09:01:24.783905Z", "to": "+919552775831"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T14:31:26.269995", "payload": {"call_control_id": "v3:GE-SxblMrInl_ufGM_NjmwO9aU8cpCRtp6GCgXCTxVvGhvBKvdsOIA", "call_leg_id": "283e4cfc-4447-11f0-bca1-02420a1f0b70", "call_session_id": "27b5b568-4447-11f0-8150-02420a1f0a70", "client_state": null, "connection_id": "2702376459367876227", "direction": "outgoing", "from": "+18162196532", "state": "bridging", "to": "+919552775831"}}, {"event_type": "call.answered", "timestamp": "2025-06-08T14:31:39.093051", "payload": {"call_control_id": "v3:0_ucF0LUvWv1ek7z2R6whaHENheTocR5HegmMIz0IDzkf6tUOX2OfA", "call_leg_id": "27b5be8c-4447-11f0-8d7d-02420a1f0a70", "call_session_id": "27b5b568-4447-11f0-8150-02420a1f0a70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+18162196532", "start_time": "2025-06-08T09:01:24.783905Z", "to": "+919552775831"}}, {"event_type": "call.answered", "timestamp": "2025-06-08T14:31:39.173115", "payload": {"call_control_id": "v3:GE-SxblMrInl_ufGM_NjmwO9aU8cpCRtp6GCgXCTxVvGhvBKvdsOIA", "call_leg_id": "283e4cfc-4447-11f0-bca1-02420a1f0b70", "call_session_id": "27b5b568-4447-11f0-8150-02420a1f0a70", "client_state": null, "connection_id": "2702376459367876227", "from": "+18162196532", "start_time": "2025-06-08T09:01:25.823906Z", "to": "+919552775831"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T14:32:21.762250", "payload": {"call_control_id": "v3:0_ucF0LUvWv1ek7z2R6whaHENheTocR5HegmMIz0IDzkf6tUOX2OfA", "call_leg_id": "27b5be8c-4447-11f0-8d7d-02420a1f0a70", "call_quality_stats": {"inbound": {"jitter_max_variance": "85.48", "jitter_packet_count": "0", "mos": "4.49", "packet_count": "2102", "skip_packet_count": "744"}, "outbound": {"packet_count": "2792", "skip_packet_count": "0"}}, "call_session_id": "27b5b568-4447-11f0-8150-02420a1f0a70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T09:02:22.603905Z", "from": "+18162196532", "hangup_cause": "normal_clearing", "hangup_source": "caller", "sip_hangup_cause": "200", "start_time": "2025-06-08T09:01:24.783905Z", "to": "+919552775831"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T14:32:21.798294", "payload": {"call_control_id": "v3:GE-SxblMrInl_ufGM_NjmwO9aU8cpCRtp6GCgXCTxVvGhvBKvdsOIA", "call_leg_id": "283e4cfc-4447-11f0-bca1-02420a1f0b70", "call_quality_stats": {"inbound": {"jitter_max_variance": "0.38", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "2778", "skip_packet_count": "13"}, "outbound": {"packet_count": "2097", "skip_packet_count": "0"}}, "call_session_id": "27b5b568-4447-11f0-8150-02420a1f0a70", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T09:02:22.603905Z", "from": "+18162196532", "hangup_cause": "normal_clearing", "hangup_source": "caller", "sip_hangup_cause": "200", "start_time": "2025-06-08T09:01:25.823906Z", "to": "+919552775831"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T15:28:27.998354", "payload": {"call_control_id": "v3:6Uwxp3-lnuqBHrt28eo-YxzQgOA77K8Ozw5hC2wQUsrF027M5wor_w", "call_leg_id": "1c2f4b34-444f-11f0-aea2-02420a1f0d70", "call_session_id": "1c2f3e1e-444f-11f0-bb82-02420a1f0d70", "caller_id_name": "+18162196532", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "direction": "incoming", "from": "+18162196532", "from_sip_uri": "+<EMAIL>:9000", "offered_codecs": "G722,PCMU,PCMA", "start_time": "2025-06-08T09:58:21.420520Z", "state": "parked", "to": "+919552775831", "to_sip_uri": "+<EMAIL>"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T15:28:28.650959", "payload": {"call_control_id": "v3:xLLygRUeCV5a4rIPfmzlg5cgS6Kzbq4raR953llhQkX1eH3Dy0ylGA", "call_leg_id": "205b0392-444f-11f0-bf9d-02420a1f0a70", "call_session_id": "1c2f3e1e-444f-11f0-bb82-02420a1f0d70", "client_state": null, "connection_id": "2702376459367876227", "direction": "outgoing", "from": "+18162196532", "state": "bridging", "to": "+919552775831"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T15:28:29.466410", "payload": {"call_control_id": "v3:6Uwxp3-lnuqBHrt28eo-YxzQgOA77K8Ozw5hC2wQUsrF027M5wor_w", "call_leg_id": "1c2f4b34-444f-11f0-aea2-02420a1f0d70", "call_session_id": "1c2f3e1e-444f-11f0-bb82-02420a1f0d70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+18162196532", "start_time": "2025-06-08T09:58:21.420520Z", "to": "+919552775831"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T15:28:29.830038", "payload": {"call_control_id": "v3:xLLygRUeCV5a4rIPfmzlg5cgS6Kzbq4raR953llhQkX1eH3Dy0ylGA", "call_leg_id": "205b0392-444f-11f0-bf9d-02420a1f0a70", "call_session_id": "1c2f3e1e-444f-11f0-bb82-02420a1f0d70", "client_state": null, "connection_id": "2702376459367876227", "from": "+18162196532", "start_time": "2025-06-08T09:58:21.420520Z", "to": "+919552775831"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T15:28:57.618852", "payload": {"call_control_id": "v3:xLLygRUeCV5a4rIPfmzlg5cgS6Kzbq4raR953llhQkX1eH3Dy0ylGA", "call_leg_id": "205b0392-444f-11f0-bf9d-02420a1f0a70", "call_quality_stats": {"inbound": {"jitter_max_variance": "0.00", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "1407", "skip_packet_count": "2"}, "outbound": {"packet_count": "0", "skip_packet_count": "0"}}, "call_session_id": "1c2f3e1e-444f-11f0-bb82-02420a1f0d70", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T09:58:58.340587Z", "from": "+18162196532", "hangup_cause": "timeout", "hangup_source": "caller", "sip_hangup_cause": "487", "start_time": "2025-06-08T09:58:28.680518Z", "to": "+919552775831"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T15:29:25.265127", "payload": {"call_control_id": "v3:_eGiHtvFixWeGMrnVtPK3WhDKizE5nRTueQj_gj7-MNRaZKMckHakQ", "call_leg_id": "41d973e6-444f-11f0-87c2-02420a1f1070", "call_session_id": "41d9655e-444f-11f0-a8cd-02420a1f1070", "caller_id_name": "+18162196532", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "direction": "incoming", "from": "+18162196532", "from_sip_uri": "+<EMAIL>:9000", "offered_codecs": "G722,PCMU,PCMA", "start_time": "2025-06-08T09:59:24.600517Z", "state": "parked", "to": "+919552775831", "to_sip_uri": "+<EMAIL>"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T15:29:25.664860", "payload": {"call_control_id": "v3:TfMnqT0ZsDBheMqBrSYNT6Mr0hUu1GbbkowWGg83hRzTkG8gG_zILQ", "call_leg_id": "42887cb0-444f-11f0-816f-02420a1f0b70", "call_quality_stats": null, "call_session_id": "41d9655e-444f-11f0-a8cd-02420a1f1070", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T09:59:26.400619Z", "from": "+18162196532", "hangup_cause": "not_found", "hangup_source": "unknown", "sip_hangup_cause": "404", "start_time": "2025-06-08T09:59:25.960549Z", "to": "+919552775831"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T15:29:26.265179", "payload": {"call_control_id": "v3:TfMnqT0ZsDBheMqBrSYNT6Mr0hUu1GbbkowWGg83hRzTkG8gG_zILQ", "call_leg_id": "42887cb0-444f-11f0-816f-02420a1f0b70", "call_session_id": "41d9655e-444f-11f0-a8cd-02420a1f1070", "client_state": null, "connection_id": "2702376459367876227", "direction": "outgoing", "from": "+18162196532", "state": "bridging", "to": "+919552775831"}}], "bridged_at": "2025-06-08T15:28:29.801257", "answered_at": "2025-06-08T14:31:39.150445", "hangup_cause": "not_found", "completed_at": "2025-06-08T15:29:25.625718", "telnyx_status": "call", "is_alive": false, "real_call_duration": 0, "start_time": null, "end_time": null, "telnyx_updated": "2025-06-08T18:08:17.537477", "accurate_status": "no_answer", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.185296"}, {"id": "19f1f3d2-928d-42fe-a8f5-bd82f1e8245c", "timestamp": "2025-06-08T14:17:27.029464", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19858539054", "agent_name": "<PERSON>", "campaign_id": "campaign_1749372434", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "v3:U35Zzf_EKGxtCAzSuQkAmDKztRsbAFb6kqErMdtrAM-xpJSTAsix-A", "call_session_id": "3c188d5c-4445-11f0-a768-02420a1f0a70", "transfer_status": "failed", "webhook_events": [{"event_type": "call.initiated", "timestamp": "2025-06-08T14:17:41.230643", "payload": {"call_control_id": "v3:U35Zzf_EKGxtCAzSuQkAmDKztRsbAFb6kqErMdtrAM-xpJSTAsix-A", "call_leg_id": "3c189cac-4445-11f0-833f-02420a1f0a70", "call_session_id": "3c188d5c-4445-11f0-a768-02420a1f0a70", "caller_id_name": "+19858539054", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "direction": "incoming", "from": "+19858539054", "from_sip_uri": "+<EMAIL>:9000", "offered_codecs": "G722,PCMU,PCMA", "start_time": "2025-06-08T08:47:39.988525Z", "state": "parked", "to": "+917420068477", "to_sip_uri": "+<EMAIL>"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T14:17:42.115411", "payload": {"call_control_id": "v3:_zDWrNeuehBHNqEmXVEQ53IVegATiRg1Zl9Jq38oez0Uy20KhWl_Lw", "call_leg_id": "3d3c2874-4445-11f0-9b24-02420a1f0a70", "call_session_id": "3c188d5c-4445-11f0-a768-02420a1f0a70", "client_state": null, "connection_id": "2702376459367876227", "direction": "outgoing", "from": "+19858539054", "state": "bridging", "to": "+917420068477"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T14:17:42.684487", "payload": {"call_control_id": "v3:_zDWrNeuehBHNqEmXVEQ53IVegATiRg1Zl9Jq38oez0Uy20KhWl_Lw", "call_leg_id": "3d3c2874-4445-11f0-9b24-02420a1f0a70", "call_session_id": "3c188d5c-4445-11f0-a768-02420a1f0a70", "client_state": null, "connection_id": "2702376459367876227", "from": "+19858539054", "start_time": "2025-06-08T08:47:39.988525Z", "to": "+917420068477"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T14:17:43.065079", "payload": {"call_control_id": "v3:U35Zzf_EKGxtCAzSuQkAmDKztRsbAFb6kqErMdtrAM-xpJSTAsix-A", "call_leg_id": "3c189cac-4445-11f0-833f-02420a1f0a70", "call_session_id": "3c188d5c-4445-11f0-a768-02420a1f0a70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+19858539054", "start_time": "2025-06-08T08:47:39.988525Z", "to": "+917420068477"}}, {"event_type": "call.answered", "timestamp": "2025-06-08T14:17:48.775706", "payload": {"call_control_id": "v3:_zDWrNeuehBHNqEmXVEQ53IVegATiRg1Zl9Jq38oez0Uy20KhWl_Lw", "call_leg_id": "3d3c2874-4445-11f0-9b24-02420a1f0a70", "call_session_id": "3c188d5c-4445-11f0-a768-02420a1f0a70", "client_state": null, "connection_id": "2702376459367876227", "from": "+19858539054", "start_time": "2025-06-08T08:47:42.028583Z", "to": "+917420068477"}}, {"event_type": "call.answered", "timestamp": "2025-06-08T14:17:48.801436", "payload": {"call_control_id": "v3:U35Zzf_EKGxtCAzSuQkAmDKztRsbAFb6kqErMdtrAM-xpJSTAsix-A", "call_leg_id": "3c189cac-4445-11f0-833f-02420a1f0a70", "call_session_id": "3c188d5c-4445-11f0-a768-02420a1f0a70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+19858539054", "start_time": "2025-06-08T08:47:39.988525Z", "to": "+917420068477"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T14:17:54.628284", "payload": {"call_control_id": "v3:U35Zzf_EKGxtCAzSuQkAmDKztRsbAFb6kqErMdtrAM-xpJSTAsix-A", "call_leg_id": "3c189cac-4445-11f0-833f-02420a1f0a70", "call_quality_stats": {"inbound": {"jitter_max_variance": "433.33", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "264", "skip_packet_count": "412"}, "outbound": {"packet_count": "624", "skip_packet_count": "0"}}, "call_session_id": "3c188d5c-4445-11f0-a768-02420a1f0a70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T08:47:55.488527Z", "from": "+19858539054", "hangup_cause": "normal_clearing", "hangup_source": "callee", "sip_hangup_cause": "200", "start_time": "2025-06-08T08:47:39.988525Z", "to": "+917420068477"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T14:17:55.046842", "payload": {"call_control_id": "v3:_zDWrNeuehBHNqEmXVEQ53IVegATiRg1Zl9Jq38oez0Uy20KhWl_Lw", "call_leg_id": "3d3c2874-4445-11f0-9b24-02420a1f0a70", "call_quality_stats": {"inbound": {"jitter_max_variance": "25.81", "jitter_packet_count": "0", "mos": "4.49", "packet_count": "593", "skip_packet_count": "13"}, "outbound": {"packet_count": "255", "skip_packet_count": "0"}}, "call_session_id": "3c188d5c-4445-11f0-a768-02420a1f0a70", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T08:47:55.468527Z", "from": "+19858539054", "hangup_cause": "normal_clearing", "hangup_source": "callee", "sip_hangup_cause": "200", "start_time": "2025-06-08T08:47:42.028583Z", "to": "+917420068477"}}], "bridged_at": "2025-06-08T14:17:42.956314", "answered_at": "2025-06-08T14:17:48.783815", "hangup_cause": "normal_clearing", "completed_at": "2025-06-08T14:17:55.008699", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.185296"}, {"id": "e2a296b6-9c6f-4c85-8640-1c7b338b0602", "timestamp": "2025-06-08T14:17:25.989151", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749372434", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "v3:HUT7r5qehC78fhSHoRrndPH8Jusxd4tK4tpMZxiO7lcBsORQb_m6rw", "call_session_id": "3c39b4d2-4445-11f0-a0f2-02420a1f0b70", "transfer_status": "failed", "webhook_events": [{"event_type": "call.initiated", "timestamp": "2025-06-08T14:17:41.195657", "payload": {"call_control_id": "v3:HUT7r5qehC78fhSHoRrndPH8Jusxd4tK4tpMZxiO7lcBsORQb_m6rw", "call_leg_id": "3c39c31e-4445-11f0-af15-02420a1f0b70", "call_session_id": "3c39b4d2-4445-11f0-a0f2-02420a1f0b70", "caller_id_name": "+19199256164", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "direction": "incoming", "from": "+19199256164", "from_sip_uri": "+<EMAIL>:9000", "offered_codecs": "G722,PCMU,PCMA", "start_time": "2025-06-08T08:47:40.203725Z", "state": "parked", "to": "+919552775831", "to_sip_uri": "+<EMAIL>"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T14:17:42.278130", "payload": {"call_control_id": "v3:0KdcT3x1Er-420ZsyqgPthJ2kXNVnJpBsE3unsSvoSANwnFxSflFRg", "call_leg_id": "3d13c08c-4445-11f0-8254-02420a1f0b70", "call_session_id": "3c39b4d2-4445-11f0-a0f2-02420a1f0b70", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T08:47:40.203725Z", "to": "+919552775831"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T14:17:42.350751", "payload": {"call_control_id": "v3:HUT7r5qehC78fhSHoRrndPH8Jusxd4tK4tpMZxiO7lcBsORQb_m6rw", "call_leg_id": "3c39c31e-4445-11f0-af15-02420a1f0b70", "call_session_id": "3c39b4d2-4445-11f0-a0f2-02420a1f0b70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T08:47:40.203725Z", "to": "+919552775831"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T14:17:42.502774", "payload": {"call_control_id": "v3:0KdcT3x1Er-420ZsyqgPthJ2kXNVnJpBsE3unsSvoSANwnFxSflFRg", "call_leg_id": "3d13c08c-4445-11f0-8254-02420a1f0b70", "call_session_id": "3c39b4d2-4445-11f0-a0f2-02420a1f0b70", "client_state": null, "connection_id": "2702376459367876227", "direction": "outgoing", "from": "+19199256164", "state": "bridging", "to": "+919552775831"}}, {"event_type": "call.answered", "timestamp": "2025-06-08T14:17:58.641761", "payload": {"call_control_id": "v3:0KdcT3x1Er-420ZsyqgPthJ2kXNVnJpBsE3unsSvoSANwnFxSflFRg", "call_leg_id": "3d13c08c-4445-11f0-8254-02420a1f0b70", "call_session_id": "3c39b4d2-4445-11f0-a0f2-02420a1f0b70", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T08:47:41.763727Z", "to": "+919552775831"}}, {"event_type": "call.answered", "timestamp": "2025-06-08T14:17:58.721287", "payload": {"call_control_id": "v3:HUT7r5qehC78fhSHoRrndPH8Jusxd4tK4tpMZxiO7lcBsORQb_m6rw", "call_leg_id": "3c39c31e-4445-11f0-af15-02420a1f0b70", "call_session_id": "3c39b4d2-4445-11f0-a0f2-02420a1f0b70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T08:47:40.203725Z", "to": "+919552775831"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T14:18:07.736465", "payload": {"call_control_id": "v3:HUT7r5qehC78fhSHoRrndPH8Jusxd4tK4tpMZxiO7lcBsORQb_m6rw", "call_leg_id": "3c39c31e-4445-11f0-af15-02420a1f0b70", "call_quality_stats": {"inbound": {"jitter_max_variance": "200.00", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "436", "skip_packet_count": "912"}, "outbound": {"packet_count": "1293", "skip_packet_count": "0"}}, "call_session_id": "3c39b4d2-4445-11f0-a0f2-02420a1f0b70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T08:48:08.603724Z", "from": "+19199256164", "hangup_cause": "normal_clearing", "hangup_source": "callee", "sip_hangup_cause": "200", "start_time": "2025-06-08T08:47:40.203725Z", "to": "+919552775831"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T14:18:07.764905", "payload": {"call_control_id": "v3:0KdcT3x1Er-420ZsyqgPthJ2kXNVnJpBsE3unsSvoSANwnFxSflFRg", "call_leg_id": "3d13c08c-4445-11f0-8254-02420a1f0b70", "call_quality_stats": {"inbound": {"jitter_max_variance": "0.00", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "1266", "skip_packet_count": "14"}, "outbound": {"packet_count": "417", "skip_packet_count": "0"}}, "call_session_id": "3c39b4d2-4445-11f0-a0f2-02420a1f0b70", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T08:48:08.583724Z", "from": "+19199256164", "hangup_cause": "normal_clearing", "hangup_source": "callee", "sip_hangup_cause": "200", "start_time": "2025-06-08T08:47:41.763727Z", "to": "+919552775831"}}], "bridged_at": "2025-06-08T14:17:42.335728", "answered_at": "2025-06-08T14:17:58.708221", "hangup_cause": "normal_clearing", "completed_at": "2025-06-08T14:18:07.751621", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.185296"}, {"id": "3dad04a9-5a37-45d9-be37-0b407c5a5897", "timestamp": "2025-06-08T11:02:55.757020", "contact_name": "CB", "contact_phone": "+918149718077", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749352097", "status": "no_answer", "notes": "", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.185296"}, {"id": "4ec684c5-88ed-4693-9c8e-2db3c931c472", "timestamp": "2025-06-08T11:02:54.641788", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19858539054", "agent_name": "<PERSON>", "campaign_id": "campaign_1749352097", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.185296"}, {"id": "b7c40ce1-cf1f-4ddb-beb5-6dd410c69f51", "timestamp": "2025-06-08T11:02:54.538276", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749352097", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.186286"}, {"id": "682659e5-fab1-48b2-80bd-614b502c50ac", "timestamp": "2025-06-08T10:59:04.454655", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19858539054", "agent_name": "<PERSON>", "campaign_id": "campaign_1749352097", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.186286"}, {"id": "1b494e28-696b-485b-b0ce-9bf004a3f52b", "timestamp": "2025-06-08T10:59:03.928443", "contact_name": "CB", "contact_phone": "+918149718077", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749352097", "status": "no_answer", "notes": "", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.186286"}, {"id": "f728f53e-3963-4418-bc2a-3baecbb1e051", "timestamp": "2025-06-08T10:59:03.827310", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749352097", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.186286"}, {"id": "334c649d-2444-46a9-a7fd-3d899082bdfc", "timestamp": "2025-06-08T10:56:04.517700", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19858539054", "agent_name": "<PERSON>", "campaign_id": "campaign_1749352097", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "v3:2UoffUQI39XeiiHwzZA_Omf7bT1qjRTo6W6pbL1-5pnTTfZ-seZMWg", "hangup_cause": "normal_clearing", "completed_at": "2025-06-08T11:03:30.454120", "webhook_events": [{"event_type": "call.hangup", "timestamp": "2025-06-08T10:59:15.611993", "payload": {"call_control_id": "v3:gWV4-RS-t9DLaEPgPzv32mBPV6Yi3oHwbsLvyoqPSVU-5eUa7TXVJQ", "call_leg_id": "19e07766-4429-11f0-bf98-02420a1f0a70", "call_quality_stats": null, "call_session_id": "19e06d5c-4429-11f0-81b6-02420a1f0a70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T05:29:16.623907Z", "from": "+19858539054", "hangup_cause": "originator_cancel", "hangup_source": "caller", "sip_hangup_cause": "487", "start_time": "2025-06-08T05:26:16.663969Z", "to": "+917420068477"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T10:59:17.354536", "payload": {"call_control_id": "v3:R6gkFfzeXTrGCqE8OfJ9GFvUdWc8Li1wDcJ7yga1L_YJpWMY_2SDlA", "call_leg_id": "86239f34-4429-11f0-86b1-02420a1f0a70", "call_session_id": "86239584-4429-11f0-8c52-02420a1f0a70", "caller_id_name": "+19858539054", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "direction": "incoming", "from": "+19858539054", "from_sip_uri": "+<EMAIL>:9000", "offered_codecs": "G722,PCMU,PCMA", "start_time": "2025-06-08T05:29:18.308531Z", "state": "parked", "to": "+917420068477", "to_sip_uri": "+<EMAIL>"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T11:02:17.249940", "payload": {"call_control_id": "v3:R6gkFfzeXTrGCqE8OfJ9GFvUdWc8Li1wDcJ7yga1L_YJpWMY_2SDlA", "call_leg_id": "86239f34-4429-11f0-86b1-02420a1f0a70", "call_quality_stats": null, "call_session_id": "86239584-4429-11f0-8c52-02420a1f0a70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T05:32:18.268684Z", "from": "+19858539054", "hangup_cause": "originator_cancel", "hangup_source": "caller", "sip_hangup_cause": "487", "start_time": "2025-06-08T05:29:18.308531Z", "to": "+917420068477"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T11:03:11.047216", "payload": {"call_control_id": "v3:2UoffUQI39XeiiHwzZA_Omf7bT1qjRTo6W6pbL1-5pnTTfZ-seZMWg", "call_leg_id": "104f54c8-442a-11f0-9f52-02420a1f0b70", "call_session_id": "104f4992-442a-11f0-a800-02420a1f0b70", "caller_id_name": "+19858539054", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "direction": "incoming", "from": "+19858539054", "from_sip_uri": "+<EMAIL>:9000", "offered_codecs": "G722,PCMU,PCMA", "start_time": "2025-06-08T05:33:10.123728Z", "state": "parked", "to": "+917420068477", "to_sip_uri": "+<EMAIL>"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T11:03:12.412346", "payload": {"call_control_id": "v3:2UoffUQI39XeiiHwzZA_Omf7bT1qjRTo6W6pbL1-5pnTTfZ-seZMWg", "call_leg_id": "104f54c8-442a-11f0-9f52-02420a1f0b70", "call_session_id": "104f4992-442a-11f0-a800-02420a1f0b70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+19858539054", "start_time": "2025-06-08T05:33:10.123728Z", "to": "+917420068477"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T11:03:12.423406", "payload": {"call_control_id": "v3:k9yVjKp07rotthhU848UnvAo39VHsrqgVvqhrhFb_Gb12c7Yd11PWQ", "call_leg_id": "115b51aa-442a-11f0-bcbb-02420a1f0b70", "call_session_id": "104f4992-442a-11f0-a800-02420a1f0b70", "client_state": null, "connection_id": "2702376459367876227", "from": "+19858539054", "start_time": "2025-06-08T05:33:10.123728Z", "to": "+917420068477"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T11:03:12.627162", "payload": {"call_control_id": "v3:k9yVjKp07rotthhU848UnvAo39VHsrqgVvqhrhFb_Gb12c7Yd11PWQ", "call_leg_id": "115b51aa-442a-11f0-bcbb-02420a1f0b70", "call_session_id": "104f4992-442a-11f0-a800-02420a1f0b70", "client_state": null, "connection_id": "2702376459367876227", "direction": "outgoing", "from": "+19858539054", "state": "bridging", "to": "+917420068477"}}, {"event_type": "call.answered", "timestamp": "2025-06-08T11:03:17.649706", "payload": {"call_control_id": "v3:k9yVjKp07rotthhU848UnvAo39VHsrqgVvqhrhFb_Gb12c7Yd11PWQ", "call_leg_id": "115b51aa-442a-11f0-bcbb-02420a1f0b70", "call_session_id": "104f4992-442a-11f0-a800-02420a1f0b70", "client_state": null, "connection_id": "2702376459367876227", "from": "+19858539054", "start_time": "2025-06-08T05:33:12.003727Z", "to": "+917420068477"}}, {"event_type": "call.answered", "timestamp": "2025-06-08T11:03:17.664925", "payload": {"call_control_id": "v3:2UoffUQI39XeiiHwzZA_Omf7bT1qjRTo6W6pbL1-5pnTTfZ-seZMWg", "call_leg_id": "104f54c8-442a-11f0-9f52-02420a1f0b70", "call_session_id": "104f4992-442a-11f0-a800-02420a1f0b70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+19858539054", "start_time": "2025-06-08T05:33:10.123728Z", "to": "+917420068477"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T11:03:30.280490", "payload": {"call_control_id": "v3:2UoffUQI39XeiiHwzZA_Omf7bT1qjRTo6W6pbL1-5pnTTfZ-seZMWg", "call_leg_id": "104f54c8-442a-11f0-9f52-02420a1f0b70", "call_quality_stats": {"inbound": {"jitter_max_variance": "433.33", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "601", "skip_packet_count": "362"}, "outbound": {"packet_count": "909", "skip_packet_count": "0"}}, "call_session_id": "104f4992-442a-11f0-a800-02420a1f0b70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T05:33:31.163726Z", "from": "+19858539054", "hangup_cause": "normal_clearing", "hangup_source": "callee", "sip_hangup_cause": "200", "start_time": "2025-06-08T05:33:10.123728Z", "to": "+917420068477"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T11:03:30.464118", "payload": {"call_control_id": "v3:k9yVjKp07rotthhU848UnvAo39VHsrqgVvqhrhFb_Gb12c7Yd11PWQ", "call_leg_id": "115b51aa-442a-11f0-bcbb-02420a1f0b70", "call_quality_stats": {"inbound": {"jitter_max_variance": "800.00", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "879", "skip_packet_count": "17"}, "outbound": {"packet_count": "586", "skip_packet_count": "0"}}, "call_session_id": "104f4992-442a-11f0-a800-02420a1f0b70", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T05:33:31.143726Z", "from": "+19858539054", "hangup_cause": "normal_clearing", "hangup_source": "callee", "sip_hangup_cause": "200", "start_time": "2025-06-08T05:33:12.003727Z", "to": "+917420068477"}}], "call_session_id": "104f4992-442a-11f0-a800-02420a1f0b70", "transfer_status": "failed", "bridged_at": "2025-06-08T11:03:12.414336", "answered_at": "2025-06-08T11:03:17.659363", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.186286"}, {"id": "f20fc4ce-3cff-4288-a646-a83dc5e555de", "timestamp": "2025-06-08T10:56:04.114820", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749352097", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "v3:_5vnkSjI8NRsd_oNI6yc3m4ikk7th1orw6hrOuoPra6G6hQQ_lbdSA", "hangup_cause": "normal_clearing", "completed_at": "2025-06-08T11:04:03.491270", "webhook_events": [{"event_type": "call.hangup", "timestamp": "2025-06-08T10:59:16.135522", "payload": {"call_control_id": "v3:E-HetkzmXBL-VGu7szJysBsVbyRF_v6W5ZeNn_uMARwon64CNcHl8g", "call_leg_id": "1a09f3ac-4429-11f0-a48c-02420a1f0a70", "call_quality_stats": null, "call_session_id": "1a09e9f2-4429-11f0-b2ac-02420a1f0a70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T05:29:16.928533Z", "from": "+19199256164", "hangup_cause": "originator_cancel", "hangup_source": "caller", "sip_hangup_cause": "487", "start_time": "2025-06-08T05:26:16.948528Z", "to": "+919552775831"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T10:59:16.864968", "payload": {"call_control_id": "v3:YfPxvO0st4Pl2xUwSMSwtlAbMgGU509ju49lVl7U6Mu1SnadIcSKCA", "call_leg_id": "85bc6a76-4429-11f0-96a7-02420a1f0b70", "call_session_id": "85bc5d4c-4429-11f0-8571-02420a1f0b70", "caller_id_name": "+19199256164", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "direction": "incoming", "from": "+19199256164", "from_sip_uri": "+<EMAIL>:9000", "offered_codecs": "G722,PCMU,PCMA", "start_time": "2025-06-08T05:29:17.623979Z", "state": "parked", "to": "+919552775831", "to_sip_uri": "+<EMAIL>"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T11:02:16.858623", "payload": {"call_control_id": "v3:YfPxvO0st4Pl2xUwSMSwtlAbMgGU509ju49lVl7U6Mu1SnadIcSKCA", "call_leg_id": "85bc6a76-4429-11f0-96a7-02420a1f0b70", "call_quality_stats": null, "call_session_id": "85bc5d4c-4429-11f0-8571-02420a1f0b70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T05:32:17.603970Z", "from": "+19199256164", "hangup_cause": "originator_cancel", "hangup_source": "caller", "sip_hangup_cause": "487", "start_time": "2025-06-08T05:29:17.623979Z", "to": "+919552775831"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T11:03:08.157171", "payload": {"call_control_id": "v3:W842itcZgvRvoniD-0CYIblJXidKAXeSTvEniHIB5U8eiQ9lQ-sSmg", "call_leg_id": "0f26df76-442a-11f0-acd5-02420a1f0a70", "call_session_id": "0f26d206-442a-11f0-958b-02420a1f0a70", "caller_id_name": "+19199256164", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "direction": "incoming", "from": "+19199256164", "from_sip_uri": "+<EMAIL>:9000", "offered_codecs": "G722,PCMU,PCMA", "start_time": "2025-06-08T05:33:08.168601Z", "state": "parked", "to": "+919552775831", "to_sip_uri": "+<EMAIL>"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T11:03:09.442562", "payload": {"call_control_id": "v3:_5vnkSjI8NRsd_oNI6yc3m4ikk7th1orw6hrOuoPra6G6hQQ_lbdSA", "call_leg_id": "0fa75a66-442a-11f0-bbe0-02420a1f0a70", "call_session_id": "0f26d206-442a-11f0-958b-02420a1f0a70", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T05:33:08.168601Z", "to": "+919552775831"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T11:03:09.795547", "payload": {"call_control_id": "v3:W842itcZgvRvoniD-0CYIblJXidKAXeSTvEniHIB5U8eiQ9lQ-sSmg", "call_leg_id": "0f26df76-442a-11f0-acd5-02420a1f0a70", "call_session_id": "0f26d206-442a-11f0-958b-02420a1f0a70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T05:33:08.168601Z", "to": "+919552775831"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T11:03:10.171385", "payload": {"call_control_id": "v3:_5vnkSjI8NRsd_oNI6yc3m4ikk7th1orw6hrOuoPra6G6hQQ_lbdSA", "call_leg_id": "0fa75a66-442a-11f0-bbe0-02420a1f0a70", "call_session_id": "0f26d206-442a-11f0-958b-02420a1f0a70", "client_state": null, "connection_id": "2702376459367876227", "direction": "outgoing", "from": "+19199256164", "state": "bridging", "to": "+919552775831"}}, {"event_type": "call.answered", "timestamp": "2025-06-08T11:03:26.067947", "payload": {"call_control_id": "v3:W842itcZgvRvoniD-0CYIblJXidKAXeSTvEniHIB5U8eiQ9lQ-sSmg", "call_leg_id": "0f26df76-442a-11f0-acd5-02420a1f0a70", "call_session_id": "0f26d206-442a-11f0-958b-02420a1f0a70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T05:33:08.168601Z", "to": "+919552775831"}}, {"event_type": "call.answered", "timestamp": "2025-06-08T11:03:26.403174", "payload": {"call_control_id": "v3:_5vnkSjI8NRsd_oNI6yc3m4ikk7th1orw6hrOuoPra6G6hQQ_lbdSA", "call_leg_id": "0fa75a66-442a-11f0-bbe0-02420a1f0a70", "call_session_id": "0f26d206-442a-11f0-958b-02420a1f0a70", "client_state": null, "connection_id": "2702376459367876227", "from": "+19199256164", "start_time": "2025-06-08T05:33:09.148531Z", "to": "+919552775831"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T11:04:03.504311", "payload": {"call_control_id": "v3:W842itcZgvRvoniD-0CYIblJXidKAXeSTvEniHIB5U8eiQ9lQ-sSmg", "call_leg_id": "0f26df76-442a-11f0-acd5-02420a1f0a70", "call_quality_stats": {"inbound": {"jitter_max_variance": "107.87", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "1836", "skip_packet_count": "930"}, "outbound": {"packet_count": "2717", "skip_packet_count": "0"}}, "call_session_id": "0f26d206-442a-11f0-958b-02420a1f0a70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T05:34:04.408530Z", "from": "+19199256164", "hangup_cause": "normal_clearing", "hangup_source": "callee", "sip_hangup_cause": "200", "start_time": "2025-06-08T05:33:08.168601Z", "to": "+919552775831"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T11:04:03.507313", "payload": {"call_control_id": "v3:_5vnkSjI8NRsd_oNI6yc3m4ikk7th1orw6hrOuoPra6G6hQQ_lbdSA", "call_leg_id": "0fa75a66-442a-11f0-bbe0-02420a1f0a70", "call_quality_stats": {"inbound": {"jitter_max_variance": "25.00", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "2690", "skip_packet_count": "13"}, "outbound": {"packet_count": "1828", "skip_packet_count": "0"}}, "call_session_id": "0f26d206-442a-11f0-958b-02420a1f0a70", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T05:34:04.368530Z", "from": "+19199256164", "hangup_cause": "normal_clearing", "hangup_source": "callee", "sip_hangup_cause": "200", "start_time": "2025-06-08T05:33:09.148531Z", "to": "+919552775831"}}], "call_session_id": "0f26d206-442a-11f0-958b-02420a1f0a70", "transfer_status": "failed", "bridged_at": "2025-06-08T11:03:09.784371", "answered_at": "2025-06-08T11:03:26.394172", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.187283"}, {"id": "ba7040f9-1b45-44ec-8def-3160dde7ff7d", "timestamp": "2025-06-08T10:56:02.489121", "contact_name": "CB", "contact_phone": "+918149718077", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749352097", "status": "no_answer", "notes": "", "call_duration": 0, "recording_url": "", "telnyx_call_id": "v3:KgMpORjq5pqQ9JY6KSPUot_TiB426mELbn0XDcuUCF6yx1Mu7VXDBw", "hangup_cause": "normal_clearing", "completed_at": "2025-06-08T11:03:34.758032", "webhook_events": [{"event_type": "call.hangup", "timestamp": "2025-06-08T10:59:16.153792", "payload": {"call_control_id": "v3:4EtYSrGETBZlGVt6v876x6HLK-GQ7oYvTtYofTIaIXK49dqHCe_-jA", "call_leg_id": "1a405622-4429-11f0-83d4-02420a1f0b70", "call_quality_stats": null, "call_session_id": "1a405122-4429-11f0-8895-02420a1f0b70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T05:29:17.188273Z", "from": "+18162196532", "hangup_cause": "originator_cancel", "hangup_source": "caller", "sip_hangup_cause": "487", "start_time": "2025-06-08T05:26:17.308269Z", "to": "+918149718077"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T10:59:17.253044", "payload": {"call_control_id": "v3:NhpB1Pj2uH8xcCA9mwKQNvk-BgRlTmD4l3H_QLj-WYH2ddR7TTJXVQ", "call_leg_id": "85dc9544-4429-11f0-94d2-02420a1f0a70", "call_session_id": "85dc8b44-4429-11f0-bec4-02420a1f0a70", "caller_id_name": "+18162196532", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "direction": "incoming", "from": "+18162196532", "from_sip_uri": "+<EMAIL>:9000", "offered_codecs": "G722,PCMU,PCMA", "start_time": "2025-06-08T05:29:17.843725Z", "state": "parked", "to": "+918149718077", "to_sip_uri": "+<EMAIL>"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T11:02:17.251944", "payload": {"call_control_id": "v3:NhpB1Pj2uH8xcCA9mwKQNvk-BgRlTmD4l3H_QLj-WYH2ddR7TTJXVQ", "call_leg_id": "85dc9544-4429-11f0-94d2-02420a1f0a70", "call_quality_stats": null, "call_session_id": "85dc8b44-4429-11f0-bec4-02420a1f0a70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T05:32:17.823726Z", "from": "+18162196532", "hangup_cause": "originator_cancel", "hangup_source": "caller", "sip_hangup_cause": "487", "start_time": "2025-06-08T05:29:17.843725Z", "to": "+918149718077"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T11:03:09.652715", "payload": {"call_control_id": "v3:KgMpORjq5pqQ9JY6KSPUot_TiB426mELbn0XDcuUCF6yx1Mu7VXDBw", "call_leg_id": "0ff7643e-442a-11f0-ba0f-02420a1f0b70", "call_session_id": "0ff75638-442a-11f0-b3ca-02420a1f0b70", "caller_id_name": "+18162196532", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "direction": "incoming", "from": "+18162196532", "from_sip_uri": "+<EMAIL>:9000", "offered_codecs": "G722,PCMU,PCMA", "start_time": "2025-06-08T05:33:09.528530Z", "state": "parked", "to": "+918149718077", "to_sip_uri": "+<EMAIL>"}}, {"event_type": "call.initiated", "timestamp": "2025-06-08T11:03:10.682397", "payload": {"call_control_id": "v3:-e_D0ciX-nvStZitRLOGh6TQkqbeTjhDFr6ZPTOUbLitcEPPmuIIDg", "call_leg_id": "108ad98a-442a-11f0-a5a7-02420a1f0a70", "call_session_id": "0ff75638-442a-11f0-b3ca-02420a1f0b70", "client_state": null, "connection_id": "2702376459367876227", "direction": "outgoing", "from": "+18162196532", "state": "bridging", "to": "+918149718077"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T11:03:10.826121", "payload": {"call_control_id": "v3:-e_D0ciX-nvStZitRLOGh6TQkqbeTjhDFr6ZPTOUbLitcEPPmuIIDg", "call_leg_id": "108ad98a-442a-11f0-a5a7-02420a1f0a70", "call_session_id": "0ff75638-442a-11f0-b3ca-02420a1f0b70", "client_state": null, "connection_id": "2702376459367876227", "from": "+18162196532", "start_time": "2025-06-08T05:33:09.528530Z", "to": "+918149718077"}}, {"event_type": "call.bridged", "timestamp": "2025-06-08T11:03:10.828633", "payload": {"call_control_id": "v3:KgMpORjq5pqQ9JY6KSPUot_TiB426mELbn0XDcuUCF6yx1Mu7VXDBw", "call_leg_id": "0ff7643e-442a-11f0-ba0f-02420a1f0b70", "call_session_id": "0ff75638-442a-11f0-b3ca-02420a1f0b70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+18162196532", "start_time": "2025-06-08T05:33:09.528530Z", "to": "+918149718077"}}, {"event_type": "call.answered", "timestamp": "2025-06-08T11:03:16.662513", "payload": {"call_control_id": "v3:-e_D0ciX-nvStZitRLOGh6TQkqbeTjhDFr6ZPTOUbLitcEPPmuIIDg", "call_leg_id": "108ad98a-442a-11f0-a5a7-02420a1f0a70", "call_session_id": "0ff75638-442a-11f0-b3ca-02420a1f0b70", "client_state": null, "connection_id": "2702376459367876227", "from": "+18162196532", "start_time": "2025-06-08T05:33:10.648527Z", "to": "+918149718077"}}, {"event_type": "call.answered", "timestamp": "2025-06-08T11:03:16.769518", "payload": {"call_control_id": "v3:KgMpORjq5pqQ9JY6KSPUot_TiB426mELbn0XDcuUCF6yx1Mu7VXDBw", "call_leg_id": "0ff7643e-442a-11f0-ba0f-02420a1f0b70", "call_session_id": "0ff75638-442a-11f0-b3ca-02420a1f0b70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "from": "+18162196532", "start_time": "2025-06-08T05:33:09.528530Z", "to": "+918149718077"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T11:03:34.732620", "payload": {"call_control_id": "v3:-e_D0ciX-nvStZitRLOGh6TQkqbeTjhDFr6ZPTOUbLitcEPPmuIIDg", "call_leg_id": "108ad98a-442a-11f0-a5a7-02420a1f0a70", "call_quality_stats": {"inbound": {"jitter_max_variance": "86.25", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "1175", "skip_packet_count": "13"}, "outbound": {"packet_count": "864", "skip_packet_count": "0"}}, "call_session_id": "0ff75638-442a-11f0-b3ca-02420a1f0b70", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T05:33:35.488527Z", "from": "+18162196532", "hangup_cause": "normal_clearing", "hangup_source": "callee", "sip_hangup_cause": "200", "start_time": "2025-06-08T05:33:10.648527Z", "to": "+918149718077"}}, {"event_type": "call.hangup", "timestamp": "2025-06-08T11:03:34.767463", "payload": {"call_control_id": "v3:KgMpORjq5pqQ9JY6KSPUot_TiB426mELbn0XDcuUCF6yx1Mu7VXDBw", "call_leg_id": "0ff7643e-442a-11f0-ba0f-02420a1f0b70", "call_quality_stats": {"inbound": {"jitter_max_variance": "800.00", "jitter_packet_count": "0", "mos": "4.50", "packet_count": "874", "skip_packet_count": "372"}, "outbound": {"packet_count": "1197", "skip_packet_count": "0"}}, "call_session_id": "0ff75638-442a-11f0-b3ca-02420a1f0b70", "calling_party_type": "sip", "client_state": null, "connection_id": "2702376459367876227", "end_time": "2025-06-08T05:33:35.488527Z", "from": "+18162196532", "hangup_cause": "normal_clearing", "hangup_source": "callee", "sip_hangup_cause": "200", "start_time": "2025-06-08T05:33:09.528530Z", "to": "+918149718077"}}], "call_session_id": "0ff75638-442a-11f0-b3ca-02420a1f0b70", "transfer_status": "failed", "bridged_at": "2025-06-08T11:03:10.811979", "answered_at": "2025-06-08T11:03:16.763501", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.187283"}, {"id": "ca50824e-d248-4c3e-b31b-dd6dc0e862ce", "timestamp": "2025-06-08T08:42:52.623039", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749352097", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.187283"}, {"id": "84b06cc0-95f1-4868-af74-ef29db55d64a", "timestamp": "2025-06-08T08:42:52.481495", "contact_name": "CB", "contact_phone": "+918149718077", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749352097", "status": "no_answer", "notes": "", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.187283"}, {"id": "c2d5d37a-3064-4ce8-bec0-30dc32f90a4c", "timestamp": "2025-06-08T08:42:52.431950", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19858539054", "agent_name": "<PERSON>", "campaign_id": "campaign_1749352097", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.187283"}, {"id": "b2af2c1c-5529-4f42-bed9-be984c2a84cc", "timestamp": "2025-06-08T08:38:31.100970", "contact_name": "CB", "contact_phone": "+918149718077", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749352097", "status": "no_answer", "notes": "", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.187283"}, {"id": "4112346c-ba6e-4f22-8727-6b8ec579e9e4", "timestamp": "2025-06-08T08:38:29.483225", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749352097", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.188286"}, {"id": "8549c380-380a-47f8-b89a-aff07a9e5b0f", "timestamp": "2025-06-08T08:38:29.183747", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19858539054", "agent_name": "<PERSON>", "campaign_id": "campaign_1749352097", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.188286"}, {"id": "cd58232f-8308-4779-8f0a-b22917c37f9b", "timestamp": "2025-06-08T08:29:58.367005", "contact_name": "CB", "contact_phone": "+918149718077", "from_phone": "+19858539054", "agent_name": "<PERSON>", "campaign_id": "campaign_1749349757", "status": "no_answer", "notes": "", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.188286"}, {"id": "13ff8dc4-ccac-403c-a1ff-29336b8911b8", "timestamp": "2025-06-08T08:29:58.216806", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749349757", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.188286"}, {"id": "91e3b77e-ca26-4b20-a048-e3dffab5b52f", "timestamp": "2025-06-08T08:29:56.958125", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749349757", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.188286"}, {"id": "7e454e67-5a6b-4d9c-91fe-d69497cdbcff", "timestamp": "2025-06-08T08:20:02.025090", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749349757", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.189280"}, {"id": "6e0fc856-d68c-4980-b844-f12e97ab3b9f", "timestamp": "2025-06-08T08:20:01.317550", "contact_name": "CB", "contact_phone": "+918149718077", "from_phone": "+19858539054", "agent_name": "<PERSON>", "campaign_id": "campaign_1749349757", "status": "no_answer", "notes": "", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.189280"}, {"id": "9d004f37-85ab-49de-812c-d2eec4c8f7b1", "timestamp": "2025-06-08T08:20:01.191719", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749349757", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.189280"}, {"id": "0d851cc0-d829-486b-b3e8-7672eddff542", "timestamp": "2025-06-08T08:01:47.832168", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749349757", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.189280"}, {"id": "0872e0b6-d36c-43b5-823c-625d838adaee", "timestamp": "2025-06-08T08:01:47.793632", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749349757", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.189280"}, {"id": "5693d88a-01ae-4c23-afbd-4ea668ee5fe6", "timestamp": "2025-06-08T08:01:47.740507", "contact_name": "CB", "contact_phone": "+918149718077", "from_phone": "+19858539054", "agent_name": "<PERSON>", "campaign_id": "campaign_1749349757", "status": "no_answer", "notes": "", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "checked_at": "2025-06-08T08:39:31.132214", "check_error": "type object 'Call' has no attribute 'list'"}, {"id": "9446b6e3-0e99-40f7-adc8-f0e4402af114", "timestamp": "2025-06-08T07:59:27.411695", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749349757", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.189280"}, {"id": "1132bc78-13b5-4322-a0cf-d62abd1cb7c2", "timestamp": "2025-06-08T07:59:27.399162", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749349757", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.190280"}, {"id": "bec0146b-f6b6-4132-bb8f-522f5af06824", "timestamp": "2025-06-08T07:59:26.945202", "contact_name": "CB", "contact_phone": "+918149718077", "from_phone": "+19858539054", "agent_name": "<PERSON>", "campaign_id": "campaign_1749349757", "status": "no_answer", "notes": "", "call_duration": 0, "recording_url": "", "telnyx_call_id": "v3:KR7ys4fq1rT74Tb_N7YxkiXZjQeAkJXZ_1jcpP4MGcqbJw8BvK4Y-Q", "checked_at": "2025-06-08T08:30:58.440283", "check_error": "type object 'Call' has no attribute 'list'", "call_session_id": "7fbdcbb4-4416-11f0-833a-02420a1f0a70", "ended_at": "2025-06-08T08:46:06.328671", "hangup_cause": "originator_cancel"}, {"id": "6656e971-6584-4bae-b7ed-853da1dee2fb", "timestamp": "2025-06-08T07:53:23.133990", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749348382", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.190280"}, {"id": "feb39370-6050-44c0-a9e1-23f6fc8c6f41", "timestamp": "2025-06-08T07:53:22.115099", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749348382", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.190280"}, {"id": "e349d13f-bc0a-499f-9b6a-3f7952db2c1d", "timestamp": "2025-06-08T07:46:41.716055", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+19858539054", "agent_name": "<PERSON>", "campaign_id": "campaign_1749348744", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "ended_at": "2025-06-08T08:46:06.473101", "hangup_cause": "originator_cancel"}, {"id": "0c661d8b-e3bb-4e2b-9ec7-dbc4f3e9ffa6", "timestamp": "2025-06-08T07:46:41.146435", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19858539054", "agent_name": "<PERSON>", "campaign_id": "campaign_1749348744", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "ended_at": "2025-06-08T08:46:05.401566", "hangup_cause": "originator_cancel"}, {"id": "31b214ed-214a-405d-9351-3772e31f0aef", "timestamp": "2025-06-08T07:45:53.503231", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749348382", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "v3:A-6cVDpJrAtYqTtL1axsgK_4E1BKe3moglAFKRKtsNMlPcFVI6PKwA", "call_session_id": "800b9092-4416-11f0-a902-02420a1f0a70", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.190280"}, {"id": "b4b82106-456d-4225-8e94-c7a94b385f30", "timestamp": "2025-06-08T07:45:52.413656", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749348382", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "v3:OqYKRCgDhTXwB5qg8v0zqDxW-CQAVBx-Wl-Qr6bYnkCrjjZjAIVxuA", "call_session_id": "7f2f4b50-4416-11f0-993b-02420a1f0a70", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.190280"}, {"id": "0608f6dd-c663-4367-928f-313a29c37459", "timestamp": "2025-06-08T07:42:33.961070", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749348382", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "v3:JxCP5VgzVW1baW5cWFmlBkwQdkYgTesVSVP583pR5s7fN80QWQ1j2A", "call_session_id": "e5304f7c-4415-11f0-8d60-02420a1f0a70", "ended_at": "2025-06-08T08:41:46.885490", "hangup_cause": "originator_cancel"}, {"id": "e08aa3ad-2cdd-4cfc-af06-69438749e47b", "timestamp": "2025-06-08T07:42:32.823709", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749348382", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "v3:hKeXJdxKNq9ztGacDBg7BPKvUHP4OkKsWkZgAEFYcMytriQ7yfUAlg", "call_session_id": "e52d626c-4415-11f0-9fd2-02420a1f0b70", "ended_at": "2025-06-08T08:41:47.749077", "hangup_cause": "originator_cancel"}, {"id": "08ba4566-d9f9-4b52-a681-7694d61cacd7", "timestamp": "2025-06-08T07:36:32.311310", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749348382", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.191281"}, {"id": "52e8ad9b-836b-4ad8-b39b-3b3953175be5", "timestamp": "2025-06-08T07:36:30.096097", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749348382", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "checked_at": "2025-06-08T19:04:17.033796", "check_error": "type object 'Call' has no attribute 'list'"}, {"id": "5d3317d7-892c-4f6d-823f-201cf5de5f13", "timestamp": "2025-06-08T07:34:59.664416", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749348165", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.191281"}, {"id": "4da39188-4937-4edc-a1ad-f4a8e3e3213a", "timestamp": "2025-06-08T07:34:59.539290", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749348165", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "checked_at": "2025-06-08T19:03:21.282091", "check_error": "type object 'Call' has no attribute 'list'"}, {"id": "b4a64c60-0e89-463c-b8d9-04dea854ede4", "timestamp": "2025-06-08T07:32:53.252734", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749348165", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "checked_at": "2025-06-08T19:02:10.959597", "check_error": "type object 'Call' has no attribute 'list'"}, {"id": "8402a13a-506a-4b03-921f-b3614c10e073", "timestamp": "2025-06-08T07:32:52.996003", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749348165", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.191281"}, {"id": "362bffba-ca99-4c25-b74f-dca273ff65f6", "timestamp": "2025-06-08T07:30:02.763282", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749346559", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "checked_at": "2025-06-08T18:18:57.780408", "check_error": "type object 'Call' has no attribute 'list'"}, {"id": "2ef3cb54-2425-414e-b1f2-73543c4f3dcc", "timestamp": "2025-06-08T07:30:02.628592", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749346559", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.191281"}, {"id": "6a654819-3fcf-4416-b134-08e9d0eb8b9d", "timestamp": "2025-06-08T07:07:51.921012", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749346559", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.191281"}, {"id": "d5b69bb6-f21f-4b68-a9d8-08b05aa493f7", "timestamp": "2025-06-08T07:07:50.506617", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749346559", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "checked_at": "2025-06-08T17:52:15.293452", "check_error": "type object 'Call' has no attribute 'list'"}, {"id": "e4f2c3b7-b2b9-4013-aad0-ed3bc4c10f36", "timestamp": "2025-06-08T07:06:07.199672", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749346559", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.192282"}, {"id": "353cf8ff-5e33-4860-9a3b-0b4f6098d3f4", "timestamp": "2025-06-08T07:06:06.677330", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749346559", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "checked_at": "2025-06-08T15:29:39.509727", "check_error": "type object 'Call' has no attribute 'list'"}, {"id": "ba1be350-3d7c-4fbc-95fe-6673834d04ea", "timestamp": "2025-06-08T07:03:13.519477", "contact_name": "<PERSON> rose", "contact_phone": "+917420854930", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749346385", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "checked_at": "2025-06-08T15:28:38.267929", "check_error": "type object 'Call' has no attribute 'list'"}, {"id": "2e42db23-d1b7-44f7-b883-17e1b6d59e05", "timestamp": "2025-06-08T07:03:13.025978", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749346385", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.192282"}, {"id": "68f4ec59-941a-4b32-8912-7025fc39af82", "timestamp": "2025-06-08T07:00:16.153529", "contact_name": "<PERSON>", "contact_phone": "+917420854930", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749345880", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.192282"}, {"id": "fa98a76f-0298-4f91-a8f9-feb66b917864", "timestamp": "2025-06-08T07:00:15.663784", "contact_name": "<PERSON> rose", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749345880", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "checked_at": "2025-06-08T08:39:29.596168", "check_error": "type object 'Call' has no attribute 'list'"}, {"id": "c87f3ee5-e6c2-4a42-82da-7e926cd3297a", "timestamp": "2025-06-08T06:54:49.316149", "contact_name": "<PERSON>", "contact_phone": "+917420854930", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749345880", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.192282"}, {"id": "9891825a-9e47-433e-bd53-d1cb1e2df046", "timestamp": "2025-06-08T06:54:48.678229", "contact_name": "<PERSON> rose", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749345880", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "checked_at": "2025-06-08T08:30:58.269435", "check_error": "type object 'Call' has no attribute 'list'"}, {"id": "184f746f-0e9e-4399-bd5f-be4dc88eb216", "timestamp": "2025-06-08T06:48:34.321869", "contact_name": "<PERSON>", "contact_phone": "+917420854930", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749345503", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.193288"}, {"id": "0c175501-a802-450d-803c-93a0e3ba848d", "timestamp": "2025-06-08T06:48:33.532650", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749345503", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.193288"}, {"id": "57bd73ac-966d-4819-b602-14264<PERSON><PERSON><PERSON><PERSON>", "timestamp": "2025-06-07T12:17:19.701207", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749274957", "status": "no_answer", "notes": "Follow-up call needed", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.193288"}, {"id": "0e814718-7d2b-45e1-8c45-d13b41c13e37", "timestamp": "2025-06-07T12:17:19.627033", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749274957", "status": "no_answer", "notes": "Interested in our services", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.193288"}, {"id": "5686c0fd-d020-4250-a1f2-e7451d9ac3ab", "timestamp": "2025-06-07T12:17:19.223139", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749274957", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.194287"}, {"id": "eb9083f2-da4c-4c7e-a12e-2b62481efd76", "timestamp": "2025-06-07T12:13:18.287748", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749274957", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.194287"}, {"id": "b645f656-730f-438d-809d-af87811d589a", "timestamp": "2025-06-07T12:13:18.208615", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749274957", "status": "no_answer", "notes": "Interested in our services", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.194287"}, {"id": "cf7cb10f-c601-454b-942d-dd9493d43424", "timestamp": "2025-06-07T12:13:17.909125", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749274957", "status": "no_answer", "notes": "Follow-up call needed", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.194287"}, {"id": "5d4bd297-eec0-40ba-acb6-4a6eed9e1efe", "timestamp": "2025-06-07T12:09:32.809882", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749274957", "status": "no_answer", "notes": "Interested in our services", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.194287"}, {"id": "fe9b8992-256c-42e3-81c9-2174cbab98b7", "timestamp": "2025-06-07T12:09:32.272800", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749274957", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "checked_at": "2025-06-08T19:04:17.934047", "check_error": "type object 'Call' has no attribute 'list'"}, {"id": "bbe1aab0-318f-4668-a1ee-c2e2f8055c98", "timestamp": "2025-06-07T12:09:32.034165", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749274957", "status": "no_answer", "notes": "Follow-up call needed", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.195282"}, {"id": "ec6919df-3c96-4430-856e-22901a4eb5f8", "timestamp": "2025-06-07T12:04:07.851703", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749271844", "status": "no_answer", "notes": "Interested in our services", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.195282"}, {"id": "d5a46975-da2c-4f7a-8908-0d2d6435b1a2", "timestamp": "2025-06-07T12:04:07.725115", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749271844", "status": "no_answer", "notes": "Follow-up call needed", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.195282"}, {"id": "de4b2e1f-3cb7-4916-87fb-4461124b8f7e", "timestamp": "2025-06-07T12:04:07.624733", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749271844", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "checked_at": "2025-06-08T19:03:21.281088", "check_error": "type object 'Call' has no attribute 'list'"}, {"id": "a1858d4b-ae13-4995-b39d-8d36e70feaa2", "timestamp": "2025-06-07T11:12:45.661368", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749274957", "status": "no_answer", "notes": "Follow-up call needed", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.195282"}, {"id": "0af7238a-c70e-4b6a-8cdf-e4e96d03fc02", "timestamp": "2025-06-07T11:12:45.430504", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749274957", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "checked_at": "2025-06-08T19:02:10.960595", "check_error": "type object 'Call' has no attribute 'list'"}, {"id": "f2875483-afeb-4042-8e89-af9b9bca7d2c", "timestamp": "2025-06-07T11:12:44.492837", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749274957", "status": "no_answer", "notes": "Interested in our services", "call_duration": 0, "recording_url": "https://example.com/recording123", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.195282"}, {"id": "592d050c-33c1-404d-9ceb-0b65741e31cf", "timestamp": "2025-06-07T11:07:32.799405", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749274643", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "checked_at": "2025-06-08T08:30:57.036944", "check_error": "type object 'Call' has no attribute 'list'"}, {"id": "ea2d7731-e578-4d67-991b-5c977a97e0fb", "timestamp": "2025-06-07T11:07:32.045340", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749274643", "status": "no_answer", "notes": "Interested in our services", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.196281"}, {"id": "667a740d-f4ac-43ba-ad93-798758c1f201", "timestamp": "2025-06-07T11:07:31.992298", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749274643", "status": "no_answer", "notes": "Follow-up call needed", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.196281"}, {"id": "882dd7c4-d542-4921-abf4-f5bc5b7bea43", "timestamp": "2025-06-07T10:56:00.420592", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749272177", "status": "no_answer", "notes": "Interested in our services", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.196281"}, {"id": "7d777dd2-1e2e-4f02-b257-706d28aa63d1", "timestamp": "2025-06-07T10:56:00.332774", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749272177", "status": "no_answer", "notes": "Follow-up call needed", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.196281"}, {"id": "a6a0cfb6-5070-45b3-9a08-0067093b74a3", "timestamp": "2025-06-07T10:56:00.170037", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749272177", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "checked_at": "2025-06-08T08:39:29.239280", "check_error": "type object 'Call' has no attribute 'list'"}, {"id": "b2378116-c875-4e84-93c7-26118cadbd68", "timestamp": "2025-06-07T08:32:14.617959", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749265326", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "checked_at": "2025-06-08T15:28:38.927850", "check_error": "type object 'Call' has no attribute 'list'"}, {"id": "17f673b8-4d73-44d9-a7ef-f77a5effebb6", "timestamp": "2025-06-07T08:32:13.582504", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749265326", "status": "no_answer", "notes": "Follow-up call needed", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.196281"}, {"id": "25c4e786-0b0f-4ce9-84dc-3368a32f7ae7", "timestamp": "2025-06-07T08:32:13.543846", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749265326", "status": "no_answer", "notes": "Interested in our services", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.196281"}, {"id": "e98ecc45-e853-452d-934c-d60c95ab167c", "timestamp": "2025-06-07T08:15:59.432579", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749264349", "status": "no_answer", "notes": "Follow-up call needed", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.197282"}, {"id": "f09ffcfd-d2f3-4f35-8643-989415fc0789", "timestamp": "2025-06-07T08:15:58.730329", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749264349", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "checked_at": "2025-06-08T15:29:40.500637", "check_error": "type object 'Call' has no attribute 'list'"}, {"id": "ef8f9be9-959c-4973-bc3c-776b34d06d21", "timestamp": "2025-06-07T08:15:58.726326", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749264349", "status": "no_answer", "notes": "Interested in our services", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.197282"}, {"id": "07b077fe-0734-4e75-855c-456ec50bd963", "timestamp": "2025-06-07T08:12:16.814131", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749264124", "status": "no_answer", "notes": "Follow-up call needed", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.197282"}, {"id": "9f8646c0-e7e4-4e52-8537-2c387b41b387", "timestamp": "2025-06-07T08:12:16.153842", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749264124", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "checked_at": "2025-06-08T17:52:15.152601", "check_error": "type object 'Call' has no attribute 'list'"}, {"id": "1701b404-ada7-4b3f-9c5d-7cfdc3d20e5e", "timestamp": "2025-06-07T08:12:15.936062", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749264124", "status": "no_answer", "notes": "Interested in our services", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.197282"}, {"id": "7b4678a2-9f7d-4cc0-aef8-41afcf6f84d5", "timestamp": "2025-06-06T11:12:12.503022", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749188523", "status": "no_answer", "notes": "Follow-up call needed", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.197282"}, {"id": "e8b2db0d-24fc-4b7e-b994-100db597c80c", "timestamp": "2025-06-06T11:12:12.345486", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749188523", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "checked_at": "2025-06-08T18:18:57.686639", "check_error": "type object 'Call' has no attribute 'list'"}, {"id": "1e6cbf6c-3483-4acc-a46e-6058339b8a52", "timestamp": "2025-06-06T11:12:12.341928", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749188523", "status": "no_answer", "notes": "Interested in our services", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.197282"}, {"id": "3b091861-7784-4612-9762-918f33e8c7fc", "timestamp": "2025-06-08T19:57:53.526784", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749392842", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.198281"}, {"id": "6b013929-8471-4668-8582-2a4ebf01d41f", "timestamp": "2025-06-08T19:57:54.669866", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749392842", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:08:42.198281"}, {"id": "a6eaefb2-9123-4cfd-9a4c-540ef57e168b", "timestamp": "2025-06-08T20:12:51.167364", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749393756", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:13:14.262396"}, {"id": "96d05ac8-ad74-4dcc-9004-817916ff7f3f", "timestamp": "2025-06-08T20:12:51.178923", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749393756", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:13:14.272942"}, {"id": "ca8767cd-74a2-46e3-a568-ab52831ac9b0", "timestamp": "2025-06-08T20:14:54.988728", "contact_name": "<PERSON> rose", "contact_phone": "+919552775831", "from_phone": "+19199256164", "agent_name": "<PERSON>", "campaign_id": "campaign_1749393889", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:15:14.215527"}, {"id": "57eb6ee2-4262-4170-a179-cb78cc93cb6a", "timestamp": "2025-06-08T20:14:55.010349", "contact_name": "<PERSON>", "contact_phone": "+917420068477", "from_phone": "+18162196532", "agent_name": "<PERSON>", "campaign_id": "campaign_1749393889", "status": "no_answer", "notes": "Demo request", "call_duration": 0, "recording_url": "", "telnyx_call_id": "", "corrected_by_fallback": true, "corrected_at": "2025-06-08T20:15:14.216529"}]