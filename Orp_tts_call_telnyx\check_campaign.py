import json

# Load campaigns
with open('data/campaigns.json', 'r') as f:
    campaigns = json.load(f)

campaign_id = "campaign_1749274957"

if campaign_id in campaigns:
    campaign = campaigns[campaign_id]
    print(f"Campaign: {campaign['name']}")
    print(f"Status: {campaign['status']}")
    print(f"Total contacts: {len(campaign.get('contacts', []))}")
    print(f"Contacts:")
    for i, contact in enumerate(campaign.get('contacts', []), 1):
        print(f"  {i}. {contact['name']} ({contact['phone_number']})")
else:
    print(f"Campaign {campaign_id} not found")
    print("Available campaigns:")
    for cid in campaigns.keys():
        print(f"  {cid}") 