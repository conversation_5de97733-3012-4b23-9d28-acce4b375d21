# Dynamic SIP Trunk Assignment

## Overview

This update solves the issue where all multiprocessing calls were using the same SIP trunk from the environment variable. Now each call process can use its own specific SIP trunk based on the phone number it's calling from.

## Problem Solved

**Before**: All calls used `SIP_OUTBOUND_TRUNK_ID` from environment variables
- ❌ Multiple calls from different numbers used the same trunk
- ❌ Couldn't call from specific Telnyx phone numbers
- ❌ All calls appeared to come from the same number

**After**: Each call uses the appropriate SIP trunk for its source number
- ✅ Each call process gets its own SIP trunk
- ✅ Calls are made from the correct Telnyx phone number
- ✅ Multiple calls can use different phone numbers simultaneously

## How It Works

### 1. SIP Trunk Mapping File
The system now uses `data/sip_trunks.json` to map phone numbers to SIP trunk IDs:

```json
{
  "+19199256164": "ST_mdGsY9Kf6vPJ",
  "+18162196532": "ST_Egm8LAHAWbg4",
  "+19858539054": "ST_LNrYFwQ3Ysfc"
}
```

### 2. Dynamic Environment Variable Assignment
When starting a call process, the system:

1. Loads the SIP trunk mapping from the JSON file
2. Extracts the `from_phone` parameter from call data
3. Looks up the corresponding SIP trunk ID
4. Sets `SIP_OUTBOUND_TRUNK_ID` for that specific process
5. Starts the agent with the correct trunk

### 3. Metadata Passing
Call metadata now includes structured information:

```json
{
  "target_phone": "+1234567890",
  "from_phone": "+19199256164",
  "sip_trunk_id": "ST_mdGsY9Kf6vPJ"
}
```

## Code Changes Made

### 1. backend.py
- **`start_outbound_call_process()`**: Added SIP trunk mapping logic
- **`create_process()`**: Added `from_phone` parameter support
- **`build_outbound_call_command()`**: Enhanced metadata structure
- **New endpoints**: `/api/sip-trunks` for managing mappings

### 2. simple_backend.py
- Already had the correct structure for passing SIP trunk info
- Uses the mapping system to determine SIP trunk per call

### 3. orp.py (and other agent files)
- Already had logic to parse and use specific SIP trunk from metadata
- No changes needed - it already supports dynamic trunk assignment

## Usage Examples

### Making a Call with Specific Source Number

**Using backend.py:**
```python
call_data = {
    "phoneNumber": "+1234567890",      # Number to call TO
    "from_phone": "+19199256164",      # Telnyx number to call FROM
    "agentPrompt": "Your prompt here",
    "voiceId": "tara"
}

response = requests.post("http://localhost:9090/api/processes", json=call_data)
```

**Using simple_backend.py:**
```python
call_data = {
    "phone_number": "+1234567890",
    "from_phone": "+19199256164",
    "agent_name_for_dispatch": "my-agent"
}

response = requests.post("http://localhost:9090/make_call", json=call_data)
```

### Managing SIP Trunk Mappings

**Get current mappings:**
```bash
curl http://localhost:9090/api/sip-trunks
```

**Update a mapping:**
```bash
curl -X POST http://localhost:9090/api/sip-trunks \
  -H "Content-Type: application/json" \
  -d '{"phone_number": "+19199256164", "sip_trunk_id": "ST_newTrunkId"}'
```

## Testing

Run the test script to verify everything works:

```bash
cd Orp_tts_call_telnyx
python test_dynamic_sip_trunks.py
```

The test will:
1. Check current SIP trunk mappings
2. Make test calls with specific `from_phone` numbers
3. Verify the logs show correct SIP trunk assignment
4. Test both backend systems

## Key Benefits

1. **Multi-tenant calling**: Different campaigns can use different phone numbers
2. **Proper caller ID**: Calls appear from the intended Telnyx number
3. **Scalability**: Multiple calls can run simultaneously with different trunks
4. **Flexibility**: Easy to add new phone numbers and SIP trunks
5. **Backward compatibility**: Still works with environment variable fallback

## Environment Variables

The system still respects the original environment variable as a fallback:

- `SIP_OUTBOUND_TRUNK_ID`: Default trunk used when no mapping exists
- Per-process assignment overrides this for specific calls

## File Structure

```
Orp_tts_call_telnyx/
├── data/
│   └── sip_trunks.json          # Phone number → SIP trunk mapping
├── backend.py                   # Enhanced with dynamic SIP assignment
├── simple_backend.py           # Already supports the feature
├── orp.py                      # Agent with SIP trunk parsing
└── test_dynamic_sip_trunks.py  # Test script
```

## Migration Notes

**Existing installations:**
1. Your existing calls will continue to work with the environment variable
2. To use the new feature, create `data/sip_trunks.json` with your mappings
3. Pass `from_phone` parameter in your call requests
4. The system will automatically use the mapped SIP trunk

**No breaking changes** - everything is backward compatible! 