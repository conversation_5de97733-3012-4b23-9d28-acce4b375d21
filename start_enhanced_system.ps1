#!/usr/bin/env powershell

# Enhanced Campaign System Startup Script
Write-Host "🚀 Starting Enhanced Campaign System" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Cyan

# Check if required directories exist
if (-not (Test-Path "Orp_tts_call_telnyx\simple_backend.py")) {
    Write-Host "❌ Backend not found in Orp_tts_call_telnyx directory" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path "frontend\quickcall-frontend\package.json")) {
    Write-Host "❌ Frontend not found in frontend\quickcall-frontend directory" -ForegroundColor Red
    exit 1
}

Write-Host "🔧 Starting Backend Server..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'Orp_tts_call_telnyx'; python simple_backend.py"

Start-Sleep -Seconds 3

Write-Host "🎨 Starting Frontend Server..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'frontend\quickcall-frontend'; npm run dev"

Start-Sleep -Seconds 5

Write-Host ""
Write-Host "✅ Enhanced Campaign System Started!" -ForegroundColor Green
Write-Host ""
Write-Host "📊 Backend:  http://localhost:9090" -ForegroundColor Cyan
Write-Host "🎨 Frontend: http://localhost:3000" -ForegroundColor Cyan
Write-Host ""
Write-Host "🎯 Enhanced Features Available:" -ForegroundColor Magenta
Write-Host "  • Real-time campaign statistics" -ForegroundColor White
Write-Host "  • Smart action buttons (start/pause/restart/recycle)" -ForegroundColor White
Write-Host "  • Cost tracking and duration monitoring" -ForegroundColor White
Write-Host "  • Recyclable contacts management" -ForegroundColor White
Write-Host "  • Automatic call transfer" -ForegroundColor White
Write-Host "  • Professional dashboard with CALLBOT2.1 features" -ForegroundColor White
Write-Host ""
Write-Host "🔥 Ready to manage campaigns with enhanced analytics!" -ForegroundColor Green

Read-Host "Press Enter to continue..." 