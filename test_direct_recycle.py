#!/usr/bin/env python3
"""
Direct test of the recycle endpoint with the dh<PERSON><PERSON><PERSON>y campaign
"""

import requests
import json

def test_recycle_dhf<PERSON><PERSON><PERSON>():
    """Test recycling the dhfryeyery campaign"""
    
    campaign_id = "campaign_1749386900"  # dhf<PERSON><PERSON>y campaign
    base_url = "http://localhost:9090"
    
    print(f"🔄 Testing recycle for campaign: {campaign_id}")
    print("=" * 50)
    
    try:
        # First check current campaign status
        status_url = f"{base_url}/api/campaigns/{campaign_id}/status"
        print(f"📊 Checking campaign status...")
        
        status_response = requests.get(status_url, timeout=10)
        if status_response.status_code == 200:
            status_data = status_response.json()
            print("Current Campaign Status:")
            print(json.dumps(status_data.get('call_statistics', {}), indent=2))
        
        # Test the debug endpoint first
        debug_url = f"{base_url}/api/campaigns/{campaign_id}/recycle/debug"
        print(f"\n🔍 Calling debug endpoint...")
        
        debug_response = requests.get(debug_url, timeout=10)
        if debug_response.status_code == 200:
            debug_data = debug_response.json()
            print("Debug Analysis:")
            print(json.dumps(debug_data, indent=2))
        else:
            print(f"❌ Debug endpoint failed: {debug_response.status_code}")
            print(debug_response.text)
        
        # Now test the actual recycle endpoint
        recycle_url = f"{base_url}/api/campaigns/{campaign_id}/recycle"
        print(f"\n🔄 Calling recycle endpoint...")
        
        recycle_data = {
            "min_retry_interval_hours": 0,  # Allow immediate recycling
            "create_new_campaign": True
        }
        
        recycle_response = requests.post(recycle_url, json=recycle_data, timeout=10)
        
        if recycle_response.status_code == 200:
            recycle_result = recycle_response.json()
            print("✅ Recycle Response:")
            print(json.dumps(recycle_result, indent=2))
        else:
            print(f"❌ Recycle failed: {recycle_response.status_code}")
            print(recycle_response.text)
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_recycle_dhfryeyery() 