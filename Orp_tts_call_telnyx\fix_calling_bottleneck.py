#!/usr/bin/env python3
"""
FIX FOR CALLING BOTTLENECK IN SIMPLE_BACKEND.PY

This script shows the key fixes needed to handle more than 4-5 concurrent calls.

MAIN ISSUES IDENTIFIED:
1. HTTP self-requests creating circular dependencies
2. Unlimited threading overwhelming the system
3. Synchronous agent creation blocking calls
4. No rate limiting on LiveKit CLI commands

SOLUTIONS DEMONSTRATED:
"""

import concurrent.futures
import time
import subprocess
import json
import logging
import os

logger = logging.getLogger(__name__)

def direct_livekit_dispatch(agent_name, metadata_dict, max_retries=3):
    """
    Direct LiveKit dispatch without HTTP overhead
    This replaces the requests.post() call in make_single_call()
    """
    cmd = [
        "lk", "dispatch", "create",
        "--new-room",
        "--agent-name", agent_name,
        "--metadata", json.dumps(metadata_dict)
    ]
    
    for attempt in range(max_retries):
        try:
            logger.info(f"📞 Direct LiveKit dispatch attempt {attempt + 1}: {agent_name}")
            result = subprocess.run(
                cmd, 
                cwd=os.path.dirname(__file__), 
                capture_output=True, 
                text=True, 
                check=False, 
                timeout=15  # 15 second timeout
            )
            
            if result.returncode == 0:
                logger.info(f"✅ LiveKit dispatch successful: {agent_name}")
                return {"success": True, "output": result.stdout}
            else:
                logger.error(f"❌ LiveKit dispatch failed (attempt {attempt + 1}): {result.stderr}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
                
        except subprocess.TimeoutExpired:
            logger.error(f"⏰ LiveKit dispatch timeout (attempt {attempt + 1}): {agent_name}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)
        except Exception as e:
            logger.error(f"💥 LiveKit dispatch error (attempt {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)
    
    return {"success": False, "error": "All dispatch attempts failed"}

def optimized_make_single_call(contact, campaign_phone_numbers, contact_index, agent_data, campaign_id, sip_trunk_mapping, use_personalized_ai=False):
    """
    OPTIMIZED VERSION of make_single_call() that fixes the bottlenecks
    
    KEY CHANGES:
    1. Direct LiveKit dispatch (no HTTP self-request)
    2. Pre-created agents (no blocking agent creation)
    3. Proper error handling and timeouts
    """
    try:
        contact_name = contact.get('name', 'Customer')
        contact_phone = contact.get('phone_number')
        
        # Distribute calls across multiple campaign phone numbers
        selected_phone = campaign_phone_numbers[contact_index % len(campaign_phone_numbers)]
        selected_sip_trunk = sip_trunk_mapping.get(selected_phone, "DEFAULT_SIP_TRUNK_ID")
        
        logger.info(f"🔄 Starting optimized call for {contact_name} ({contact_phone}) from {selected_phone}")
        
        # For personalized AI, use pre-created agent name pattern
        if use_personalized_ai:
            agent_name_for_call = f"{agent_data['name']}_{contact_name.replace(' ', '_').replace('.', '')}"
        else:
            # For shared AI, use round-robin agent selection
            shared_agent_index = contact_index % 5  # Assuming 5 shared agents
            agent_name_for_call = f"shared_{agent_data['name']}_campaign_{campaign_id}_{shared_agent_index + 1}"
        
        # Build metadata for LiveKit
        metadata = {
            "target_phone": contact_phone,
            "from_phone": selected_phone,
            "sip_trunk_id": selected_sip_trunk,
            "campaign_id": campaign_id,
            "call_id": f"call_{campaign_id}_{contact_index}",
            "contact_name": contact_name,
            "ai_mode": "personalized" if use_personalized_ai else "shared"
        }
        
        # DIRECT LIVEKIT DISPATCH (no HTTP request)
        result = direct_livekit_dispatch(agent_name_for_call, metadata)
        
        if result["success"]:
            logger.info(f"✅ OPTIMIZED CALL INITIATED: {contact_name} ({contact_phone})")
            return {"success": True, "contact_name": contact_name, "contact_phone": contact_phone}
        else:
            logger.error(f"❌ OPTIMIZED CALL FAILED: {contact_name} - {result.get('error')}")
            return {"success": False, "contact_name": contact_name, "error": result.get('error')}
            
    except Exception as e:
        logger.error(f"💥 CRITICAL ERROR in optimized call for {contact.get('name', 'Unknown')}: {str(e)}")
        return {"success": False, "contact_name": contact.get('name', 'Unknown'), "error": str(e)}

def optimized_campaign_calling(campaign, campaign_id, agent_data, sip_trunk_mapping):
    """
    OPTIMIZED CAMPAIGN CALLING with proper thread management
    
    KEY IMPROVEMENTS:
    1. ThreadPoolExecutor limits concurrent calls
    2. Staggered call initiation
    3. Proper error handling and timeouts
    4. Rate limiting to avoid overwhelming LiveKit
    """
    contacts = campaign.get('contacts', [])
    campaign_phone_numbers = campaign.get('telnyx_numbers', [])
    use_personalized_ai = campaign.get('use_personalized_ai', False)
    
    if not campaign_phone_numbers:
        logger.error(f"❌ No phone numbers available for campaign {campaign_id}")
        return {"success": False, "error": "No phone numbers configured"}
    
    if not contacts:
        logger.error(f"❌ No contacts available for campaign {campaign_id}")
        return {"success": False, "error": "No contacts configured"}
    
    # CONTROLLED CONCURRENCY - limit to 5 simultaneous calls
    max_concurrent_calls = 5
    stagger_delay = 0.8  # Delay between call batches
    
    logger.info(f"🚀 OPTIMIZED: Starting {len(contacts)} calls with max {max_concurrent_calls} concurrent")
    logger.info(f"📞 Using {len(campaign_phone_numbers)} phone numbers: {', '.join(campaign_phone_numbers)}")
    
    successful_calls = 0
    failed_calls = 0
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_concurrent_calls) as executor:
        # Submit calls in batches to avoid overwhelming the system
        batch_size = max_concurrent_calls
        
        for batch_start in range(0, len(contacts), batch_size):
            batch_end = min(batch_start + batch_size, len(contacts))
            batch_contacts = contacts[batch_start:batch_end]
            
            logger.info(f"📞 Processing batch {batch_start//batch_size + 1}: contacts {batch_start + 1}-{batch_end}")
            
            # Submit batch to thread pool
            futures = []
            for i, contact in enumerate(batch_contacts):
                contact_index = batch_start + i
                future = executor.submit(
                    optimized_make_single_call,
                    contact,
                    campaign_phone_numbers,
                    contact_index,
                    agent_data,
                    campaign_id,
                    sip_trunk_mapping,
                    use_personalized_ai
                )
                futures.append((future, contact_index, contact.get('name', 'Unknown')))
            
            # Wait for batch to complete with timeout
            for future, contact_index, contact_name in futures:
                try:
                    result = future.result(timeout=30)  # 30 second timeout per call
                    if result.get("success"):
                        successful_calls += 1
                        logger.info(f"✅ Batch call {successful_calls} completed: {contact_name}")
                    else:
                        failed_calls += 1
                        logger.error(f"❌ Batch call failed: {contact_name} - {result.get('error')}")
                except concurrent.futures.TimeoutError:
                    failed_calls += 1
                    logger.error(f"⏰ Batch call timeout: {contact_name}")
                except Exception as e:
                    failed_calls += 1
                    logger.error(f"💥 Batch call exception: {contact_name} - {str(e)}")
            
            # Stagger between batches to avoid overwhelming LiveKit
            if batch_end < len(contacts):
                logger.info(f"⏳ Staggering {stagger_delay}s before next batch...")
                time.sleep(stagger_delay)
    
    logger.info(f"✅ OPTIMIZED CAMPAIGN COMPLETED: {successful_calls} successful, {failed_calls} failed")
    
    return {
        "success": True,
        "total_calls": len(contacts),
        "successful_calls": successful_calls,
        "failed_calls": failed_calls
    }

def pre_create_personalized_agents(contacts, agent_data, campaign_id):
    """
    PRE-CREATE AGENTS for personalized AI to avoid blocking during calls
    
    This should be called BEFORE starting the campaign to create all agents upfront
    """
    logger.info(f"🤖 Pre-creating {len(contacts)} personalized agents...")
    
    successful_agents = 0
    failed_agents = 0
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
        # Submit agent creation tasks
        futures = []
        for contact in contacts:
            contact_name = contact.get('name', 'Customer')
            agent_name = f"{agent_data['name']}_{contact_name.replace(' ', '_').replace('.', '')}"
            
            # This would call start_dynamic_agent_internal in the real implementation
            future = executor.submit(mock_create_agent, agent_name, agent_data)
            futures.append((future, contact_name, agent_name))
        
        # Wait for all agents to be created
        for future, contact_name, agent_name in futures:
            try:
                result = future.result(timeout=45)  # 45 second timeout per agent
                if result.get("success"):
                    successful_agents += 1
                    logger.info(f"✅ Agent created: {agent_name}")
                else:
                    failed_agents += 1
                    logger.error(f"❌ Agent creation failed: {agent_name}")
            except Exception as e:
                failed_agents += 1
                logger.error(f"💥 Agent creation error: {agent_name} - {str(e)}")
    
    logger.info(f"🤖 Agent pre-creation complete: {successful_agents} successful, {failed_agents} failed")
    return successful_agents > 0

def mock_create_agent(agent_name, agent_data):
    """Mock agent creation for demonstration"""
    time.sleep(1)  # Simulate agent creation time
    return {"success": True, "agent_id": f"agent_{hash(agent_name) % 10000}"}

if __name__ == "__main__":
    # DEMONSTRATION: How to fix your calling bottleneck
    
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    print("🔧 CALLING BOTTLENECK FIX DEMONSTRATION")
    print("=" * 50)
    
    # Mock data for demonstration
    mock_campaign = {
        "telnyx_numbers": ["+1234567890", "+1234567891"],
        "contacts": [
            {"name": "John Doe", "phone_number": "+1111111111"},
            {"name": "Jane Smith", "phone_number": "+2222222222"},
            {"name": "Bob Johnson", "phone_number": "+3333333333"},
            {"name": "Alice Brown", "phone_number": "+4444444444"},
            {"name": "Charlie Wilson", "phone_number": "+5555555555"},
            {"name": "Diana Davis", "phone_number": "+6666666666"},
            {"name": "Eve Miller", "phone_number": "+7777777777"},
        ],
        "use_personalized_ai": False  # Set to True to test personalized mode
    }
    
    mock_agent_data = {
        "name": "TestAgent",
        "system_prompt": "You are a helpful AI assistant."
    }
    
    mock_sip_mapping = {
        "+1234567890": "ST_trunk1",
        "+1234567891": "ST_trunk2"
    }
    
    # Run the optimized campaign calling
    result = optimized_campaign_calling(
        campaign=mock_campaign,
        campaign_id="test_campaign_123",
        agent_data=mock_agent_data,
        sip_trunk_mapping=mock_sip_mapping
    )
    
    print("\n🎯 OPTIMIZATION RESULTS:")
    print(f"Total calls: {result.get('total_calls', 0)}")
    print(f"Successful: {result.get('successful_calls', 0)}")
    print(f"Failed: {result.get('failed_calls', 0)}")
    
    print("\n📋 TO IMPLEMENT IN YOUR SIMPLE_BACKEND.PY:")
    print("1. Add concurrent.futures import")
    print("2. Replace the HTTP self-request with direct_livekit_dispatch()")
    print("3. Replace unlimited threading with ThreadPoolExecutor")
    print("4. Add staggered call initiation with time.sleep()")
    print("5. Add proper timeouts and error handling")
    print("6. For personalized AI: pre-create agents before campaign starts") 
"""
FIX FOR CALLING BOTTLENECK IN SIMPLE_BACKEND.PY

This script shows the key fixes needed to handle more than 4-5 concurrent calls.

MAIN ISSUES IDENTIFIED:
1. HTTP self-requests creating circular dependencies
2. Unlimited threading overwhelming the system
3. Synchronous agent creation blocking calls
4. No rate limiting on LiveKit CLI commands

SOLUTIONS DEMONSTRATED:
"""

import concurrent.futures
import time
import subprocess
import json
import logging
import os

logger = logging.getLogger(__name__)

def direct_livekit_dispatch(agent_name, metadata_dict, max_retries=3):
    """
    Direct LiveKit dispatch without HTTP overhead
    This replaces the requests.post() call in make_single_call()
    """
    cmd = [
        "lk", "dispatch", "create",
        "--new-room",
        "--agent-name", agent_name,
        "--metadata", json.dumps(metadata_dict)
    ]
    
    for attempt in range(max_retries):
        try:
            logger.info(f"📞 Direct LiveKit dispatch attempt {attempt + 1}: {agent_name}")
            result = subprocess.run(
                cmd, 
                cwd=os.path.dirname(__file__), 
                capture_output=True, 
                text=True, 
                check=False, 
                timeout=15  # 15 second timeout
            )
            
            if result.returncode == 0:
                logger.info(f"✅ LiveKit dispatch successful: {agent_name}")
                return {"success": True, "output": result.stdout}
            else:
                logger.error(f"❌ LiveKit dispatch failed (attempt {attempt + 1}): {result.stderr}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
                
        except subprocess.TimeoutExpired:
            logger.error(f"⏰ LiveKit dispatch timeout (attempt {attempt + 1}): {agent_name}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)
        except Exception as e:
            logger.error(f"💥 LiveKit dispatch error (attempt {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)
    
    return {"success": False, "error": "All dispatch attempts failed"}

def optimized_make_single_call(contact, campaign_phone_numbers, contact_index, agent_data, campaign_id, sip_trunk_mapping, use_personalized_ai=False):
    """
    OPTIMIZED VERSION of make_single_call() that fixes the bottlenecks
    
    KEY CHANGES:
    1. Direct LiveKit dispatch (no HTTP self-request)
    2. Pre-created agents (no blocking agent creation)
    3. Proper error handling and timeouts
    """
    try:
        contact_name = contact.get('name', 'Customer')
        contact_phone = contact.get('phone_number')
        
        # Distribute calls across multiple campaign phone numbers
        selected_phone = campaign_phone_numbers[contact_index % len(campaign_phone_numbers)]
        selected_sip_trunk = sip_trunk_mapping.get(selected_phone, "DEFAULT_SIP_TRUNK_ID")
        
        logger.info(f"🔄 Starting optimized call for {contact_name} ({contact_phone}) from {selected_phone}")
        
        # For personalized AI, use pre-created agent name pattern
        if use_personalized_ai:
            agent_name_for_call = f"{agent_data['name']}_{contact_name.replace(' ', '_').replace('.', '')}"
        else:
            # For shared AI, use round-robin agent selection
            shared_agent_index = contact_index % 5  # Assuming 5 shared agents
            agent_name_for_call = f"shared_{agent_data['name']}_campaign_{campaign_id}_{shared_agent_index + 1}"
        
        # Build metadata for LiveKit
        metadata = {
            "target_phone": contact_phone,
            "from_phone": selected_phone,
            "sip_trunk_id": selected_sip_trunk,
            "campaign_id": campaign_id,
            "call_id": f"call_{campaign_id}_{contact_index}",
            "contact_name": contact_name,
            "ai_mode": "personalized" if use_personalized_ai else "shared"
        }
        
        # DIRECT LIVEKIT DISPATCH (no HTTP request)
        result = direct_livekit_dispatch(agent_name_for_call, metadata)
        
        if result["success"]:
            logger.info(f"✅ OPTIMIZED CALL INITIATED: {contact_name} ({contact_phone})")
            return {"success": True, "contact_name": contact_name, "contact_phone": contact_phone}
        else:
            logger.error(f"❌ OPTIMIZED CALL FAILED: {contact_name} - {result.get('error')}")
            return {"success": False, "contact_name": contact_name, "error": result.get('error')}
            
    except Exception as e:
        logger.error(f"💥 CRITICAL ERROR in optimized call for {contact.get('name', 'Unknown')}: {str(e)}")
        return {"success": False, "contact_name": contact.get('name', 'Unknown'), "error": str(e)}

def optimized_campaign_calling(campaign, campaign_id, agent_data, sip_trunk_mapping):
    """
    OPTIMIZED CAMPAIGN CALLING with proper thread management
    
    KEY IMPROVEMENTS:
    1. ThreadPoolExecutor limits concurrent calls
    2. Staggered call initiation
    3. Proper error handling and timeouts
    4. Rate limiting to avoid overwhelming LiveKit
    """
    contacts = campaign.get('contacts', [])
    campaign_phone_numbers = campaign.get('telnyx_numbers', [])
    use_personalized_ai = campaign.get('use_personalized_ai', False)
    
    if not campaign_phone_numbers:
        logger.error(f"❌ No phone numbers available for campaign {campaign_id}")
        return {"success": False, "error": "No phone numbers configured"}
    
    if not contacts:
        logger.error(f"❌ No contacts available for campaign {campaign_id}")
        return {"success": False, "error": "No contacts configured"}
    
    # CONTROLLED CONCURRENCY - limit to 5 simultaneous calls
    max_concurrent_calls = 5
    stagger_delay = 0.8  # Delay between call batches
    
    logger.info(f"🚀 OPTIMIZED: Starting {len(contacts)} calls with max {max_concurrent_calls} concurrent")
    logger.info(f"📞 Using {len(campaign_phone_numbers)} phone numbers: {', '.join(campaign_phone_numbers)}")
    
    successful_calls = 0
    failed_calls = 0
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_concurrent_calls) as executor:
        # Submit calls in batches to avoid overwhelming the system
        batch_size = max_concurrent_calls
        
        for batch_start in range(0, len(contacts), batch_size):
            batch_end = min(batch_start + batch_size, len(contacts))
            batch_contacts = contacts[batch_start:batch_end]
            
            logger.info(f"📞 Processing batch {batch_start//batch_size + 1}: contacts {batch_start + 1}-{batch_end}")
            
            # Submit batch to thread pool
            futures = []
            for i, contact in enumerate(batch_contacts):
                contact_index = batch_start + i
                future = executor.submit(
                    optimized_make_single_call,
                    contact,
                    campaign_phone_numbers,
                    contact_index,
                    agent_data,
                    campaign_id,
                    sip_trunk_mapping,
                    use_personalized_ai
                )
                futures.append((future, contact_index, contact.get('name', 'Unknown')))
            
            # Wait for batch to complete with timeout
            for future, contact_index, contact_name in futures:
                try:
                    result = future.result(timeout=30)  # 30 second timeout per call
                    if result.get("success"):
                        successful_calls += 1
                        logger.info(f"✅ Batch call {successful_calls} completed: {contact_name}")
                    else:
                        failed_calls += 1
                        logger.error(f"❌ Batch call failed: {contact_name} - {result.get('error')}")
                except concurrent.futures.TimeoutError:
                    failed_calls += 1
                    logger.error(f"⏰ Batch call timeout: {contact_name}")
                except Exception as e:
                    failed_calls += 1
                    logger.error(f"💥 Batch call exception: {contact_name} - {str(e)}")
            
            # Stagger between batches to avoid overwhelming LiveKit
            if batch_end < len(contacts):
                logger.info(f"⏳ Staggering {stagger_delay}s before next batch...")
                time.sleep(stagger_delay)
    
    logger.info(f"✅ OPTIMIZED CAMPAIGN COMPLETED: {successful_calls} successful, {failed_calls} failed")
    
    return {
        "success": True,
        "total_calls": len(contacts),
        "successful_calls": successful_calls,
        "failed_calls": failed_calls
    }

def pre_create_personalized_agents(contacts, agent_data, campaign_id):
    """
    PRE-CREATE AGENTS for personalized AI to avoid blocking during calls
    
    This should be called BEFORE starting the campaign to create all agents upfront
    """
    logger.info(f"🤖 Pre-creating {len(contacts)} personalized agents...")
    
    successful_agents = 0
    failed_agents = 0
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
        # Submit agent creation tasks
        futures = []
        for contact in contacts:
            contact_name = contact.get('name', 'Customer')
            agent_name = f"{agent_data['name']}_{contact_name.replace(' ', '_').replace('.', '')}"
            
            # This would call start_dynamic_agent_internal in the real implementation
            future = executor.submit(mock_create_agent, agent_name, agent_data)
            futures.append((future, contact_name, agent_name))
        
        # Wait for all agents to be created
        for future, contact_name, agent_name in futures:
            try:
                result = future.result(timeout=45)  # 45 second timeout per agent
                if result.get("success"):
                    successful_agents += 1
                    logger.info(f"✅ Agent created: {agent_name}")
                else:
                    failed_agents += 1
                    logger.error(f"❌ Agent creation failed: {agent_name}")
            except Exception as e:
                failed_agents += 1
                logger.error(f"💥 Agent creation error: {agent_name} - {str(e)}")
    
    logger.info(f"🤖 Agent pre-creation complete: {successful_agents} successful, {failed_agents} failed")
    return successful_agents > 0

def mock_create_agent(agent_name, agent_data):
    """Mock agent creation for demonstration"""
    time.sleep(1)  # Simulate agent creation time
    return {"success": True, "agent_id": f"agent_{hash(agent_name) % 10000}"}

if __name__ == "__main__":
    # DEMONSTRATION: How to fix your calling bottleneck
    
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    print("🔧 CALLING BOTTLENECK FIX DEMONSTRATION")
    print("=" * 50)
    
    # Mock data for demonstration
    mock_campaign = {
        "telnyx_numbers": ["+1234567890", "+1234567891"],
        "contacts": [
            {"name": "John Doe", "phone_number": "+1111111111"},
            {"name": "Jane Smith", "phone_number": "+2222222222"},
            {"name": "Bob Johnson", "phone_number": "+3333333333"},
            {"name": "Alice Brown", "phone_number": "+4444444444"},
            {"name": "Charlie Wilson", "phone_number": "+5555555555"},
            {"name": "Diana Davis", "phone_number": "+6666666666"},
            {"name": "Eve Miller", "phone_number": "+7777777777"},
        ],
        "use_personalized_ai": False  # Set to True to test personalized mode
    }
    
    mock_agent_data = {
        "name": "TestAgent",
        "system_prompt": "You are a helpful AI assistant."
    }
    
    mock_sip_mapping = {
        "+1234567890": "ST_trunk1",
        "+1234567891": "ST_trunk2"
    }
    
    # Run the optimized campaign calling
    result = optimized_campaign_calling(
        campaign=mock_campaign,
        campaign_id="test_campaign_123",
        agent_data=mock_agent_data,
        sip_trunk_mapping=mock_sip_mapping
    )
    
    print("\n🎯 OPTIMIZATION RESULTS:")
    print(f"Total calls: {result.get('total_calls', 0)}")
    print(f"Successful: {result.get('successful_calls', 0)}")
    print(f"Failed: {result.get('failed_calls', 0)}")
    
    print("\n📋 TO IMPLEMENT IN YOUR SIMPLE_BACKEND.PY:")
    print("1. Add concurrent.futures import")
    print("2. Replace the HTTP self-request with direct_livekit_dispatch()")
    print("3. Replace unlimited threading with ThreadPoolExecutor")
    print("4. Add staggered call initiation with time.sleep()")
    print("5. Add proper timeouts and error handling")
    print("6. For personalized AI: pre-create agents before campaign starts") 