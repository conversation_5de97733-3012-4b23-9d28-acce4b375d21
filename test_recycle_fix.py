#!/usr/bin/env python3
"""
Test script to verify the recycle function fix
"""

import requests
import json
import time
from datetime import datetime

# Configuration
API_URL = "http://localhost:5000"

def test_recycle_function():
    """Test the recycle function with a real campaign"""
    print("🧪 Testing Recycle Function Fix")
    print("=" * 50)
    
    try:
        # 1. Get all campaigns
        print("📋 Fetching campaigns...")
        campaigns_response = requests.get(f"{API_URL}/api/campaigns")
        if campaigns_response.status_code != 200:
            print(f"❌ Failed to fetch campaigns: {campaigns_response.status_code}")
            return
        
        campaigns = campaigns_response.json()
        if not campaigns:
            print("❌ No campaigns found")
            return

        print(f"📊 Found {len(campaigns)} campaigns")

        # Handle different response formats
        if isinstance(campaigns, list):
            # If it's a list, convert to dict format
            campaigns_dict = {str(i): camp for i, camp in enumerate(campaigns)}
        elif isinstance(campaigns, dict):
            campaigns_dict = campaigns
        else:
            print(f"❌ Unexpected campaigns format: {type(campaigns)}")
            return

        # Find a campaign with completed status
        completed_campaigns = []
        for cid, campaign in campaigns_dict.items():
            if isinstance(campaign, dict) and campaign.get('status') == 'completed':
                completed_campaigns.append(cid)
            elif isinstance(campaign, str):
                print(f"⚠️ Campaign {cid} is a string, not a dict: {campaign}")

        # If no completed campaigns, try any campaign with calls
        if not completed_campaigns:
            print("⚠️ No completed campaigns found, looking for any campaign with calls...")
            for cid, campaign in campaigns_dict.items():
                if isinstance(campaign, dict):
                    completed_campaigns.append(cid)
                    break
        
        if not completed_campaigns:
            print("❌ No completed campaigns found to test recycling")
            print("Available campaigns:")
            for cid, campaign in campaigns.items():
                print(f"  - {cid}: {campaign.get('name', 'Unnamed')} (status: {campaign.get('status', 'unknown')})")
            return
        
        # Use the first available campaign
        test_campaign_id = completed_campaigns[0]
        campaign = campaigns_dict[test_campaign_id]
        print(f"✅ Found test campaign: {campaign.get('name', 'Unnamed')} (ID: {test_campaign_id})")
        
        # 2. Check call logs for this campaign
        print(f"\n📞 Checking call logs for campaign {test_campaign_id}...")
        call_logs_response = requests.get(f"{API_URL}/api/call-logs")
        if call_logs_response.status_code == 200:
            all_call_logs = call_logs_response.json()
            campaign_calls = [log for log in all_call_logs if log.get('campaign_id') == test_campaign_id]
            print(f"   Found {len(campaign_calls)} call logs for this campaign")

            # Show call statuses
            statuses = {}
            recyclable_statuses = ['failed', 'no_answer', 'no-answer', 'busy']

            for call in campaign_calls:
                status = call.get('status', 'unknown')
                statuses[status] = statuses.get(status, 0) + 1

            print("   Call status breakdown:")
            for status, count in statuses.items():
                recyclable = "♻️ RECYCLABLE" if status.lower() in recyclable_statuses else "✅ NOT RECYCLABLE"
                print(f"     - {status}: {count} ({recyclable})")

            # Show individual calls for debugging
            print("   Individual call details:")
            for i, call in enumerate(campaign_calls[:5]):  # Show first 5
                status = call.get('status', 'unknown')
                name = call.get('contact_name', 'Unknown')
                phone = call.get('contact_phone', 'No phone')
                timestamp = call.get('timestamp', 'No timestamp')
                recyclable = "♻️" if status.lower() in recyclable_statuses else "✅"
                print(f"     {i+1}. {name} ({phone}) - {status} {recyclable} - {timestamp}")

            if len(campaign_calls) > 5:
                print(f"     ... and {len(campaign_calls) - 5} more calls")
        else:
            print(f"   ⚠️ Could not fetch call logs: {call_logs_response.status_code}")
        
        # 3. Test recycle function (in-place)
        print(f"\n🔄 Testing recycle function (in-place)...")
        recycle_data = {
            "min_retry_interval_hours": 0.1,  # 6 minutes for testing
            "create_new_campaign": False
        }
        
        recycle_response = requests.post(
            f"{API_URL}/api/campaigns/{test_campaign_id}/recycle",
            headers={'Content-Type': 'application/json'},
            json=recycle_data
        )
        
        print(f"   Response status: {recycle_response.status_code}")
        
        if recycle_response.status_code == 200:
            result = recycle_response.json()
            print("   ✅ Recycle function executed successfully!")
            print(f"   📊 Result:")
            print(f"      - Success: {result.get('success', False)}")
            print(f"      - Message: {result.get('message', 'No message')}")
            print(f"      - Total unresponsive: {result.get('total_unresponsive', 0)}")
            print(f"      - Eligible for retry: {result.get('eligible_for_retry', 0)}")
            
            if result.get('contacts'):
                print(f"      - Contacts to recycle:")
                for contact in result['contacts'][:3]:  # Show first 3
                    print(f"        * {contact.get('contact_name', 'Unknown')} ({contact.get('phone_number', 'No phone')}) - {contact.get('reason', 'No reason')}")
                if len(result['contacts']) > 3:
                    print(f"        ... and {len(result['contacts']) - 3} more")
        else:
            error_data = recycle_response.json() if recycle_response.headers.get('content-type', '').startswith('application/json') else {}
            print(f"   ❌ Recycle function failed!")
            print(f"      Error: {error_data.get('error', 'Unknown error')}")
        
        # 4. Test recycle function (create new campaign)
        print(f"\n🆕 Testing recycle function (create new campaign)...")
        recycle_data_new = {
            "min_retry_interval_hours": 0.1,  # 6 minutes for testing
            "create_new_campaign": True
        }
        
        recycle_response_new = requests.post(
            f"{API_URL}/api/campaigns/{test_campaign_id}/recycle",
            headers={'Content-Type': 'application/json'},
            json=recycle_data_new
        )
        
        print(f"   Response status: {recycle_response_new.status_code}")
        
        if recycle_response_new.status_code == 200:
            result_new = recycle_response_new.json()
            print("   ✅ New recycled campaign created successfully!")
            print(f"   📊 Result:")
            print(f"      - Success: {result_new.get('success', False)}")
            print(f"      - Message: {result_new.get('message', 'No message')}")
            print(f"      - Original campaign ID: {result_new.get('original_campaign_id', 'Unknown')}")
            print(f"      - New campaign ID: {result_new.get('recycled_campaign_id', 'Unknown')}")
            print(f"      - Eligible for retry: {result_new.get('eligible_for_retry', 0)}")
        else:
            error_data_new = recycle_response_new.json() if recycle_response_new.headers.get('content-type', '').startswith('application/json') else {}
            print(f"   ❌ New recycled campaign creation failed!")
            print(f"      Error: {error_data_new.get('error', 'Unknown error')}")
        
        print(f"\n🎉 Recycle function test completed!")
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_recycle_function()
