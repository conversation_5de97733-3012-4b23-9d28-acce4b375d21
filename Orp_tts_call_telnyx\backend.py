from flask import Flask, request, jsonify
from flask_cors import CORS
import subprocess
import threading
import time
import json
import uuid
import logging
import os
import signal
from datetime import datetime, timezone
from dataclasses import dataclass, asdict
from typing import Dict, Optional, List
import psutil
from collections import deque
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

@dataclass
class ProcessData:
    def __init__(self, process_id: str, call_data: dict):
        self.process_id = process_id
        self.call_data = call_data
        self.start_time = datetime.now(timezone.utc)
        self.end_time = None
        self.status = 'running'  # running, completed, failed, stopped
        self.agent_process = None  # Agent subprocess object
        self.dispatch_process = None  # Dispatch subprocess object
        self.logs = []  # List of log messages
        self.error_message = None
        self.exit_code = None

class ProcessManager:
    def __init__(self):
        self.processes: Dict[str, ProcessData] = {}
        self.max_concurrent_processes = 10
        
    def create_process(self, call_data: dict) -> str:
        """Create a new process"""
        process_id = str(uuid.uuid4())
        
        process_data = ProcessData(process_id, call_data)
        
        self.processes[process_id] = process_data
        return process_id
    
    def get_active_process_count(self) -> int:
        """Get number of active processes"""
        return len([p for p in self.processes.values() if p.status == 'running'])
    
    def stop_process(self, process_id: str) -> bool:
        """Stop a specific process"""
        if process_id not in self.processes:
            return False
            
        process_data = self.processes[process_id]
        
        if process_data.status != 'running':
            return False
        
        process_data.status = 'stopping'
        process_data.logs.append(f"[{datetime.now().isoformat()}] Process stop requested")
        
        # Terminate processes
        for proc in [process_data.agent_process, process_data.dispatch_process]:
            if proc and proc.poll() is None:
                try:
                    # Kill process tree
                    parent = psutil.Process(proc.pid)
                    for child in parent.children(recursive=True):
                        child.kill()
                    parent.kill()
                    process_data.logs.append(f"[{datetime.now().isoformat()}] Process terminated")
                except (psutil.NoSuchProcess, ProcessLookupError):
                    pass
        
        process_data.status = 'completed'
        process_data.end_time = datetime.now(timezone.utc)
        return True
    
    def cleanup_old_processes(self):
        """Remove old completed/failed processes"""
        current_time = datetime.now(timezone.utc)
        to_remove = []
        
        for process_id, process_data in self.processes.items():
            if process_data.status in ['completed', 'failed'] and process_data.end_time:
                time_diff = (current_time - process_data.end_time).total_seconds()
                if time_diff > 3600:  # 1 hour
                    to_remove.append(process_id)
        
        for process_id in to_remove:
            del self.processes[process_id]
            logger.info(f"Cleaned up process {process_id}")

# Global process manager
process_manager = ProcessManager()

# Data management
DATA_DIR = Path("data")
DATA_DIR.mkdir(exist_ok=True)

CAMPAIGNS_FILE = DATA_DIR / "campaigns.json"
CALL_LOGS_FILE = DATA_DIR / "call_logs.json"

def load_json_data(file_path: Path, default=None):
    """Load JSON data from file"""
    if default is None:
        default = {}
    try:
        if file_path.exists():
            with open(file_path, 'r') as f:
                return json.load(f)
        return default
    except Exception as e:
        logger.error(f"Error loading {file_path}: {e}")
        return default

def save_json_data(file_path: Path, data):
    """Save JSON data to file"""
    try:
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2, default=str)
    except Exception as e:
        logger.error(f"Error saving {file_path}: {e}")

# Load existing data
campaigns_storage = load_json_data(CAMPAIGNS_FILE, {})
call_logs = load_json_data(CALL_LOGS_FILE, [])

def log_call(call_data: dict) -> str:
    """Log a call attempt"""
    call_id = str(uuid.uuid4())
    call_log = {
        'call_id': call_id,
        'campaign_id': call_data.get('campaign_id'),
        'contact_name': call_data.get('contact_name', 'Unknown'),
        'contact_phone': call_data.get('phone_number'),
        'from_phone': call_data.get('from_phone'),
        'status': 'initiated',
        'timestamp': datetime.now(timezone.utc).isoformat(),
        'initiated_at': datetime.now(timezone.utc).isoformat(),
        'notes': call_data.get('notes', ''),
        'agent_id': call_data.get('agent_id'),
        'voice_id': call_data.get('voice_id'),
        'model_id': call_data.get('model_id')
    }

    call_logs.append(call_log)
    save_json_data(CALL_LOGS_FILE, call_logs)
    logger.info(f"Logged call {call_id} for {call_data.get('contact_name')}")
    return call_id

def update_call_status(call_id: str, status: str, **kwargs):
    """Update call status and additional data"""
    for call_log in call_logs:
        if call_log.get('call_id') == call_id:
            call_log['status'] = status
            call_log['updated_at'] = datetime.now(timezone.utc).isoformat()

            # Update additional fields
            for key, value in kwargs.items():
                call_log[key] = value

            # Mark as completed if final status
            if status in ['completed', 'no_answer', 'failed', 'busy']:
                call_log['completed_at'] = datetime.now(timezone.utc).isoformat()
                call_log['needs_recycling'] = status in ['no_answer', 'failed', 'busy']

            save_json_data(CALL_LOGS_FILE, call_logs)
            logger.info(f"Updated call {call_id} status to {status}")
            return True

    logger.warning(f"Call {call_id} not found for status update")
    return False

def get_unresponsive_contacts(campaign_id: str) -> List[Dict]:
    """Get contacts who didn't answer or failed for recycling"""
    unresponsive = []

    # Group calls by phone number to get latest status
    phone_calls = {}
    for call in call_logs:
        if call.get('campaign_id') == campaign_id:
            phone = call.get('contact_phone')
            if phone:
                if phone not in phone_calls or call.get('timestamp', '') > phone_calls[phone].get('timestamp', ''):
                    phone_calls[phone] = call

    # Find unresponsive contacts
    for phone, latest_call in phone_calls.items():
        if latest_call.get('status') in ['no_answer', 'failed', 'busy']:
            unresponsive.append({
                'phone_number': phone,
                'contact_name': latest_call.get('contact_name', 'Unknown'),
                'notes': latest_call.get('notes', ''),
                'last_attempt': latest_call.get('timestamp'),
                'reason': latest_call.get('status')
            })

    return unresponsive

# Predefined agents, voices, and models
AGENTS = [
    {
        "id": "customer-service",
        "name": "Customer Service Agent",
        "prompt": "You are a helpful and professional customer service representative. Keep your responses brief and friendly. Use appropriate emotional tags like <smile> or <chuckle> to sound natural."
    },
    {
        "id": "sales",
        "name": "Sales Representative",
        "prompt": "You are an engaging sales representative. Be enthusiastic but not pushy. Listen to customer needs and provide helpful solutions. Keep responses concise and use tags like <giggle> or <laugh> naturally."
    },
    {
        "id": "support",
        "name": "Technical Support",
        "prompt": "You are a knowledgeable technical support agent. Be patient and thorough in explaining solutions. Use <sigh> when appropriate to show understanding of customer frustration."
    },
    {
        "id": "custom",
        "name": "Custom Agent",
        "prompt": "You are a conversational AI assistant designed to be engaging and human-like. Use emotional tags like <giggle>, <laugh>, <sigh> to express natural reactions. Keep responses brief and natural."
    }
]

VOICES = [
    {"id": "tara", "name": "Tara", "description": "A confident female voice with a natural cadence"},
    {"id": "sarah", "name": "Sarah", "description": "Warm and professional female voice"},
    {"id": "mike", "name": "Mike", "description": "Clear and authoritative male voice"},
    {"id": "alex", "name": "Alex", "description": "Friendly and approachable unisex voice"}
]

MODELS = [
    {
        "id": "llama-3.3-70b-versatile",
        "name": "Llama 3.3 70B",
        "description": "High-performance large language model",
        "provider": "Groq"
    },
    {
        "id": "gpt-4o-mini",
        "name": "GPT-4o Mini",
        "description": "Fast and efficient OpenAI model",
        "provider": "OpenAI"
    },
    {
        "id": "gpt-4",
        "name": "GPT-4",
        "description": "Advanced OpenAI language model",
        "provider": "OpenAI"
    }
]

def build_agent_command(call_data: dict) -> List[str]:
    """Build the command to start the agent process"""
    cmd = ["python", "parameterized_orp.py", "dev"]
    if call_data.get("agentPrompt"):
        cmd.extend(["--system-prompt", call_data["agentPrompt"]])
    cmd.extend(["--tts-provider", "orpheus"])
    if call_data.get("voiceId"):
        cmd.extend(["--tts-voice", call_data["voiceId"]])
    if call_data.get("modelId"):
        cmd.extend(["--llm-model", call_data["modelId"]])
    cmd.extend(["--llm-temperature", "0.8"])
    # Always use outbound-caller as the agent name for LiveKit integration
    cmd.extend(["--agent-name", "outbound-caller"])
    cmd.extend(["--greeting", "Hello, "])
    return cmd

def build_outbound_call_command(call_data: dict) -> List[str]:
    """Build the exact command to initiate an outbound call"""
    # Remove any non-numeric characters except + for international format
    clean_number = call_data["phoneNumber"].strip()
    
    # Return the exact command as specified
    return ["lk", "dispatch", "create", "--new-room", "--agent-name", "outbound-caller", "--metadata", clean_number]

def start_outbound_call_process(process_id: str, call_data: dict):
    """Start a single outbound call process"""
    def run_call():
        process_data = process_manager.processes.get(process_id)
        if not process_data:
            return
        
        try:
            # Load environment variables from .env and .env.local files
            env = os.environ.copy()
            
            # Add any missing environment variables from .env file
            if os.path.exists('.env'):
                with open('.env', 'r') as f:
                    for line in f:
                        if line.strip() and not line.startswith('#'):
                            try:
                                key, value = line.strip().split('=', 1)
                                if key not in env:
                                    env[key] = value.strip('"').strip("'")
                            except ValueError:
                                continue
            
            # Add any missing environment variables from .env.local file (takes precedence)
            if os.path.exists('.env.local'):
                with open('.env.local', 'r') as f:
                    for line in f:
                        if line.strip() and not line.startswith('#'):
                            try:
                                key, value = line.strip().split('=', 1)
                                env[key] = value.strip('"').strip("'")
                            except ValueError:
                                continue  # Skip lines that don't have key=value format
            
            # Validate critical environment variables
            required_env_vars = [
                'LIVEKIT_URL', 'LIVEKIT_API_KEY', 'LIVEKIT_API_SECRET',
                'SIP_OUTBOUND_TRUNK_ID', 'DEEPGRAM_API_KEY', 'ORPHEUS_API_KEY'
            ]
            
            missing_vars = [var for var in required_env_vars if var not in env or not env[var]]
            if missing_vars:
                error_msg = f"Missing required environment variables: {', '.join(missing_vars)}"
                process_data.logs.append(f"[{datetime.now().isoformat()}] ERROR: {error_msg}")
                process_data.status = 'failed'
                process_data.error_message = error_msg
                process_data.end_time = datetime.now(timezone.utc)
                return
            
            # Step 1: Start the agent process first
            agent_cmd = build_agent_command(call_data)
            process_data.logs.append(f"[{datetime.now().isoformat()}] Starting agent: {' '.join(agent_cmd)}")
            
            # Print environment variables for debugging
            for key in required_env_vars:
                process_data.logs.append(f"[{datetime.now().isoformat()}] ENV: {key}={env.get(key, 'NOT_SET')}")
            
            # Start the agent process with the enhanced environment
            agent_process = subprocess.Popen(
                agent_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True,
                env=env,
                cwd=os.path.abspath(os.path.dirname(__file__))  # Run from the script directory
            )
            
            process_data.agent_process = agent_process  # Store the agent process
            process_data.logs.append(f"[{datetime.now().isoformat()}] Agent process started with PID {agent_process.pid}")
            
            # Start reading agent output in separate threads to avoid blocking
            def read_agent_output():
                try:
                    for line in iter(agent_process.stdout.readline, ''):
                        if line.strip():
                            process_data.logs.append(f"[{datetime.now().isoformat()}] AGENT OUTPUT: {line.strip()}")
                except Exception as e:
                    process_data.logs.append(f"[{datetime.now().isoformat()}] ERROR reading agent output: {str(e)}")
            
            def read_agent_error():
                try:
                    for line in iter(agent_process.stderr.readline, ''):
                        if line.strip():
                            process_data.logs.append(f"[{datetime.now().isoformat()}] AGENT ERROR: {line.strip()}")
                except Exception as e:
                    process_data.logs.append(f"[{datetime.now().isoformat()}] ERROR reading agent error output: {str(e)}")
            
            agent_output_thread = threading.Thread(target=read_agent_output, daemon=True)
            agent_output_thread.start()
            
            agent_error_thread = threading.Thread(target=read_agent_error, daemon=True)
            agent_error_thread.start()
            
            # Wait a bit to let the agent start up
            time.sleep(5)
            
            # Check if the agent process is still running
            if agent_process.poll() is not None:
                returncode = agent_process.poll()
                process_data.logs.append(f"[{datetime.now().isoformat()}] Agent process exited early with code {returncode}")
                if returncode != 0:
                    process_data.status = 'failed'
                    process_data.error_message = f"Agent process failed with exit code {returncode}"
                    process_data.end_time = datetime.now(timezone.utc)
                    process_data.exit_code = returncode
                    return
            else:
                process_data.logs.append(f"[{datetime.now().isoformat()}] Agent process is running successfully")
            
            # Step 2: Now run the dispatch command to initiate the call
            dispatch_cmd = build_outbound_call_command(call_data)
            process_data.logs.append(f"[{datetime.now().isoformat()}] Starting outbound call: {' '.join(dispatch_cmd)}")
            
            # Start the dispatch process with the enhanced environment
            dispatch_process = subprocess.Popen(
                dispatch_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True,
                env=env,
                cwd=os.path.abspath(os.path.dirname(__file__))  # Run from the script directory
            )
            
            process_data.dispatch_process = dispatch_process  # Store the dispatch process
            process_data.logs.append(f"[{datetime.now().isoformat()}] Dispatch process started with PID {dispatch_process.pid}")
            
            # Start reading dispatch output in separate threads to avoid blocking
            def read_dispatch_output():
                try:
                    for line in iter(dispatch_process.stdout.readline, ''):
                        if line.strip():
                            process_data.logs.append(f"[{datetime.now().isoformat()}] DISPATCH OUTPUT: {line.strip()}")
                except Exception as e:
                    process_data.logs.append(f"[{datetime.now().isoformat()}] ERROR reading dispatch output: {str(e)}")
            
            def read_dispatch_error():
                try:
                    for line in iter(dispatch_process.stderr.readline, ''):
                        if line.strip():
                            process_data.logs.append(f"[{datetime.now().isoformat()}] DISPATCH ERROR: {line.strip()}")
                except Exception as e:
                    process_data.logs.append(f"[{datetime.now().isoformat()}] ERROR reading dispatch error output: {str(e)}")
            
            dispatch_output_thread = threading.Thread(target=read_dispatch_output, daemon=True)
            dispatch_output_thread.start()
            
            dispatch_error_thread = threading.Thread(target=read_dispatch_error, daemon=True)
            dispatch_error_thread.start()
            
            # Wait for the dispatch command to complete
            dispatch_process.wait()
            dispatch_returncode = dispatch_process.returncode
            process_data.logs.append(f"[{datetime.now().isoformat()}] Dispatch process completed with code {dispatch_returncode}")
            
            # The agent process should keep running to handle the call
            process_data.logs.append(f"[{datetime.now().isoformat()}] Call initiated successfully, agent process continues running")
                
        except Exception as e:
            logger.error(f"Error starting outbound call process for {process_id}: {str(e)}")
            process_data.logs.append(f"[{datetime.now().isoformat()}] EXCEPTION: {str(e)}")
            process_data.status = 'failed'
            process_data.error_message = str(e)
            process_data.end_time = datetime.now(timezone.utc)
    
    thread = threading.Thread(target=run_call, daemon=True)
    thread.start()

# Process Management Endpoints

@app.route('/api/process', methods=['POST'])
@app.route('/api/processes', methods=['POST'])
def create_process():
    """Start a new call process"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        phone_number = data.get('phoneNumber')
        if not phone_number:
            return jsonify({"error": "phoneNumber is required"}), 400
        
        # Check concurrent process limit
        if process_manager.get_active_process_count() >= process_manager.max_concurrent_processes:
            return jsonify({
                "error": "Maximum concurrent processes reached"
            }), 429
        
        # Validate environment variables
        required_env_vars = [
            "LIVEKIT_URL", "LIVEKIT_API_KEY", "LIVEKIT_API_SECRET", 
            "SIP_OUTBOUND_TRUNK_ID", "DEEPGRAM_API_KEY", "ORPHEUS_API_KEY"
        ]
        
        missing_vars = []
        for var in required_env_vars:
            if not os.getenv(var):
                # Try to load from .env and .env.local
                var_value = None
                if os.path.exists('.env.local'):
                    with open('.env.local', 'r') as f:
                        for line in f:
                            if line.strip() and not line.startswith('#'):
                                try:
                                    key, value = line.strip().split('=', 1)
                                    if key == var:
                                        var_value = value.strip('"').strip("'")
                                        os.environ[var] = var_value
                                        break
                                except ValueError:
                                    continue
                
                if not var_value and os.path.exists('.env'):
                    with open('.env', 'r') as f:
                        for line in f:
                            if line.strip() and not line.startswith('#'):
                                try:
                                    key, value = line.strip().split('=', 1)
                                    if key == var:
                                        var_value = value.strip('"').strip("'")
                                        os.environ[var] = var_value
                                        break
                                except ValueError:
                                    continue
                
                if not var_value:
                    missing_vars.append(var)
        
        if missing_vars:
            return jsonify({"error": f"Missing required environment variables: {', '.join(missing_vars)}"}), 400
        
        call_data = {
            "phoneNumber": phone_number,
            "agentPrompt": data.get('agentPrompt', ''),
            "agentId": data.get('agentId', 'custom'),
            "voiceId": data.get('voiceId', 'tara'),
            "modelId": data.get('modelId', 'llama-3.3-70b-versatile'),
            "callerId": data.get('callerId')
        }
        
        # Create process
        process_id = process_manager.create_process(call_data)
        
        # Log the process creation
        logger.info(f"Creating new call process {process_id} for {phone_number}")
        
        # Start outbound call process using our simplified approach
        start_outbound_call_process(process_id, call_data)
        
        return jsonify({
            "processId": process_id,
            "status": "running",
            "startTime": datetime.now(timezone.utc).isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error in create_process: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/processes', methods=['GET'])
def list_processes():
    """Get all active and recent processes"""
    try:
        processes = []
        for process_data in process_manager.processes.values():
            processes.append({
                "id": process_data.process_id,
                "status": process_data.status,
                "startTime": process_data.start_time.isoformat(),
                "callData": process_data.call_data
            })
        
        return jsonify(processes)
    except Exception as e:
        logger.error(f"Error in list_processes: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/processes/<process_id>', methods=['GET'])
def get_process_details(process_id):
    """Get detailed information about a specific process"""
    try:
        process_data = process_manager.processes.get(process_id)
        if not process_data:
            return jsonify({"error": "Process not found"}), 404
        
        return jsonify({
            "processId": process_data.process_id,
            "status": process_data.status,
            "startTime": process_data.start_time.isoformat(),
            "endTime": process_data.end_time.isoformat() if process_data.end_time else None,
            "exitCode": process_data.exit_code,
            "callData": process_data.call_data,
            "logs": process_data.logs
        })
        
    except Exception as e:
        logger.error(f"Error in get_process_details: {str(e)}")
        return jsonify({"error": str(e)}), 500

def stop_process(process_id: str):
    """Stop a running process"""
    process_data = process_manager.processes.get(process_id)
    if not process_data:
        return jsonify({"error": "Process not found"}), 404
    
    if process_data.status != 'running':
        return jsonify({"error": "Process is not running"}), 400
    
    try:
        # Stop both processes if they exist
        if process_data.dispatch_process:
            try:
                process_data.dispatch_process.terminate()
                process_data.logs.append(f"[{datetime.now().isoformat()}] Dispatch process terminated")
            except Exception as e:
                process_data.logs.append(f"[{datetime.now().isoformat()}] Error terminating dispatch process: {str(e)}")
        
        if process_data.agent_process:
            try:
                process_data.agent_process.terminate()
                process_data.logs.append(f"[{datetime.now().isoformat()}] Agent process terminated")
            except Exception as e:
                process_data.logs.append(f"[{datetime.now().isoformat()}] Error terminating agent process: {str(e)}")
        
        process_data.status = 'stopped'
        process_data.end_time = datetime.now(timezone.utc)
        return jsonify({"message": "Process stopped"})
    except Exception as e:
        logger.error(f"Error stopping process {process_id}: {str(e)}")
        return jsonify({"error": f"Error stopping process: {str(e)}"}), 500

@app.route('/api/processes/<process_id>', methods=['DELETE'])
@app.route('/api/process/<process_id>', methods=['DELETE'])
def stop_process_endpoint(process_id):
    """Stop a running process"""
    logger.info(f"Received request to stop process {process_id}")
    return stop_process(process_id)

# Agent Management Endpoints
@app.route('/api/agents', methods=['GET'])
def list_agents():
    """Get a list of available agent templates"""
    return jsonify(AGENTS)

@app.route('/api/agents', methods=['POST'])
def create_agent():
    """Create a new agent template"""
    try:
        data = request.get_json()
        
        if not data or not data.get('name') or not data.get('prompt'):
            return jsonify({"error": "Name and prompt are required"}), 400
        
        new_agent = {
            "id": str(uuid.uuid4()),
            "name": data['name'],
            "prompt": data['prompt']
        }
        
        AGENTS.append(new_agent)
        return jsonify(new_agent), 201
        
    except Exception as e:
        logger.error(f"Error in create_agent: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/agents/<agent_id>', methods=['PUT'])
def update_agent(agent_id):
    """Update an existing agent template"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        for agent in AGENTS:
            if agent['id'] == agent_id:
                if 'name' in data:
                    agent['name'] = data['name']
                if 'prompt' in data:
                    agent['prompt'] = data['prompt']
                return jsonify(agent)
        
        return jsonify({"error": "Agent not found"}), 404
        
    except Exception as e:
        logger.error(f"Error in update_agent: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/agents/<agent_id>', methods=['DELETE'])
def delete_agent(agent_id):
    """Delete an agent template"""
    try:
        for i, agent in enumerate(AGENTS):
            if agent['id'] == agent_id:
                del AGENTS[i]
                return jsonify({"message": "Agent deleted successfully"})
        
        return jsonify({"error": "Agent not found"}), 404
        
    except Exception as e:
        logger.error(f"Error in delete_agent: {str(e)}")
        return jsonify({"error": str(e)}), 500

# Voice Endpoints
@app.route('/api/voices', methods=['GET'])
def list_voices():
    """Get available voice options"""
    return jsonify(VOICES)

# Model Endpoints
@app.route('/api/models', methods=['GET'])
def list_models():
    """Get available LLM models"""
    return jsonify(MODELS)

# Optional Call Endpoints (for future use)
@app.route('/api/calls/history', methods=['GET'])
def get_call_history():
    """Get call history (placeholder)"""
    return jsonify([])

@app.route('/api/calls/initiate', methods=['POST'])
def initiate_call():
    """Initiate a call (placeholder - redirects to processes)"""
    return create_process()

@app.route('/api/calls/batch', methods=['POST'])
def batch_calls():
    """Batch call initiation (placeholder)"""
    return jsonify({"error": "Batch calls not implemented"}), 501

# Health check endpoint
@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "active_processes": process_manager.get_active_process_count(),
        "total_processes": len(process_manager.processes),
        "timestamp": datetime.now(timezone.utc).isoformat()
    })

# Cleanup task
def cleanup_task():
    """Background task to cleanup old processes"""
    while True:
        time.sleep(300)  # Run every 5 minutes
        process_manager.cleanup_old_processes()

# Start cleanup task
cleanup_thread = threading.Thread(target=cleanup_task, daemon=True)
cleanup_thread.start()

if __name__ == '__main__':
    port = 9090  # Use port 9090 as expected by frontend
    
    logger.info(f"Starting backend server on port {port}")
    logger.info(f"Max concurrent processes: {process_manager.max_concurrent_processes}")
    logger.info(f"API Base URL: http://localhost:{port}/api")
    
    app.run(
        host='0.0.0.0', 
        port=port, 
        debug=False,
        threaded=True
    )