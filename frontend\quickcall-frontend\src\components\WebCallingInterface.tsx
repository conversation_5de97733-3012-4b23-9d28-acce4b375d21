"use client";

// API URL configuration
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:9090';

import { NoAgentNotification } from "./NoAgentNotification";
import TranscriptionView from "./TranscriptionView";
import {
  BarVisualizer,
  DisconnectButton,
  RoomAudioRenderer,
  RoomContext,
  VideoTrack,
  VoiceAssistantControlBar,
  useVoiceAssistant,
} from "@livekit/components-react";
import { AnimatePresence, motion } from "framer-motion";
import { Room, RoomEvent } from "livekit-client";
import { useCallback, useEffect, useState } from "react";
import { Mic, Phone, PhoneOff, Loader2 } from "lucide-react";

interface ConnectionDetails {
  serverUrl: string;
  participantToken: string;
}

interface WebCallingInterfaceProps {
  agentId: string;
  agentName: string;
  onEndCall?: () => void;
}

export function WebCallingInterface({ agentId, agentName, onEndCall }: WebCallingInterfaceProps) {
  const [room] = useState(new Room());
  const [isSpawningWorker, setIsSpawningWorker] = useState(false);
  const [workerError, setWorkerError] = useState("");

  const onConnectButtonClicked = useCallback(async () => {
    try {
      setIsSpawningWorker(true);
      setWorkerError("");

      // Check if LiveKit worker is available for the selected agent
      if (agentId) {
        console.log("Checking LiveKit worker for agent ID:", agentId);
        
        const workerResponse = await fetch(`${API_URL}/api/livekit/spawn`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            agent_id: agentId,
          }),
        });

        if (!workerResponse.ok) {
          const errorData = await workerResponse.json();
          throw new Error(errorData.error || 'LiveKit worker not available for this agent');
        }

        const workerResult = await workerResponse.json();
        console.log('LiveKit worker status:', workerResult);
      }

      // Get connection details
      const url = new URL(
        process.env.NEXT_PUBLIC_CONN_DETAILS_ENDPOINT ?? "/api/connection-details",
        window.location.origin
      );
      
      // Add agent ID to the connection details request
      if (agentId) {
        url.searchParams.set('agentId', agentId);
      }
      
      const response = await fetch(url.toString());
      
      if (!response.ok) {
        throw new Error('Failed to get connection details');
      }
      
      const connectionDetailsData: ConnectionDetails = await response.json();

      await room.connect(connectionDetailsData.serverUrl, connectionDetailsData.participantToken);
      await room.localParticipant.setMicrophoneEnabled(true);
    } catch (error) {
      console.error('Error connecting to voice assistant:', error);
      setWorkerError(error instanceof Error ? error.message : 'Failed to connect');
    } finally {
      setIsSpawningWorker(false);
    }
  }, [room, agentId]);

  useEffect(() => {
    room.on(RoomEvent.MediaDevicesError, onDeviceFailure);

    return () => {
      room.off(RoomEvent.MediaDevicesError, onDeviceFailure);
    };
  }, [room]);

  return (
    <div className="w-full h-full bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20 flex flex-col overflow-hidden">
      <RoomContext.Provider value={room}>
        <SimpleVoiceAssistant 
          onConnectButtonClicked={onConnectButtonClicked}
          isSpawningWorker={isSpawningWorker}
          workerError={workerError}
          agentName={agentName}
          onEndCall={onEndCall}
        />
      </RoomContext.Provider>
    </div>
  );
}

function SimpleVoiceAssistant(props: { 
  onConnectButtonClicked: () => void;
  isSpawningWorker: boolean;
  workerError: string;
  agentName: string;
  onEndCall?: () => void;
}) {
  const { state: agentState } = useVoiceAssistant();

  return (
    <div className="w-full h-full flex flex-col overflow-hidden">
      <AnimatePresence mode="wait">
        {agentState === "disconnected" ? (
          <motion.div
            key="disconnected"
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -20 }}
            transition={{ duration: 0.4, ease: [0.16, 1, 0.3, 1] }}
            className="flex flex-col items-center justify-center w-full h-full p-4 sm:p-6 md:p-8"
          >
            <div className="text-center space-y-4 sm:space-y-6 md:space-y-8 w-full max-w-md mx-auto">
              {/* Agent Avatar */}
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="relative mx-auto w-24 h-24 mb-6"
              >
                <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-xl">
                  <Phone className="w-10 h-10 text-white" />
                </div>
                <div className="absolute -inset-2 bg-gradient-to-br from-blue-500/20 to-purple-600/20 rounded-full animate-pulse" />
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.2 }}
              >
                <h3 className="text-2xl font-bold text-gray-900 mb-3">Ready to chat with {props.agentName}</h3>
                <p className="text-gray-600 leading-relaxed">
                  Start a voice conversation with your AI assistant. Click the button below to begin.
                </p>
              </motion.div>

              {props.workerError && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="bg-red-50 border border-red-200 rounded-xl p-4 shadow-sm"
                >
                  <p className="text-red-700 text-sm font-medium">{props.workerError}</p>
                </motion.div>
              )}

              <motion.button
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.3 }}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="group relative w-full py-4 px-8 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 overflow-hidden"
                onClick={() => props.onConnectButtonClicked()}
                disabled={props.isSpawningWorker}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700" />
                <div className="relative flex items-center justify-center gap-3">
                  {props.isSpawningWorker ? (
                    <>
                      <Loader2 className="w-5 h-5 animate-spin" />
                      Starting conversation...
                    </>
                  ) : (
                    <>
                      <Phone className="w-5 h-5" />
                      Start Conversation
                    </>
                  )}
                </div>
              </motion.button>
            </div>
          </motion.div>
        ) : (
          <motion.div
            key="connected"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.4, ease: [0.16, 1, 0.3, 1] }}
            className="flex flex-col w-full h-full overflow-hidden"
          >
            {/* Enhanced Header */}
            <div className="flex items-center justify-between p-3 sm:p-4 lg:p-6 bg-white/80 backdrop-blur-sm border-b border-gray-200/50 shadow-sm flex-shrink-0">
              <div className="flex items-center gap-4">
                <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center shadow-md">
                  <Phone className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="font-bold text-gray-900 text-lg">Voice Assistant</h3>
                  <p className="text-sm text-gray-600">Connected with {props.agentName}</p>
                </div>
              </div>

              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2 px-3 py-1.5 bg-green-50 border border-green-200 rounded-full">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  <span className="text-xs font-semibold text-green-700">Connected</span>
                </div>

                <div className="flex items-center gap-2">
                  <div className="[&_.lk-button]:bg-gray-50 [&_.lk-button]:hover:bg-gray-100 [&_.lk-button]:border-gray-200 [&_.lk-button]:text-gray-700 [&_.lk-button]:shadow-sm [&_.lk-button]:w-10 [&_.lk-button]:h-10 [&_.lk-button]:rounded-lg [&_.lk-button]:transition-all [&_.lk-button]:hover:shadow-md">
                    <VoiceAssistantControlBar controls={{ leave: false }} />
                  </div>
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <DisconnectButton
                      className="bg-red-500 hover:bg-red-600 text-white rounded-lg w-10 h-10 shadow-md hover:shadow-lg transition-all duration-200 flex items-center justify-center"
                      onClick={props.onEndCall}
                    >
                      <PhoneOff className="w-4 h-4" />
                    </DisconnectButton>
                  </motion.div>
                </div>
              </div>
            </div>

            {/* Main Content Area */}
            <div className="flex-1 flex flex-col overflow-hidden">
              {/* Enhanced Agent Visualizer */}
              <div className="flex-shrink-0 p-3 sm:p-4 lg:p-6 bg-gradient-to-b from-white/50 to-transparent">
                <AgentVisualizer agentName={props.agentName} />
              </div>

              {/* Chat section with enhanced styling */}
              <div className="flex-1 bg-white/60 backdrop-blur-sm min-h-0 border-t border-gray-200/50 overflow-hidden">
                <TranscriptionView selectedAgentName={props.agentName} />
              </div>
            </div>

            <RoomAudioRenderer />
            <NoAgentNotification state={agentState} />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

function AgentVisualizer({ agentName }: { agentName: string }) {
  const { state: agentState, videoTrack, audioTrack } = useVoiceAssistant();

  if (videoTrack) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="h-[140px] w-full max-w-sm mx-auto rounded-xl overflow-hidden shadow-lg border border-gray-200/50"
      >
        <VideoTrack trackRef={videoTrack} />
      </motion.div>
    );
  }

  const getStateInfo = () => {
    switch (agentState) {
      case "listening":
        return { text: "Listening...", color: "from-blue-500 to-cyan-500", icon: Mic };
      case "thinking":
        return { text: "Thinking...", color: "from-amber-500 to-orange-500", icon: Loader2 };
      case "speaking":
        return { text: "Speaking...", color: "from-green-500 to-emerald-500", icon: Phone };
      default:
        return { text: "Ready", color: "from-gray-500 to-slate-500", icon: Mic };
    }
  };

  const stateInfo = getStateInfo();
  const StateIcon = stateInfo.icon;

    return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="h-[120px] sm:h-[140px] md:h-[160px] w-full max-w-lg mx-auto"
    >
      <div
        className={`relative w-full h-full bg-gradient-to-br ${stateInfo.color} rounded-lg sm:rounded-xl flex items-center justify-center shadow-lg overflow-hidden`}
      >
        {/* Background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.3)_0%,transparent_50%)]" />
        </div>

        {/* Audio visualizer */}
        <div className="absolute inset-0 flex items-center justify-center p-4">
          <BarVisualizer
            state={agentState}
            barCount={9}
            trackRef={audioTrack}
            className="agent-visualizer [&>div]:bg-white/90 [&>div]:shadow-sm [&>div]:rounded-full [&>div]:mx-1"
            options={{ minHeight: 12, maxHeight: 80 }}
          />
        </div>

        {/* State indicator */}
        <motion.div
          key={agentState}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center gap-2 px-4 py-2 bg-white/95 backdrop-blur-sm rounded-full shadow-lg"
        >
          <StateIcon className={`w-4 h-4 text-gray-700 ${agentState === "thinking" ? "animate-spin" : ""}`} />
          <span className="text-sm font-semibold text-gray-700">{stateInfo.text}</span>
        </motion.div>
      </div>
    </motion.div>
  );
}

function onDeviceFailure(error: Error) {
  console.error(error);
  alert(
    "Error acquiring camera or microphone permissions. Please make sure you grant the necessary permissions in your browser and reload the tab"
  );
}