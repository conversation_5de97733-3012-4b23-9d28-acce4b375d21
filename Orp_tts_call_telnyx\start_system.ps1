# QuickCall System Startup Script
# This script starts the enhanced calling system with proper environment checking

Write-Host "🚀 QuickCall Enhanced Calling System" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan
Write-Host ""

# Check if we're in the right directory
if (-not (Test-Path "simple_backend.py")) {
    Write-Host "❌ Error: simple_backend.py not found in current directory" -ForegroundColor Red
    Write-Host "Please run this script from the Orp_tts_call_telnyx directory" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Correct usage:" -ForegroundColor White
    Write-Host "cd Orp_tts_call_telnyx" -ForegroundColor Gray
    Write-Host "powershell -ExecutionPolicy Bypass -File start_system.ps1" -ForegroundColor Gray
    pause
    exit 1
}

# Check Python installation
Write-Host "🔍 Checking Python installation..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ Python found: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python not found. Please install Python 3.8 or later." -ForegroundColor Red
    pause
    exit 1
}

# Check if virtual environment should be activated
if (Test-Path "venv/Scripts/Activate.ps1") {
    Write-Host "🔄 Activating virtual environment..." -ForegroundColor Yellow
    & "venv/Scripts/Activate.ps1"
    Write-Host "✅ Virtual environment activated" -ForegroundColor Green
} elseif (Test-Path "../venv/Scripts/Activate.ps1") {
    Write-Host "🔄 Activating virtual environment..." -ForegroundColor Yellow
    & "../venv/Scripts/Activate.ps1"
    Write-Host "✅ Virtual environment activated" -ForegroundColor Green
} else {
    Write-Host "⚠️  No virtual environment found (optional)" -ForegroundColor Yellow
}

# Create data directory if it doesn't exist
if (-not (Test-Path "data")) {
    Write-Host "📁 Creating data directory..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Path "data" | Out-Null
    Write-Host "✅ Data directory created" -ForegroundColor Green
}

# Check key environment variables
Write-Host ""
Write-Host "🔧 Environment Check:" -ForegroundColor Yellow
$envVars = @{
    "TELNYX_API_KEY" = $env:TELNYX_API_KEY
    "TELNYX_APP_ID" = $env:TELNYX_APP_ID
    "LIVEKIT_URL" = $env:LIVEKIT_URL
    "LIVEKIT_API_KEY" = $env:LIVEKIT_API_KEY
    "LIVEKIT_API_SECRET" = $env:LIVEKIT_API_SECRET
}

$missingVars = @()
foreach ($var in $envVars.Keys) {
    if ($envVars[$var]) {
        Write-Host "✅ $var is set" -ForegroundColor Green
    } else {
        Write-Host "⚠️  $var not set (will use defaults)" -ForegroundColor Yellow
        $missingVars += $var
    }
}

if ($missingVars.Count -gt 0) {
    Write-Host ""
    Write-Host "💡 To set environment variables, create a .env file or use:" -ForegroundColor Cyan
    Write-Host "`$env:VARIABLE_NAME = 'your_value'" -ForegroundColor Gray
}

Write-Host ""
Write-Host "🎯 Starting Enhanced Calling System..." -ForegroundColor Cyan
Write-Host "Server will be available at: http://localhost:9090" -ForegroundColor White
Write-Host ""
Write-Host "📊 Key Features Available:" -ForegroundColor Yellow
Write-Host "• Real-time webhook call tracking" -ForegroundColor White
Write-Host "• Enhanced call answer detection" -ForegroundColor White
Write-Host "• API backup verification" -ForegroundColor White
Write-Host "• Auto-correction of mismarked calls" -ForegroundColor White
Write-Host "• Live campaign monitoring" -ForegroundColor White
Write-Host ""
Write-Host "🛠️  Quick Fix Commands:" -ForegroundColor Yellow
Write-Host "• Fix mismarked calls: POST /api/call-logs/fix-mismarked" -ForegroundColor Gray
Write-Host "• Verify statuses: POST /api/call-logs/verify-status" -ForegroundColor Gray
Write-Host ""
Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Red
Write-Host ""

# Start the server
try {
    python simple_backend.py
} catch {
    Write-Host ""
    Write-Host "❌ Failed to start server. Error details above." -ForegroundColor Red
    Write-Host ""
    Write-Host "Common solutions:" -ForegroundColor Yellow
    Write-Host "1. Install required packages: pip install -r requirements.txt" -ForegroundColor Gray
    Write-Host "2. Check Python version: python --version" -ForegroundColor Gray
    Write-Host "3. Verify file permissions" -ForegroundColor Gray
    pause
} 