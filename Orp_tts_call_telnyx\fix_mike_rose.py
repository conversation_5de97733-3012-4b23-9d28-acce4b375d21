#!/usr/bin/env python3

import json
from datetime import datetime

# Load call logs
with open('data/call_logs.json', 'r') as f:
    call_logs = json.load(f)

# Find <PERSON> rose's call from 2025-06-08T10:56
mike_rose_index = -1
for i, call in enumerate(call_logs):
    if (call.get('contact_phone') == '+919552775831' and 
        call.get('contact_name') == 'Mike rose' and
        call.get('timestamp', '').startswith('2025-06-08T10:56')):
        mike_rose_index = i
        break

if mike_rose_index >= 0:
    print(f"Found <PERSON> rose's call at index {mike_rose_index}")
    print(f"Current status: {call_logs[mike_rose_index]['status']}")
    
    # Update status to answered
    call_logs[mike_rose_index]['status'] = 'answered'
    call_logs[mike_rose_index]['manual_update'] = {
        'timestamp': datetime.now().isoformat(),
        'old_status': 'initiated',
        'new_status': 'answered',
        'reason': 'Manual fix - call was answered with conversation'
    }
    
    # Save back to file
    with open('data/call_logs.json', 'w') as f:
        json.dump(call_logs, f, indent=2)
    
    print("✅ Fixed <PERSON> rose's call status to 'answered'")
else:
    print("❌ Could not find Mike rose's call")

# Also check current statuses
print("\nCurrent call statuses for 2025-06-08T10:56:")
for i, call in enumerate(call_logs):
    if call.get('timestamp', '').startswith('2025-06-08T10:56'):
        print(f"{i}: {call.get('contact_name')} - {call.get('contact_phone')} - {call.get('status')}") 
 