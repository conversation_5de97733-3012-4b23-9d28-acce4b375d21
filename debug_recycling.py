#!/usr/bin/env python3
"""
Debug script to understand why recycling isn't finding unresponsive contacts
"""

import json
from datetime import datetime

def debug_recycling():
    # Load data
    with open('Orp_tts_call_telnyx/data/campaigns.json', 'r') as f:
        campaigns = json.load(f)
    
    with open('Orp_tts_call_telnyx/data/call_logs.json', 'r') as f:
        call_logs = json.load(f)
    
    # Check the specific campaign
    campaign_id = "campaign_1749373258"  # The "wfrwqer" campaign
    
    if campaign_id not in campaigns:
        print(f"Campaign {campaign_id} not found!")
        return
    
    campaign = campaigns[campaign_id]
    print(f"Campaign: {campaign['name']}")
    print(f"Status: {campaign['status']}")
    print(f"Contacts: {len(campaign['contacts'])}")
    
    # Get call logs for this campaign
    campaign_calls = [log for log in call_logs if log.get('campaign_id') == campaign_id]
    print(f"Total call logs: {len(campaign_calls)}")
    
    # Group by phone number (like the recycling logic does)
    calls_by_number = {}
    for call in campaign_calls:
        phone_number = call.get('contact_phone')
        if phone_number:
            if phone_number not in calls_by_number:
                calls_by_number[phone_number] = call
            else:
                # Compare timestamps to keep the latest
                current_time = calls_by_number[phone_number].get('timestamp', '')
                new_time = call.get('timestamp', '')
                if new_time > current_time:
                    calls_by_number[phone_number] = call
    
    print(f"Unique phone numbers: {len(calls_by_number)}")
    
    # Check each contact
    print("\n=== CONTACT ANALYSIS ===")
    recyclable_statuses = ["no_answer", "failed", "busy", "initiated"]
    numbers_to_recycle = []
    
    for contact in campaign['contacts']:
        contact_phone = contact.get('phone_number')
        contact_name = contact.get('name', 'Unknown')
        
        print(f"\nContact: {contact_name} ({contact_phone})")
        
        if contact_phone in calls_by_number:
            call = calls_by_number[contact_phone]
            call_status = call.get('status', 'initiated')
            print(f"  Last call status: {call_status}")
            print(f"  Call timestamp: {call.get('timestamp', '')}")
            print(f"  Call duration: {call.get('call_duration', 0)}")
            
            if call_status in recyclable_statuses:
                numbers_to_recycle.append({
                    'phone_number': contact_phone,
                    'contact_name': contact_name,
                    'last_status': call_status
                })
                print(f"  ✅ RECYCLABLE (status: {call_status})")
            else:
                print(f"  ❌ NOT RECYCLABLE (status: {call_status})")
        else:
            print(f"  📞 NEVER CALLED - should be recyclable")
            numbers_to_recycle.append({
                'phone_number': contact_phone,
                'contact_name': contact_name,
                'last_status': 'not_called'
            })
    
    print(f"\n=== RESULT ===")
    print(f"Total contacts that should be recycled: {len(numbers_to_recycle)}")
    
    if numbers_to_recycle:
        print("Contacts to recycle:")
        for contact in numbers_to_recycle:
            print(f"  - {contact['contact_name']} ({contact['phone_number']}) - {contact['last_status']}")
    else:
        print("❌ NO CONTACTS TO RECYCLE FOUND - This is the bug!")

if __name__ == '__main__':
    debug_recycling() 