import json

# Load call logs
with open('data/call_logs.json', 'r') as f:
    logs = json.load(f)

# Get recent calls (last 10)
recent = logs[-10:]

print("Recent calls:")
for call in recent:
    print(f"Campaign: {call['campaign_id']}, Contact: {call['contact_name']}, Duration: {call['call_duration']}, Status: {call['status']}")

# Count calls per campaign
campaign_counts = {}
for call in logs:
    cid = call['campaign_id']
    campaign_counts[cid] = campaign_counts.get(cid, 0) + 1

print("\nCalls per campaign:")
for cid, count in campaign_counts.items():
    if count > 0:
        print(f"Campaign {cid}: {count} calls") 