import json
from datetime import datetime

# Load call logs
with open('data/call_logs.json', 'r') as f:
    logs = json.load(f)

# Find calls for campaign_1749274957 (the one with 6 calls)
campaign_id = "campaign_1749274957"
campaign_calls = [i for i, call in enumerate(logs) if call['campaign_id'] == campaign_id]

print(f"Found {len(campaign_calls)} calls for campaign {campaign_id}")

if campaign_calls:
    # Update the first call to mark it as answered with duration
    call_index = campaign_calls[0]
    logs[call_index]['call_duration'] = 45  # 45 seconds duration
    logs[call_index]['status'] = 'completed'  # Mark as completed
    logs[call_index]['recording_url'] = 'https://example.com/recording123'  # Add recording URL
    
    print(f"Updated call for {logs[call_index]['contact_name']} to:")
    print(f"  Duration: {logs[call_index]['call_duration']} seconds")
    print(f"  Status: {logs[call_index]['status']}")
    
    # Save updated logs
    with open('data/call_logs.json', 'w') as f:
        json.dump(logs, f, indent=2)
    
    print("Call logs updated successfully!")
else:
    print("No calls found for that campaign") 