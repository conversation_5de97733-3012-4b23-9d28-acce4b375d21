"""
Call Log Analyzer - Analyzes actual call logs and provides accurate campaign statistics
"""

import json
from pathlib import Path
from typing import Dict, List, Any

class CallLogAnalyzer:
    def __init__(self, data_dir: str = "data"):
        self.data_dir = Path(data_dir)
        
    def load_call_logs(self) -> List[Dict]:
        """Load call logs from JSON file"""
        call_logs_file = self.data_dir / "call_logs.json"
        if call_logs_file.exists():
            try:
                with open(call_logs_file, 'r') as f:
                    return json.load(f)
            except:
                return []
        return []
    
    def analyze_call_outcome(self, call: Dict) -> str:
        """Determine actual call outcome based on duration and status"""
        duration = call.get('call_duration', 0)
        status = call.get('status', 'unknown')
        telnyx_call_id = call.get('telnyx_call_id', '')
        
        # If duration is properly recorded and > 5 seconds, it's completed
        if duration > 5:
            return 'completed'
        # If duration is short but > 0, consider it a short call (still completed)
        elif duration > 0:
            return 'short_call'
        # If we have a Telnyx call ID, the call was actually attempted (not just logged)
        elif telnyx_call_id and telnyx_call_id.strip():
            # Since duration isn't being updated, we can't distinguish easily
            # For now, assume calls with Telnyx IDs that weren't updated are no_answer
            return 'no_answer'
        # If status indicates failure
        elif status == 'failed':
            return 'failed'
        # If status is 'initiated' with no duration update, likely no answer
        elif status == 'initiated':
            return 'no_answer'
        else:
            return 'unknown'
    
    def analyze_campaign(self, campaign_id: str) -> Dict[str, Any]:
        """Analyze calls for a specific campaign"""
        all_logs = self.load_call_logs()
        campaign_logs = [log for log in all_logs if log.get('campaign_id') == campaign_id]
        
        if not campaign_logs:
            return {'error': 'No call logs found', 'total_calls': 0}
        
        outcomes = {'completed': 0, 'short_call': 0, 'no_answer': 0, 'failed': 0, 'unknown': 0}
        total_duration = 0
        call_details = []
        
        for call in campaign_logs:
            outcome = self.analyze_call_outcome(call)
            outcomes[outcome] += 1
            
            duration = call.get('call_duration', 0)
            total_duration += duration
            
            call_details.append({
                'contact_name': call.get('contact_name', 'Unknown'),
                'contact_phone': call.get('contact_phone', ''),
                'duration': duration,
                'outcome': outcome,
                'timestamp': call.get('timestamp', ''),
                'status': call.get('status', 'unknown')
            })
        
        return {
            'campaign_id': campaign_id,
            'total_calls': len(campaign_logs),
            'call_statistics': {
                'total_calls': len(campaign_logs),
                'completed': outcomes['completed'] + outcomes['short_call'],
                'failed': outcomes['failed'],
                'no_answer': outcomes['no_answer'],
                'unknown': outcomes['unknown']
            },
            'detailed_outcomes': outcomes,
            'total_duration': total_duration,
            'call_details': call_details
        }
    
    def print_report(self, campaign_id: str):
        """Print detailed report for a campaign"""
        analysis = self.analyze_campaign(campaign_id)
        
        if 'error' in analysis:
            print(f"❌ {analysis['error']}")
            return
        
        print(f"📊 ACCURATE Campaign Analysis: {campaign_id}")
        print("=" * 60)
        print(f"📞 Total Calls: {analysis['total_calls']}")
        print(f"✅ Completed: {analysis['call_statistics']['completed']}")
        print(f"❌ Failed: {analysis['call_statistics']['failed']}")
        print(f"📵 No Answer: {analysis['call_statistics']['no_answer']}")
        print(f"❓ Unknown: {analysis['call_statistics']['unknown']}")
        print(f"⏱️ Total Duration: {analysis['total_duration']}s")
        print()
        
        print("📋 Call Details:")
        for call in analysis['call_details']:
            print(f"  📞 {call['contact_name']} ({call['contact_phone']})")
            print(f"     Duration: {call['duration']}s | Outcome: {call['outcome']}")
            print(f"     Status: {call['status']} | Time: {call['timestamp']}")
            print()

def main():
    import sys
    analyzer = CallLogAnalyzer()
    
    if len(sys.argv) > 1:
        campaign_id = sys.argv[1]
        analyzer.print_report(campaign_id)
    else:
        print("Usage: python call_log_analyzer.py <campaign_id>")

if __name__ == "__main__":
    main() 