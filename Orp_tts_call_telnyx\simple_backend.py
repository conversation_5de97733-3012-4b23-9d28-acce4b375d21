import os
import json
import time
import logging
import subprocess
import threading
import uuid
import requests
import io
import sys
import socket
import multiprocessing
from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
from datetime import datetime, timezone
from dataclasses import dataclass, field
from typing import Dict, List, Optional
import base64
import tempfile
from werkzeug.utils import secure_filename
import re
from groq import Groq  # Still needed for LLM models endpoint
import telnyx
from tts_service import test_tts, generate_tts, get_available_voices
import time as time_module
from threading import Timer
from pathlib import Path

# Import campaign utilities
try:
    from campaign_statistics import CampaignStats, get_campaign_statistics, get_unresponsive_contacts
    from recycle_unresponsive import RecycleUnresponsive, create_recycled_campaign, recycle_campaign_in_place
    from call_log_analyzer import CallLogAnalyzer
    from telnyx_call_analyzer import TelnyxCallAnalyzer
    CAMPAIGN_STATS_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Campaign statistics modules not available: {e}")
    CAMPAIGN_STATS_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Default System Prompt
DEFAULT_SYSTEM_PROMPT = """# You are a helpful and friendly AI assistant.

You are designed to be helpful, harmless, and honest in all your interactions. Your primary goal is to assist the caller with any questions or tasks they might have.

## Guidelines for your conversation:
- Be friendly and conversational, but professional.
- Provide comprehensive and accurate information.
- Ask clarifying questions when needed.
- Avoid making assumptions about the caller.
- Be respectful of the caller's time.
- Offer additional help when appropriate.

You have a wide range of knowledge and can help with many topics including technology, science, history, culture, and more.
"""

# Default Emotional System Prompt
DEFAULT_EMOTIONAL_PROMPT = """You are a conversational AI designed to be engaging, professional, and human-like, delivering responses that are concise (one or two sentences) and infused with subtle emotional cues to mimic natural conversation. Use the following text-based tags to convey emotions naturally, without emojis:

<giggle>: Indicates lighthearted amusement or a gentle chuckle, used for mildly funny or cheerful moments.
<laugh>: Represents genuine laughter for something truly humorous, used sparingly for stronger amusement.
<chuckle>: Denotes a quiet or subtle laugh, often for something mildly amusing or thoughtful.
Guidelines for Using Emotional Tags
Natural Integration: Incorporate tags seamlessly into sentences, as a person might in spoken or written conversation.
Show, Don't Tell: Use tags to express emotions instead of stating them (e.g., instead of "I'm happy," use <giggle> in a cheerful response).
Contextual Appropriateness: Match tags to the conversation's tone and context, ensuring they are professional and relevant.
Sparsity and Subtlety: Use tags sparingly, typically no more than once per response, to avoid overuse or exaggeration and maintain a natural flow.
Professional Tone: Avoid excessive laughter, overly emotional responses, or inappropriate tones (e.g., no sexual undertones or forced humor).
Objective: Create engaging, human-like responses that feel warm, friendly, and professional, fostering trust and relatability with the user.
Example Usage
Greeting: "Great to connect, <chuckle>! Got a moment to chat?"</chuckle>
Clarification: "Thanks for sharing, <chuckle>! Could you tell me a bit more?"</chuckle>
Handling Hesitation: "I totally get it, <giggle>. What's most important to you right now?"</giggle>"""

AGENT_CONFIG = {
    "1": {
        "name": "Mia",
        "voice_id": "elise",  # Example voice ID, adjust as needed
        "model_id": "llama-3.3-70b-versatile", # Example model, adjust as needed
        "system_prompt": DEFAULT_SYSTEM_PROMPT
    },
    "2": {
        "name": "David",
        "voice_id": "david", # Example voice ID
        "model_id": "gpt-4o", # Example model
        "system_prompt": DEFAULT_SYSTEM_PROMPT.replace("helpful and friendly AI assistant", "knowledgeable and efficient AI support agent")
    }
    # Add more agents here as needed
}

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Persistent storage using JSON files
import json
from pathlib import Path

# Create data directory
DATA_DIR = Path(__file__).parent / "data"
DATA_DIR.mkdir(exist_ok=True)

AGENTS_FILE = DATA_DIR / "agents.json"
CAMPAIGNS_FILE = DATA_DIR / "campaigns.json" 
SIP_TRUNKS_FILE = DATA_DIR / "sip_trunks.json"
CALL_LOGS_FILE = DATA_DIR / "call_logs.json"

def load_json_data(file_path, default=None):
    """Load data from JSON file"""
    if default is None:
        default = {}
    try:
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception as e:
        logger.error(f"Error loading {file_path}: {e}")
    return default

def save_json_data(file_path, data):
    """Save data to JSON file"""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, default=str)
        return True
    except Exception as e:
        logger.error(f"Error saving {file_path}: {e}")
        return False

def log_call(call_data):
    """Log a call to persistent storage"""
    call_log_entry = {
        'id': str(uuid.uuid4()),
        'timestamp': datetime.now().isoformat(),
        'contact_name': call_data.get('contact_name', 'Unknown'),
        'contact_phone': call_data.get('contact_phone', ''),
        'from_phone': call_data.get('from_phone', ''),
        'agent_name': call_data.get('agent_name', ''),
        'campaign_id': call_data.get('campaign_id', ''),
        'status': call_data.get('status', 'initiated'),
        'notes': call_data.get('contact_notes', ''),
        'call_duration': call_data.get('call_duration', 0),
        'recording_url': call_data.get('recording_url', ''),
        'telnyx_call_id': call_data.get('telnyx_call_id', '')
    }
    
    call_logs.append(call_log_entry)
    save_json_data(CALL_LOGS_FILE, call_logs)
    logger.info(f"📋 Call logged: {call_log_entry['contact_name']} ({call_log_entry['contact_phone']})")
    return call_log_entry

# Load persistent data
agents_storage = load_json_data(AGENTS_FILE, {})
campaigns_storage = load_json_data(CAMPAIGNS_FILE, {})
sip_trunk_mapping = load_json_data(SIP_TRUNKS_FILE, {})
call_logs = load_json_data(CALL_LOGS_FILE, [])

# Telnyx configuration
TELNYX_API_KEY = os.getenv('TELNYX_API_KEY', '**********************************************************')
TELNYX_APP_ID = os.getenv('TELNYX_APP_ID', '2702376459367876227')

# Initialize Telnyx
telnyx.api_key = TELNYX_API_KEY

# Default SIP trunk ID (your current one)
DEFAULT_SIP_TRUNK_ID = os.getenv('SIP_OUTBOUND_TRUNK_ID', 'ST_mdGsY9Kf6vPJ')

# LiveKit Configuration
LIVEKIT_URL = os.getenv('LIVEKIT_URL', 'wss://testing-zjqnplxr.livekit.cloud')
LIVEKIT_API_KEY = os.getenv('LIVEKIT_API_KEY', 'APIqFuS9DNsSaxd')
LIVEKIT_API_SECRET = os.getenv('LIVEKIT_API_SECRET', 'lx5Nthx9u9okwcDfzxjH07VSSSqZ5XNT91pqF83Se0L')

def initialize_default_agents():
    """Initialize default agents in storage if empty"""
    if not agents_storage:
        default_agents = [
            {
                'id': 'mia',
                'name': 'Mia',
                'description': 'Friendly sales assistant',
                'system_prompt': 'You are Mia, a friendly and professional sales assistant.',
                'emotional_prompt': DEFAULT_EMOTIONAL_PROMPT,
                'model_id': 'llama-3.3-70b-versatile',
                'voice_id': 'Tara',
                'interrupt_speech_duration': 1.0,
                'allow_interruptions': True,
                'initial_greeting': 'Hello my name is Mia, how can I help you today?',
                'use_initial_greeting': True,
                'is_active': False
            },
            {
                'id': 'alex',
                'name': 'Alex',
                'description': 'Technical support specialist',
                'system_prompt': 'You are Alex, a knowledgeable technical support specialist.',
                'emotional_prompt': DEFAULT_EMOTIONAL_PROMPT,
                'model_id': 'llama-3.1-8b-instant',
                'voice_id': 'Elise',
                'interrupt_speech_duration': 1.0,
                'allow_interruptions': True,
                'initial_greeting': 'Hello my name is Alex, I\'m here to help with your technical questions.',
                'use_initial_greeting': True,
                'is_active': False
            }
        ]
        
        for agent in default_agents:
            agents_storage[agent['id']] = agent
        
        # Save default agents to persistent storage
        save_json_data(AGENTS_FILE, agents_storage)

# Initialize default agents on startup
initialize_default_agents()

def combine_prompts(emotional_prompt: str, system_prompt: str) -> str:
    """Combine emotional prompt with system prompt"""
    if not emotional_prompt:
        return system_prompt
    return f"{emotional_prompt}\n\n{system_prompt}"

def start_dynamic_agent_internal(agent_name: str, llm_prompt: str, agent_type: str = 'phone'):
    """Internal function to start a dynamic agent without HTTP request"""
    if not agent_name or not llm_prompt:
        return {"success": False, "error": "agent_name and llm_prompt are required"}

    logger.info(f"Starting dynamic agent internally: {agent_name} (type: {agent_type})")

    try:
        # Choose template based on agent type
        if agent_type == 'web':
            template_path = os.path.join(os.path.dirname(__file__), "web_agent_template.py")
        else:
            template_path = os.path.join(os.path.dirname(__file__), "orp.py")
            
        if not os.path.exists(template_path):
            logger.error(f"Template not found at {template_path}")
            return {"success": False, "error": f"Template not found: {template_path}"}

        with open(template_path, 'r', encoding='utf-8') as f:
            original_content = f.read()

        # Escape the incoming llm_prompt to be safely embedded in a Python triple-quoted string
        escaped_llm_prompt = llm_prompt.replace('"""', '\"\"\"')
        
        # Replace _system_prompt
        prompt_replacement_string = f'_system_prompt = """{escaped_llm_prompt}"""'
        new_content = re.sub(r'_system_prompt\s*=\s*r?"""(?:.|\n)*?"""', prompt_replacement_string, original_content, count=1)
        
        if new_content == original_content:
            logger.warning("System prompt replacement in agent template might have failed. Check the template structure.")

        # Replace user_identity
        target_identity_line = 'user_identity = "DYNAMIC_AGENT_USER_IDENTITY_PLACEHOLDER"'
        replacement_identity_line = f'user_identity = "{agent_name}"'
        if target_identity_line in new_content:
            new_content = new_content.replace(target_identity_line, replacement_identity_line)
        else:
            logger.warning(f"Target line '{target_identity_line}' for agent name replacement not found in template.")

        # Replace agent_name placeholder in WorkerOptions
        worker_agent_name_placeholder = '"DYNAMIC_WORKER_AGENT_NAME_PLACEHOLDER"'
        actual_agent_name_quoted = f'"{agent_name}"'
        if worker_agent_name_placeholder in new_content:
            new_content = new_content.replace(worker_agent_name_placeholder, actual_agent_name_quoted)
            logger.info(f"Replaced worker agent name placeholder with {actual_agent_name_quoted}")
        else:
            logger.warning(f"Worker agent name placeholder '{worker_agent_name_placeholder}' not found in template.")

        safe_agent_filename = secure_filename(agent_name.replace(' ', '_')) + f"_{agent_type}.py"
        new_agent_filepath = os.path.join(DYNAMIC_AGENTS_DIR, safe_agent_filename)

        with open(new_agent_filepath, 'w', encoding='utf-8') as f:
            f.write(new_content)
        logger.info(f"Created dynamic agent script: {new_agent_filepath}")

        # Run the newly created agent script
        cmd = [sys.executable, new_agent_filepath, "dev"]
        process_cwd = os.path.dirname(__file__)
        process = subprocess.Popen(cmd, cwd=process_cwd)
        
        # Generate a unique agent ID for tracking
        agent_id = str(uuid.uuid4())
        
        # Register the dynamic agent in the running_agents system
        running_agents[agent_id] = AgentProcess(
            agent_id=agent_id,
            agent_name=agent_name,
            system_prompt=llm_prompt,
            voice_id="elise",
            model_id="llama-3.3-70b-versatile",
            process=process,
            status="running"
        )
        
        # Also add to AGENT_CONFIG for compatibility
        AGENT_CONFIG[agent_id] = {
            "name": agent_name,
            "voice_id": "elise",
            "model_id": "llama-3.3-70b-versatile",
            "system_prompt": llm_prompt
        }
        
        # Store the process for management
        running_dynamic_agents[agent_name] = process

        logger.info(f"Successfully started dynamic agent '{agent_name}' with PID {process.pid} and ID {agent_id}")
        return {
            "success": True,
            "message": f"Dynamic agent '{agent_name}' started successfully.",
            "agent_name": agent_name,
            "agent_id": agent_id,
            "script_path": new_agent_filepath,
            "pid": process.pid,
            "agent_type": agent_type
        }

    except Exception as e:
        logger.error(f"Error starting dynamic agent '{agent_name}': {str(e)}", exc_info=True)
        return {"success": False, "error": f"Failed to start dynamic agent: {str(e)}"}

@app.route('/dynamic_agent/start', methods=['POST'])
def start_dynamic_agent():
    data = request.json
    agent_name = data.get('agent_name')
    llm_prompt = data.get('llm_prompt')
    agent_type = data.get('agent_type', 'phone')  # 'phone' or 'web'

    if not agent_name or not llm_prompt:
        return jsonify({"error": "agent_name and llm_prompt are required"}), 400

    logger.info(f"Received request to start dynamic agent: {agent_name} (type: {agent_type})")

    result = start_dynamic_agent_internal(agent_name, llm_prompt, agent_type)
    
    if result.get("success"):
        return jsonify(result), 200
    else:
        return jsonify(result), 500

@app.route('/make_call', methods=['POST'])
def make_call():
    data = request.json
    agent_name_for_dispatch = data.get('agent_name_for_dispatch') # e.g., "Mia" instead of "outbound-caller"
    phone_number = data.get('phone_number')
    agent_id = data.get('agent_id')  # Optional: if provided, look up the agent name

    if not phone_number:
        logger.error("Missing phone_number in /make_call request")
        return jsonify({"error": "phone_number is required"}), 400

    # Check if we need to create a phone agent or if agent name is already provided
    if agent_id and not agent_name_for_dispatch:
        agent_data = running_agents.get(agent_id)
        if agent_data:
            logger.info(f"Found agent '{agent_data.agent_name}' for phone call")
            
            # Create a phone agent dynamically for this call
            try:
                phone_agent_name = f"{agent_data.agent_name}_phone"
                logger.info(f"Creating phone agent '{phone_agent_name}' for phone call")
                
                # Create phone agent using the same system prompt but with phone type
                phone_agent_response = start_dynamic_agent_internal(
                    agent_name=phone_agent_name,
                    llm_prompt=agent_data.system_prompt,
                    agent_type='phone'
                )
                
                if phone_agent_response.get('success'):
                    # Use the phone agent name for dispatch
                    agent_name_for_dispatch = phone_agent_name
                    logger.info(f"Successfully created phone agent '{phone_agent_name}' for call")
                else:
                    logger.error(f"Failed to create phone agent: {phone_agent_response.get('error')}")
                    return jsonify({"error": "Failed to create phone agent for call"}), 500
                    
            except Exception as e:
                logger.error(f"Error creating phone agent: {str(e)}")
                return jsonify({"error": f"Failed to create phone agent: {str(e)}"}), 500
                
        else:
            # Try to get from AGENT_CONFIG as fallback
            agent_config = AGENT_CONFIG.get(agent_id)
            if agent_config:
                phone_agent_name = f"{agent_config['name']}_phone"
                phone_agent_response = start_dynamic_agent_internal(
                    agent_name=phone_agent_name,
                    llm_prompt=agent_config['system_prompt'],
                    agent_type='phone'
                )
                
                if phone_agent_response.get('success'):
                    agent_name_for_dispatch = phone_agent_name
                    logger.info(f"Created phone agent from config: '{phone_agent_name}'")
                else:
                    logger.error(f"Failed to create phone agent from config")
                    return jsonify({"error": "Failed to create phone agent for call"}), 500
            else:
                logger.error(f"Agent ID '{agent_id}' not found in running agents or config")
                return jsonify({"error": f"Agent ID '{agent_id}' not found"}), 400
    elif agent_name_for_dispatch:
        # If agent name is provided directly, check if it's already a phone agent
        if not agent_name_for_dispatch.endswith('_phone'):
            # Create phone version only if not already a phone agent
            phone_agent_name = f"{agent_name_for_dispatch}_phone"
            
            # Check if this phone agent already exists to avoid duplication
            existing_agent = None
            for agent_id, agent_process in running_agents.items():
                if agent_process.agent_name == phone_agent_name:
                    existing_agent = agent_process
                    break
            
            if existing_agent and existing_agent.status == "running":
                # Use existing phone agent
                agent_name_for_dispatch = phone_agent_name
                logger.info(f"Using existing phone agent: '{phone_agent_name}'")
            else:
                # Create new phone agent - get system prompt from campaign agent if possible
                default_prompt = "You are a helpful AI assistant making outbound phone calls."
                
                # Try to get the system prompt from the original agent
                for agent_id, agent_process in running_agents.items():
                    if agent_process.agent_name == agent_name_for_dispatch:
                        default_prompt = agent_process.system_prompt
                        break
                
                phone_agent_response = start_dynamic_agent_internal(
                    agent_name=phone_agent_name,
                    llm_prompt=default_prompt,
                    agent_type='phone'
                )
                
                if phone_agent_response.get('success'):
                    agent_name_for_dispatch = phone_agent_name
                    logger.info(f"Created phone agent from name: '{phone_agent_name}'")
                else:
                    logger.error(f"Failed to create phone agent from name")
                    return jsonify({"error": "Failed to create phone agent for call"}), 500
        else:
            # Agent name is already a phone agent, use it directly
            logger.info(f"Using existing phone agent: '{agent_name_for_dispatch}'")
    else:
        # Create default phone agent
        agent_name_for_dispatch = "outbound-caller"
        default_prompt = "You are a helpful AI assistant making outbound phone calls."
        
        phone_agent_response = start_dynamic_agent_internal(
            agent_name=agent_name_for_dispatch,
            llm_prompt=default_prompt,
            agent_type='phone'
        )
        
        if phone_agent_response.get('success'):
            logger.info(f"Created default phone agent: '{agent_name_for_dispatch}'")
        else:
            logger.warning("Failed to create default phone agent, using fallback")

    logger.info(f"Using phone agent '{agent_name_for_dispatch}' for phone call")

    # Get SIP trunk info if from_phone is provided
    from_phone = data.get('from_phone')
    sip_trunk_id = data.get('sip_trunk_id')
    
    # If from_phone is provided, get the appropriate SIP trunk
    if from_phone:
        sip_trunk_id = sip_trunk_mapping.get(from_phone, DEFAULT_SIP_TRUNK_ID)
        logger.info(f"Using SIP trunk {sip_trunk_id} for number {from_phone}")
    elif not sip_trunk_id:
        sip_trunk_id = DEFAULT_SIP_TRUNK_ID
        logger.info(f"Using default SIP trunk {sip_trunk_id}")

    # Build structured metadata for the agent
    if from_phone or sip_trunk_id != DEFAULT_SIP_TRUNK_ID:
        metadata = {
            "target_phone": phone_number,
            "from_phone": from_phone,
            "sip_trunk_id": sip_trunk_id,
            "campaign_id": data.get('campaign_id'),
            "call_id": data.get('call_id'),
            "contact_name": data.get('contact_name', 'Unknown')
        }
        metadata_str = json.dumps(metadata)
        logger.info(f"Using structured metadata: {metadata_str}")
    else:
        metadata_str = phone_number
        logger.info(f"Using simple metadata: {metadata_str}")

    cmd = [
        "lk", "dispatch", "create",
        "--new-room",
        "--agent-name", agent_name_for_dispatch,
        "--metadata", metadata_str
    ]
    
    process_cwd = os.path.dirname(__file__) # Directory of simple_backend.py

    try:
        logger.info(f"Executing command: {' '.join(cmd)} in CWD: {process_cwd}")
        result = subprocess.run(cmd, cwd=process_cwd, capture_output=True, text=True, check=False)

        if result.returncode == 0:
            logger.info(f"lk dispatch command successful. Output: {result.stdout}")
            # Attempt to parse stdout as JSON if it looks like JSON, otherwise return as string
            try:
                output_json = json.loads(result.stdout)
            except json.JSONDecodeError:
                output_json = result.stdout.strip()
            return jsonify({"success": True, "message": "Call dispatched successfully.", "output": output_json}), 200
        else:
            logger.error(f"lk dispatch command failed. Return code: {result.returncode}. Error: {result.stderr}. Output: {result.stdout}")
            return jsonify({
                "success": False, 
                "message": "Failed to dispatch call.", 
                "error": result.stderr.strip(), 
                "output": result.stdout.strip(),
                "returncode": result.returncode
            }), 500

    except FileNotFoundError:
        logger.error(f"lk command not found. Ensure LiveKit CLI is installed and in PATH.")
        return jsonify({"error": "lk command not found. Is LiveKit CLI installed and in PATH?"}), 500
    except Exception as e:
        logger.error(f"Error executing lk dispatch command: {e}", exc_info=True)
        return jsonify({"error": f"An unexpected error occurred: {str(e)}"}), 500

# Dictionary to store running agents
running_agents = {}

# Dictionary to store call processes
call_processes = {}

# Directory for dynamically generated agent scripts
DYNAMIC_AGENTS_DIR = os.path.join(os.path.dirname(__file__), "dynamic_agents")
if not os.path.exists(DYNAMIC_AGENTS_DIR):
    os.makedirs(DYNAMIC_AGENTS_DIR)
    logger.info(f"Created dynamic_agents directory at {DYNAMIC_AGENTS_DIR}")

def cleanup_old_agent_files():
    """Clean up old agent files to prevent accumulation"""
    try:
        import time
        current_time = time.time()
        one_hour_ago = current_time - 3600  # 1 hour ago
        
        for filename in os.listdir(DYNAMIC_AGENTS_DIR):
            if filename.endswith('.py'):
                filepath = os.path.join(DYNAMIC_AGENTS_DIR, filename)
                file_mtime = os.path.getmtime(filepath)
                
                # Remove files older than 1 hour
                if file_mtime < one_hour_ago:
                    try:
                        os.remove(filepath)
                        logger.info(f"Cleaned up old agent file: {filename}")
                    except Exception as e:
                        logger.warning(f"Could not remove old agent file {filename}: {e}")
    except Exception as e:
        logger.warning(f"Error during agent cleanup: {e}")

# Run cleanup on startup
cleanup_old_agent_files()

# Dictionary to store running dynamic agents (optional, for future management)
running_dynamic_agents = {}

# Dictionary to store LiveKit workers
livekit_workers = {}

def get_free_port() -> int:
    """Get a free port for LiveKit worker"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.bind(('', 0))
        port = s.getsockname()[1]
        logger.info(f"Free port obtained for LiveKit worker: {port}")
        return port

def start_livekit_worker(agent_id: str, agent_name: str, system_prompt: str) -> Optional[int]:
    """Start a LiveKit worker for web calling"""
    try:
        # Check if we have an existing LiveKit worker for this agent
        if agent_id in livekit_workers:
            proc = livekit_workers[agent_id]
            if proc.is_alive():
                logger.info(f"LiveKit worker already running for agent '{agent_name}' with PID {proc.pid}")
                return None
            else:
                # Clean up dead process
                del livekit_workers[agent_id]

        # Get a free port for the worker
        port = get_free_port()
        
        # Find an available LiveKit worker script
        # We'll look for the current_livekit_voice.py script
        worker_script_path = None
        potential_paths = [
            os.path.join(os.path.dirname(__file__), "voice-agents", "current_livekit_voice.py"),
            os.path.join(os.path.dirname(__file__), "..", "voice-agents", "current_livekit_voice.py"),
            os.path.join("voice-agents", "current_livekit_voice.py")
        ]
        
        for path in potential_paths:
            if os.path.exists(path):
                worker_script_path = path
                break
        
        if not worker_script_path:
            logger.warning("LiveKit worker script not found. LiveKit web calling will not be available.")
            return None
        
        logger.info(f"Starting LiveKit worker for agent '{agent_name}' using script: {worker_script_path}")
        
        # Start the LiveKit worker process
        # Note: This is a simplified version - in production you'd want more robust process management
        cmd = [sys.executable, worker_script_path]
        env = os.environ.copy()
        env['WORKER_PORT'] = str(port)
        env['ASSISTANT_ID'] = agent_name
        env['SYSTEM_PROMPT'] = system_prompt
        env['ROOM_NAME'] = f'agent_room_{agent_id}'
        
        process = subprocess.Popen(
            cmd, 
            cwd=os.path.dirname(worker_script_path),
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Store the process
        livekit_workers[agent_id] = process
        
        logger.info(f"Started LiveKit worker for agent '{agent_name}' with PID {process.pid} on port {port}")
        return port
        
    except Exception as e:
        logger.error(f"Error starting LiveKit worker for agent '{agent_name}': {str(e)}", exc_info=True)
        return None

def stop_livekit_worker(agent_id: str) -> bool:
    """Stop a LiveKit worker"""
    try:
        if agent_id in livekit_workers:
            proc = livekit_workers[agent_id]
            if proc.is_alive():
                proc.terminate()
                try:
                    proc.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    proc.kill()
                    proc.wait()
            del livekit_workers[agent_id]
            logger.info(f"Stopped LiveKit worker for agent ID '{agent_id}'")
            return True
        return False
    except Exception as e:
        logger.error(f"Error stopping LiveKit worker for agent ID '{agent_id}': {str(e)}", exc_info=True)
        return False

def load_env_variables():
    """Load environment variables from .env and .env.local files"""
    env_vars = {}
    
    # Try to load from .env.local first (takes precedence)
    if os.path.exists('.env.local'):
        with open('.env.local', 'r') as f:
            for line in f:
                if line.strip() and not line.startswith('#'):
                    try:
                        key, value = line.strip().split('=', 1)
                        env_vars[key] = value.strip('"').strip("'")
                    except ValueError:
                        continue
    
    # Then load from .env
    if os.path.exists('.env'):
        with open('.env', 'r') as f:
            for line in f:
                if line.strip() and not line.startswith('#'):
                    try:
                        key, value = line.strip().split('=', 1)
                        # Only set if not already set from .env.local
                        if key not in env_vars:
                            env_vars[key] = value.strip('"').strip("'")
                    except ValueError:
                        continue
    
    # Update environment variables
    for key, value in env_vars.items():
        os.environ[key] = value
    
    return env_vars

@dataclass
class AgentProcess:
    agent_id: str
    agent_name: str
    system_prompt: str
    voice_id: str
    model_id: str
    process: Optional[subprocess.Popen] = None
    start_time: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    logs: List[str] = field(default_factory=list)
    status: str = "initializing"

@dataclass
class CallProcess:
    call_id: str
    phone_number: str
    agent_id: str
    process: Optional[subprocess.Popen] = None
    start_time: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    logs: List[str] = field(default_factory=list)
    status: str = "initializing"

def create_agent_file(agent_id, agent_name, system_prompt, voice_id, model_id):
    """Create a Python file for a specific agent with its parameters"""
    # Create the agents directory if it doesn't exist
    agents_dir = os.path.join(os.getcwd(), "agents")
    if not os.path.exists(agents_dir):
        os.makedirs(agents_dir)
        logger.info(f"Created agents directory at {agents_dir}")
    
    # Create the file path
    agent_file = os.path.join(agents_dir, f"agent_{agent_id}.py")
    
    # Escape any triple quotes in the system prompt to avoid syntax errors
    escaped_system_prompt = system_prompt.replace("'''", "\\'''")
    
    # Create the agent file content
    content = f'''import os
import sys
import time
import subprocess
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('agent_{agent_id}')

# Set environment variables for parameterized_orp.py to use
os.environ['AGENT_NAME'] = '{agent_name}'
os.environ['SYSTEM_PROMPT'] = """{escaped_system_prompt}"""
os.environ['VOICE_ID'] = '{voice_id}'
os.environ['MODEL_ID'] = '{model_id}'

# Get the parent directory path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# This script runs parameterized_orp.py with specific parameters
if __name__ == '__main__':
    logger.info(f"Starting agent {agent_name} with voice {voice_id} and model {model_id}")
    
    # Build the command to run parameterized_orp.py directly
    cmd = [
        sys.executable,  # Python executable
        os.path.join(parent_dir, "parameterized_orp.py"),
        "dev",
        '--tts_voice', "{voice_id}",
        '--llm_model', "{model_id}",
        "--agent_name", "{agent_name}"
    ]
    
    # Add system prompt if provided
    if """{escaped_system_prompt}""":
        cmd.extend(["--system_prompt", """{escaped_system_prompt}"""])
    
    logger.info(f"Running command: {{' '.join(cmd)}}")
    
    try:
        # Run parameterized_orp.py as a subprocess
        # We don't capture output so it will show in the terminal
        process = subprocess.Popen(
            cmd,
            cwd=parent_dir,
            env=os.environ.copy()
        )
        
        # Wait for the process to complete
        process.wait()
        logger.info("Agent process completed")
        
    except Exception as e:
        logger.error(f"Error running agent: {{str(e)}}")
    
    # Keep the terminal window open after the process completes
    logger.info("Keeping terminal window open. Press Ctrl+C to exit.")
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Exiting...")
'''
    
    # Write the file
    with open(agent_file, 'w') as f:
        f.write(content)
    
    logger.info(f"Created agent file: {agent_file}")
    return agent_file

def build_agent_command(agent_id, agent_name, system_prompt, voice_id, model_id):
    """Build command to start an agent process"""
    # Create the agent file
    agent_file = create_agent_file(agent_id, agent_name, system_prompt, voice_id, model_id)
    
    # Build the command to run the agent file
    if os.name == 'nt':  # Windows
        # For Windows, open a new terminal window to run the agent
        # Set the window title to include the agent_id for easier process management
        return [
            "cmd.exe", "/c", "start", 
            f"Agent {agent_id} - {agent_name}",  # Window title
            "cmd.exe", "/k", 
            "python", agent_file, "dev"
        ]
    else:  # Linux/Mac
        # For Linux/Mac, use a terminal if available
        if os.system("which gnome-terminal > /dev/null 2>&1") == 0:
            return ["gnome-terminal", "--title", f"Agent {agent_id} - {agent_name}", "--", "python", agent_file, "dev"]
        elif os.system("which xterm > /dev/null 2>&1") == 0:
            return ["xterm", "-T", f"Agent {agent_id} - {agent_name}", "-e", f"python {agent_file} dev"]
        else:
            # Fallback to running without a terminal
            return ["python", agent_file, "dev"]

def build_call_command(phone_number, agent_name_descriptive):
    """Build command to start an outbound call based on user-provided syntax"""
    clean_number = phone_number.strip()
    return [
        "lk", "dispatch", "create",
        "--new-room",
        "--agent-name", agent_name_descriptive,  # Use the descriptive agent name (e.g., 'Mia')
        "--metadata", clean_number  # Phone number is passed via metadata
    ]

def read_process_output(process, logs, process_type):
    """Read and log process output"""
    for line in iter(process.stdout.readline, b''):
        try:
            line_str = line.decode('utf-8').strip()
            if line_str:
                timestamp = datetime.now().isoformat()
                log_entry = f"[{timestamp}] {line_str}"
                logs.append(log_entry)
                logger.info(f"{process_type} output: {line_str}")
        except Exception as e:
            logger.error(f"Error reading {process_type} output: {str(e)}")

def read_process_error(process, logs, process_type):
    """Read and log process errors"""
    for line in iter(process.stderr.readline, b''):
        try:
            line_str = line.decode('utf-8').strip()
            if line_str:
                timestamp = datetime.now().isoformat()
                log_entry = f"[{timestamp}] ERROR: {line_str}"
                logs.append(log_entry)
                logger.error(f"{process_type} error: {line_str}")
        except Exception as e:
            logger.error(f"Error reading {process_type} error output: {str(e)}")

@app.route('/api/agents', methods=['GET'])
def list_agents():
    """List all agents (both running and not running)"""
    try:
        agents = []
        
        # Add agents from storage
        for agent_id, agent_data in agents_storage.items():
            is_active = agent_id in running_agents
            agents.append({
                "id": agent_id,
                "name": agent_data['name'],
                "description": agent_data['description'],
                "systemPrompt": agent_data['system_prompt'],
                "emotionalPrompt": agent_data.get('emotional_prompt', DEFAULT_EMOTIONAL_PROMPT),
                "modelId": agent_data.get('model_id', 'llama-3.3-70b-versatile'),
                "voiceId": agent_data.get('voice_id', 'Tara'),
                "interruptSpeechDuration": agent_data.get('interrupt_speech_duration', 1.0),
                "allowInterruptions": agent_data.get('allow_interruptions', True),
                "initialGreeting": agent_data.get('initial_greeting', f'Hello my name is {agent_data["name"]}, how can I help you today?'),
                "useInitialGreeting": agent_data.get('use_initial_greeting', True),
                "isActive": is_active,
            })
        
        return jsonify({"agents": agents})
    except Exception as e:
        logger.error(f"Error listing agents: {str(e)}")
        return jsonify({"error": str(e)}), 500

# New CRUD endpoints for agent management
@app.route('/api/agents', methods=['POST'])
def create_agent():
    """Create a new agent"""
    try:
        data = request.json
        if not data or not data.get('name') or not data.get('systemPrompt'):
            return jsonify({'error': 'Name and system prompt are required'}), 400
        
        agent_id = str(uuid.uuid4())
        agents_storage[agent_id] = {
            'name': data['name'],
            'description': data.get('description', ''),
            'system_prompt': data['systemPrompt'],
            'emotional_prompt': data.get('emotionalPrompt', DEFAULT_EMOTIONAL_PROMPT),
            'model_id': data.get('model_id', 'llama-3.3-70b-versatile'),
            'voice_id': data.get('voice_id', 'Tara'),
            'interrupt_speech_duration': data.get('interruptSpeechDuration', 1.0),
            'allow_interruptions': data.get('allowInterruptions', True),
            'initial_greeting': data.get('initialGreeting', f'Hello my name is {data["name"]}, how can I help you today?'),
            'use_initial_greeting': data.get('useInitialGreeting', True),
            'is_active': False,
            'created_at': datetime.now().isoformat()
        }
        
        # Save agent to storage
        agents_storage[agent_id] = {
            'id': agent_id,
            'name': data['name'],
            'description': data.get('description', ''),
            'system_prompt': data['systemPrompt'],
            'emotional_prompt': data.get('emotionalPrompt', DEFAULT_EMOTIONAL_PROMPT),
            'model_id': data.get('modelId', 'llama-3.3-70b-versatile'),
            'voice_id': data.get('voice_id', 'Tara'),
            'interrupt_speech_duration': data.get('interruptSpeechDuration', 1.0),
            'allow_interruptions': data.get('allowInterruptions', True),
            'initial_greeting': data.get('initialGreeting', f'Hello my name is {data["name"]}, how can I help you today?'),
            'use_initial_greeting': data.get('useInitialGreeting', True),
            'is_active': False,
            'created_at': datetime.now().isoformat()
        }
        
        # Save to persistent storage
        save_json_data(AGENTS_FILE, agents_storage)
        
        return jsonify({'agent_id': agent_id, 'message': 'Agent created successfully'})
    except Exception as e:
        logger.error(f"Error creating agent: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/agents/<agent_id>', methods=['PUT'])
def update_agent(agent_id):
    """Update an existing agent"""
    try:
        if agent_id not in agents_storage:
            return jsonify({'error': 'Agent not found'}), 404
        
        data = request.json
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Update agent data
        agents_storage[agent_id].update({
            'name': data.get('name', agents_storage[agent_id]['name']),
            'description': data.get('description', agents_storage[agent_id]['description']),
            'system_prompt': data.get('systemPrompt', agents_storage[agent_id]['system_prompt']),
            'emotional_prompt': data.get('emotionalPrompt', agents_storage[agent_id].get('emotional_prompt', DEFAULT_EMOTIONAL_PROMPT)),
            'model_id': data.get('modelId', agents_storage[agent_id].get('model_id', 'llama-3.3-70b-versatile')),
            'voice_id': data.get('voiceId', agents_storage[agent_id].get('voice_id', 'Tara')),
            'interrupt_speech_duration': data.get('interruptSpeechDuration', agents_storage[agent_id].get('interrupt_speech_duration', 1.0)),
            'allow_interruptions': data.get('allowInterruptions', agents_storage[agent_id].get('allow_interruptions', True)),
            'initial_greeting': data.get('initialGreeting', agents_storage[agent_id].get('initial_greeting', '')),
            'use_initial_greeting': data.get('useInitialGreeting', agents_storage[agent_id].get('use_initial_greeting', True)),
        })
        
        # Save to persistent storage
        save_json_data(AGENTS_FILE, agents_storage)
        
        return jsonify({'message': 'Agent updated successfully', 'agent': {
            'id': agent_id,
            'name': agents_storage[agent_id]['name'],
            'description': agents_storage[agent_id]['description'],
            'systemPrompt': agents_storage[agent_id]['system_prompt'],
            'emotionalPrompt': agents_storage[agent_id]['emotional_prompt'],
            'modelId': agents_storage[agent_id]['model_id'],
            'voiceId': agents_storage[agent_id]['voice_id'],
            'interruptSpeechDuration': agents_storage[agent_id]['interrupt_speech_duration'],
            'allowInterruptions': agents_storage[agent_id]['allow_interruptions'],
            'initialGreeting': agents_storage[agent_id]['initial_greeting'],
            'useInitialGreeting': agents_storage[agent_id]['use_initial_greeting'],
            'isActive': agents_storage[agent_id]['is_active']
        }})
        
    except Exception as e:
        logger.error(f"Error updating agent: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/agents/<agent_id>', methods=['DELETE'])
def delete_agent(agent_id):
    """Delete an agent"""
    try:
        if agent_id not in agents_storage:
            return jsonify({'error': 'Agent not found'}), 404
        
        # Stop the agent if it's active
        if agent_id in running_agents:
            try:
                process = running_agents[agent_id].process
                if process:
                    process.terminate()
                    process.wait(timeout=5)
                del running_agents[agent_id]
            except Exception as e:
                logger.error(f"Error stopping agent process: {e}")
        
        del agents_storage[agent_id]
        
        # Save to persistent storage
        save_json_data(AGENTS_FILE, agents_storage)
        
        return jsonify({'message': 'Agent deleted successfully'})
    except Exception as e:
        logger.error(f"Error deleting agent: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/activate_agent', methods=['POST'])
def activate_agent():
    """Activate an agent"""
    try:
        data = request.json
        agent_id = data.get('agent_id')
        
        if not agent_id:
            return jsonify({"error": "Agent ID is required"}), 400
        
        if agent_id not in agents_storage:
            return jsonify({"error": "Agent not found"}), 404
        
        agent_data = agents_storage[agent_id]
        
        # Combine emotional prompt with system prompt
        combined_prompt = combine_prompts(
            agent_data.get('emotional_prompt', DEFAULT_EMOTIONAL_PROMPT),
            agent_data['system_prompt']
        )
        
        # Create the dynamic agent
        result = start_dynamic_agent_internal(
            agent_name=agent_data['name'],
            llm_prompt=combined_prompt,
            agent_type='phone'
        )
        
        if result.get('success'):
            agents_storage[agent_id]['is_active'] = True
            return jsonify({"message": f"Agent {agent_data['name']} activated successfully"})
        else:
            return jsonify({"error": result.get('error', 'Failed to activate agent')}), 500
            
    except Exception as e:
        logger.error(f"Error activating agent: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/deactivate_agent', methods=['POST'])
def deactivate_agent():
    """Deactivate an agent"""
    try:
        data = request.json
        agent_id = data.get('agent_id')
        
        if not agent_id:
            return jsonify({"error": "Agent ID is required"}), 400
        
        if agent_id not in agents_storage:
            return jsonify({"error": "Agent not found"}), 404
        
        agent_data = agents_storage[agent_id]
        
        # Stop the agent process
        if agent_id in running_agents:
            try:
                process = running_agents[agent_id].process
                if process:
                    process.terminate()
                    process.wait(timeout=5)
                del running_agents[agent_id]
            except Exception as e:
                logger.error(f"Error stopping agent process: {e}")
        
        agents_storage[agent_id]['is_active'] = False
        return jsonify({"message": f"Agent {agent_data['name']} deactivated successfully"})
        
    except Exception as e:
        logger.error(f"Error deactivating agent: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/calls', methods=['POST'])
@app.route('/api/process', methods=['POST'])  # Support both endpoints as per memory
@app.route('/api/processes', methods=['POST'])  # Support both endpoints as per memory
def start_call():
    """Start a new outbound call"""
    try:
        # Log the raw request data for debugging
        logger.info(f"Received call request: {request.data}")
        
        data = request.get_json()
        logger.info(f"Parsed JSON data: {data}")
        
        if not data:
            logger.error("No data provided in request")
            return jsonify({"error": "No data provided"}), 400
        
        # Log all keys in the request data
        logger.info(f"Request data keys: {list(data.keys())}")
        
        phone_number = data.get('phoneNumber')
        if not phone_number:
            logger.error("Phone number is missing from request")
            return jsonify({"error": "Phone number is required"}), 400
        
        logger.info(f"Phone number from request: {phone_number}")
        
        agent_id = data.get('agentId')
        if not agent_id:
            logger.error("Agent ID is missing from request")
            return jsonify({"error": "Agent ID is required"}), 400
        
        logger.info(f"Agent ID from request: {agent_id}")
        
        # Check if agent is running
        agent_data = running_agents.get(agent_id)
        logger.info(f"Running agents: {list(running_agents.keys())}")
        
        if not agent_data:
            logger.error(f"Agent {agent_id} not found in running_agents")
            return jsonify({"error": f"Agent {agent_id} is not running. Please activate the agent first."}), 400
        
        if agent_data.status != "running":
            logger.error(f"Agent {agent_id} status is {agent_data.status}, not running")
            return jsonify({"error": f"Agent {agent_id} is not in running state (status: {agent_data.status}). Please activate the agent first."}), 400
        
        # Load environment variables
        env_vars = load_env_variables()
        
        # Validate required environment variables for LiveKit
        required_env_vars = [
            "LIVEKIT_URL", "LIVEKIT_API_KEY", "LIVEKIT_API_SECRET", 
            "SIP_OUTBOUND_TRUNK_ID"
        ]
        
        missing_vars = []
        for var in required_env_vars:
            if var not in env_vars and var not in os.environ:
                missing_vars.append(var)
        
        if missing_vars:
            return jsonify({"error": f"Missing required environment variables: {', '.join(missing_vars)}"}), 400
        
        # Generate call ID
        call_id = str(uuid.uuid4())
        
        # Get the descriptive agent name from AGENT_CONFIG
        agent_config_details = AGENT_CONFIG.get(agent_id)
        if not agent_config_details:
            logger.error(f"Agent configuration for ID {agent_id} not found.")
            return jsonify({"error": f"Agent configuration for ID {agent_id} not found"}), 404
        descriptive_agent_name = agent_config_details['name']

        # Build call command - use the descriptive agent name for LiveKit
        cmd = build_call_command(phone_number, descriptive_agent_name)
        
        # Create call process object
        call_process = CallProcess(
            call_id=call_id,
            phone_number=phone_number,
            agent_id=agent_id
        )
        
        # Log the command we're about to run
        logger.info(f"Starting call with command: {' '.join(cmd)}")
        
        try:
            # Start the call process in a completely new terminal window
            if os.name == 'nt':  # Windows
                # For Windows, open a new terminal window with a descriptive title
                window_title = f"Call {call_id} - {phone_number} - Agent {agent_id}"
                cmd_str = " ".join(cmd)
                
                # Use start command to open a completely new terminal window
                terminal_cmd = [
                    "cmd.exe", "/c", "start", 
                    window_title,  # Window title
                    "cmd.exe", "/k", 
                    cmd_str  # The command as a single string
                ]
                
                logger.info(f"Running terminal command: {' '.join(terminal_cmd)}")
                
                # Execute the command to open a new terminal
                process = subprocess.Popen(
                    terminal_cmd,
                    env=os.environ.copy(),
                    cwd=os.getcwd(),
                    shell=False
                )
            else:  # Linux/Mac
                # For Linux/Mac, use a terminal if available
                cmd_str = " ".join(cmd)
                window_title = f"Call {call_id} - {phone_number} - Agent {agent_id}"
                
                if os.system("which gnome-terminal > /dev/null 2>&1") == 0:
                    terminal_cmd = [
                        "gnome-terminal", 
                        "--title", window_title,
                        "--", 
                        "bash", "-c", f"{cmd_str}; exec bash"
                    ]
                    process = subprocess.Popen(terminal_cmd, shell=False)
                elif os.system("which xterm > /dev/null 2>&1") == 0:
                    terminal_cmd = [
                        "xterm", 
                        "-title", window_title,
                        "-e", 
                        f"{cmd_str}; exec bash"
                    ]
                    process = subprocess.Popen(terminal_cmd, shell=False)
                else:
                    # Fallback to running without a terminal
                    logger.warning("No terminal emulator found, running without visible terminal")
                    process = subprocess.Popen(
                        cmd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        env=os.environ.copy(),
                        cwd=os.getcwd()
                    )
                    
                    # Start threads to read output and error if we're not using a terminal
                    stdout_thread = threading.Thread(
                        target=read_process_output,
                        args=(process, call_process.logs, f"Call {call_id}"),
                        daemon=True
                    )
                    stderr_thread = threading.Thread(
                        target=read_process_error,
                        args=(process, call_process.logs, f"Call {call_id}"),
                        daemon=True
                    )
                    
                    stdout_thread.start()
                    stderr_thread.start()
            
            # Store the process and update status
            call_process.process = process
            call_process.status = "running"
            call_process.logs.append(f"Started call to {phone_number} using agent {agent_id}")
            
            # Add to call processes
            call_processes[call_id] = call_process
            
            logger.info(f"Call {call_id} started successfully")
            
            return jsonify({
                "callId": call_id,
                "phoneNumber": phone_number,
                "agentId": agent_id,
                "status": "running",
                "startTime": call_process.start_time.isoformat()
            })
            
        except Exception as e:
            error_msg = f"Error starting call {call_id}: {str(e)}"
            call_process.status = "failed"
            call_process.logs.append(error_msg)
            logger.error(error_msg)
            return jsonify({"error": error_msg}), 500
        
    except Exception as e:
        logger.error(f"Error starting call: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/calls/<call_id>', methods=['GET'])
def get_call(call_id):
    """Get details of a specific call"""
    try:
        call_data = call_processes.get(call_id)
        if not call_data:
            return jsonify({"error": "Call not found"}), 404
        
        return jsonify({
            "callId": call_id,
            "phoneNumber": call_data.phone_number,
            "agentId": call_data.agent_id,
            "status": call_data.status,
            "startTime": call_data.start_time.isoformat(),
            "logs": call_data.logs
        })
    except Exception as e:
        logger.error(f"Error getting call {call_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/calls', methods=['GET'])
def list_calls():
    """List all calls"""
    try:
        calls = []
        
        for call_id, call_data in call_processes.items():
            calls.append({
                "callId": call_id,
                "phoneNumber": call_data.phone_number,
                "agentId": call_data.agent_id,
                "status": call_data.status,
                "startTime": call_data.start_time.isoformat()
            })
        
        return jsonify(calls)
    except Exception as e:
        logger.error(f"Error listing calls: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "runningAgents": len(running_agents),
        "activeCalls": len(call_processes)
    })

@app.route('/api/voices', methods=['GET'])
def list_voices():
    """Get list of available voices"""
    return get_available_voices()

@app.route('/api/models', methods=['GET'])
def list_models():
    """List available models"""
    models = [
        {"id": "llama-3.3-70b-versatile", "name": "Llama 3.3 70B", "description": "High performance model"},
        {"id": "llama-3.1-8b", "name": "Llama 3.1 8B", "description": "Fast, efficient model"},
        {"id": "mixtral-8x7b", "name": "Mixtral 8x7B", "description": "Powerful mixture-of-experts model"}
    ]
    return jsonify(models)

@app.route('/api/models/groq', methods=['GET'])
def get_groq_models():
    """Fetch available Groq models"""
    try:
        # Initialize Groq client
        groq_api_key = os.getenv('GROQ_API_KEY')
        if not groq_api_key:
            return jsonify({'error': 'GROQ_API_KEY not found in environment variables'}), 500
        
        client = Groq(api_key=groq_api_key)
        
        # Fetch models
        models_response = client.models.list()
        models = []
        
        for model in models_response.data:
            models.append({
                'id': model.id,
                'object': model.object,
                'created': model.created,
                'owned_by': model.owned_by,
                'context_window': getattr(model, 'context_window', None)
            })
        
        return jsonify({'models': models})
    except Exception as e:
        logger.error(f"Error fetching Groq models: {str(e)}")
        return jsonify({'error': f'Failed to fetch Groq models: {str(e)}'}), 500

@app.route('/api/livekit/spawn', methods=['POST'])
def spawn_livekit_worker():
    """Dispatch a LiveKit room for the agent (using existing LiveKit dispatch system)"""
    try:
        data = request.json
        agent_id = data.get('agent_id')
        
        if not agent_id:
            return jsonify({"error": "agent_id is required"}), 400
            
        # Check if agent exists and is running
        agent_data = running_agents.get(agent_id)
        if not agent_data:
            return jsonify({"error": f"Agent ID '{agent_id}' not found or not running"}), 400
        
        # Use the LiveKit dispatch system to create a room for web calling
        # This creates a room that the agent can join, similar to phone calls but for web
        room_name = f"agent_room_{agent_id}"
        
        cmd = [
            "lk", "dispatch", "create",
            "--room", room_name,
            "--agent-name", agent_data.agent_name,
            "--metadata", f"web_call_{agent_id}"
        ]
        
        process_cwd = os.path.dirname(__file__)
        
        try:
            logger.info(f"Creating LiveKit room for web calling: {' '.join(cmd)}")
            result = subprocess.run(cmd, cwd=process_cwd, capture_output=True, text=True, check=False)
            
            if result.returncode == 0:
                logger.info(f"LiveKit room created successfully for agent {agent_data.agent_name}")
                return jsonify({
                    "message": f"LiveKit room ready for agent '{agent_data.agent_name}'",
                    "agent_id": agent_id,
                    "agent_name": agent_data.agent_name,
                    "room_name": room_name,
                    "status": "running"
                }), 200
            else:
                logger.error(f"Failed to create LiveKit room. Error: {result.stderr}")
                return jsonify({
                    "error": f"Failed to create LiveKit room: {result.stderr.strip()}"
                }), 500
                
        except FileNotFoundError:
            logger.error("lk command not found. Ensure LiveKit CLI is installed.")
            return jsonify({"error": "LiveKit CLI not found. Please ensure it's installed and in PATH."}), 500
        
    except Exception as e:
        logger.error(f"Error managing LiveKit room: {str(e)}", exc_info=True)
        return jsonify({"error": f"Failed to manage LiveKit room: {str(e)}"}), 500



@app.route('/api/tts/test', methods=['POST'])
def test_tts_endpoint():
    """Test TTS with provided text and voice"""
    data = request.json
    if not data:
        return jsonify({"error": "No data provided"}), 400
        
    text = data.get('text')
    voice = data.get('voice', 'tara')
    
    return test_tts(text, voice)

@app.route('/api/tts/generate', methods=['POST'])
def generate_tts_endpoint():
    """Generate TTS audio for agents and return audio data"""
    data = request.json
    if not data:
        return jsonify({"error": "No data provided"}), 400
        
    text = data.get('text')
    voice = data.get('voice', 'tara')
    
    return generate_tts(text, voice)

@app.route('/api/telnyx/numbers', methods=['GET'])
def get_telnyx_numbers():
    """Get available Telnyx phone numbers with SIP trunk mapping"""
    try:
        # Fetch available phone numbers from Telnyx
        phone_numbers = telnyx.PhoneNumber.list()
        
        numbers_data = []
        for number in phone_numbers.data:
            if number.status == 'active':
                numbers_data.append({
                    'phone_number': number.phone_number,
                    'connection_id': number.connection_id,
                    'messaging_profile_id': getattr(number, 'messaging_profile_id', None)
                })
        
        # Auto-create SIP trunks for new numbers and cleanup removed ones
        created_trunks = auto_create_sip_trunks_for_numbers(numbers_data)
        removed_numbers = cleanup_removed_numbers(numbers_data)
        
        # Build the response with SIP trunk information
        numbers = []
        for number in phone_numbers.data:
            if number.status == 'active':
                phone_num = number.phone_number
                sip_trunk_id = sip_trunk_mapping.get(phone_num, DEFAULT_SIP_TRUNK_ID)
                
                numbers.append({
                    'id': number.id,
                    'phone_number': phone_num,
                    'connection_id': number.connection_id,
                    'messaging_profile_id': getattr(number, 'messaging_profile_id', None),
                    'sip_trunk_id': sip_trunk_id,
                    'has_custom_trunk': phone_num in sip_trunk_mapping
                })
        
        return jsonify({
            'numbers': numbers,
            'count': len(numbers),
            'default_trunk_id': DEFAULT_SIP_TRUNK_ID,
            'auto_created_trunks': created_trunks,
            'removed_numbers': removed_numbers
        })
        
    except Exception as e:
        logger.error(f"Error fetching Telnyx numbers: {str(e)}")
        # Return mock data if Telnyx API fails
        mock_numbers = [
            {
                'id': '1', 
                'phone_number': '+1234567890', 
                'connection_id': 'conn_1',
                'sip_trunk_id': DEFAULT_SIP_TRUNK_ID,
                'has_custom_trunk': False
            },
            {
                'id': '2', 
                'phone_number': '+1234567891', 
                'connection_id': 'conn_2',
                'sip_trunk_id': DEFAULT_SIP_TRUNK_ID,
                'has_custom_trunk': False
            },
        ]
        return jsonify({
            'numbers': mock_numbers,
            'count': len(mock_numbers),
            'mock': True,
            'error': f'Using mock data: {str(e)}',
            'auto_created_trunks': []
        })

@app.route('/api/campaigns', methods=['GET'])
def get_campaigns():
    """Get all campaigns"""
    try:
        campaigns_list = []
        for campaign_id, campaign_data in campaigns_storage.items():
            campaigns_list.append({
                'id': campaign_id,
                'name': campaign_data.get('name', ''),
                'status': campaign_data.get('status', 'draft'),
                'telnyxNumber': campaign_data.get('telnyx_number', ''),
                'agentId': campaign_data.get('agent_id', ''),
                'agentName': campaign_data.get('agent_name', ''),
                'agentVoice': campaign_data.get('agent_voice', ''),
                'firstMessage': campaign_data.get('first_message', ''),
                'createdAt': campaign_data.get('created_at', ''),
                'contactsCount': len(campaign_data.get('contacts', []))
            })
        
        return jsonify(campaigns_list)
        
    except Exception as e:
        logger.error(f"Error fetching campaigns: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/campaigns', methods=['POST'])
def create_campaign():
    """Create a new campaign"""
    try:
        data = request.json
        
        # Generate campaign ID
        campaign_id = f"campaign_{int(datetime.now().timestamp())}"
        
        # Validate required fields
        required_fields = ['name', 'agentId']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        # Validate telnyxNumbers (plural) - at least one phone number is required
        if not data.get('telnyxNumbers') or len(data.get('telnyxNumbers', [])) == 0:
            return jsonify({'error': 'Missing required field: telnyxNumbers - at least one phone number is required'}), 400
        
        # Get agent data
        agent_id = data.get('agentId')
        agent_data = agents_storage.get(agent_id)
        if not agent_data:
            return jsonify({'error': f'Agent not found: {agent_id}'}), 400
        
        # Create campaign using agent data
        campaign = {
            'id': campaign_id,
            'name': data.get('name'),
            'status': 'draft',
            'telnyx_numbers': data.get('telnyxNumbers', []),  # Multiple numbers instead of single
            'agent_id': agent_id,
            'agent_name': agent_data.get('name'),
            'first_message': data.get('firstMessage', ''),
            'created_at': datetime.now().isoformat(),
            'contacts': data.get('contacts', []),
            # Store agent settings for easy access
            'agent_voice': agent_data.get('voice_id'),
            'agent_model': agent_data.get('model_id'),
            'agent_system_prompt': agent_data.get('system_prompt'),
            'agent_emotional_prompt': agent_data.get('emotional_prompt')
        }
        
        campaigns_storage[campaign_id] = campaign
        
        # Save to persistent storage
        save_json_data(CAMPAIGNS_FILE, campaigns_storage)
        
        return jsonify({
            'message': 'Campaign created successfully',
            'campaign': {
                'id': campaign_id,
                'name': campaign['name'],
                'status': campaign['status'],
                'contactsCount': len(campaign['contacts'])
            }
        })
        
    except Exception as e:
        logger.error(f"Error creating campaign: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/campaigns/<campaign_id>', methods=['PUT'])
def update_campaign(campaign_id):
    """Update an existing campaign"""
    try:
        if campaign_id not in campaigns_storage:
            return jsonify({'error': 'Campaign not found'}), 404
        
        data = request.json
        campaign = campaigns_storage[campaign_id]
        
        # Update campaign fields
        updatable_fields = [
            'name', 'telnyx_numbers', 'system_message', 'first_message',
            'ai_provider', 'voice', 'custom_voice_id', 'language', 'contacts', 'agent_id'
        ]
        
        for field in updatable_fields:
            if field in data:
                if field == 'telnyxNumbers':
                    campaign['telnyx_numbers'] = data[field]
                elif field == 'systemMessage':
                    campaign['system_message'] = data[field]
                elif field == 'firstMessage':
                    campaign['first_message'] = data[field]
                elif field == 'aiProvider':
                    campaign['ai_provider'] = data[field]
                elif field == 'customVoiceId':
                    campaign['custom_voice_id'] = data[field]
                elif field == 'agentId':
                    campaign['agent_id'] = data[field]
                else:
                    campaign[field] = data[field]
        
        campaign['updated_at'] = datetime.now().isoformat()
        
        return jsonify({
            'message': 'Campaign updated successfully',
            'campaign': {
                'id': campaign_id,
                'name': campaign['name'],
                'status': campaign['status']
            }
        })
        
    except Exception as e:
        logger.error(f"Error updating campaign: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/campaigns/<campaign_id>', methods=['DELETE'])
def delete_campaign(campaign_id):
    """Delete a campaign"""
    try:
        if campaign_id not in campaigns_storage:
            return jsonify({'error': 'Campaign not found'}), 404
        
        del campaigns_storage[campaign_id]
        
        return jsonify({'message': 'Campaign deleted successfully'})
        
    except Exception as e:
        logger.error(f"Error deleting campaign: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/campaigns/<campaign_id>/start', methods=['POST'])
def start_campaign(campaign_id):
    """Start a campaign with enhanced concurrent calling"""
    try:
        if campaign_id not in campaigns_storage:
            return jsonify({'error': 'Campaign not found'}), 404
        
        campaign = campaigns_storage[campaign_id]
        
        if not campaign.get('contacts'):
            return jsonify({'error': 'No contacts in campaign'}), 400
        
        # Check if campaign is already running
        if campaign.get('status') == 'running':
            return jsonify({'error': 'Campaign is already running'}), 400
        
        campaign['status'] = 'running'
        campaign['started_at'] = datetime.now().isoformat()
        
        # Save campaign status immediately
        save_json_data(CAMPAIGNS_FILE, campaigns_storage)
        
        # Actually start calling the contacts in the campaign concurrently
        logger.info(f"🚀 Starting campaign {campaign_id} with {len(campaign['contacts'])} contacts - ENHANCED CONCURRENT CALLING")
        
        # Get the agent data once
        agent_data = agents_storage.get(campaign['agent_id'])
        if not agent_data:
            campaign['status'] = 'failed'
            campaign['error'] = f'Agent {campaign["agent_id"]} not found'
            save_json_data(CAMPAIGNS_FILE, campaigns_storage)
            return jsonify({'error': f'Agent {campaign["agent_id"]} not found'}), 404
        
        # Enhanced thread-safe call statistics
        import threading
        from collections import defaultdict
        
        call_results = {
            'successful': 0,
            'failed': 0,
            'total': len(campaign['contacts']),
            'errors': [],
            'completed_contacts': []
        }
        results_lock = threading.Lock()
        
        def check_call_status_later(call_id, contact_name):
            """Check call status after delay to see if call was actually answered"""
            try:
                # Wait additional time for call to be established and potentially answered
                import time
                time.sleep(30)  # Additional 30 seconds for call to be picked up
                
                # Try to get call status from Telnyx or LiveKit
                try:
                    # Check if we can find this call in Telnyx
                    import telnyx
                    calls = telnyx.Call.list(limit=50)
                    
                    for call in calls.data:
                        # Try to match call by timing and number
                        call_time = call.get('created_at', '')
                        call_status = call.get('status', '')
                        call_answer_time = call.get('answered_at')
                        
                        if call_answer_time and call_status in ['completed', 'answered']:
                            # Call was answered - update status
                            logger.info(f"📞 ✅ Call {call_id} for {contact_name} was ANSWERED - updating status")
                            
                            # Find and update the call log
                            for i, log in enumerate(call_logs):
                                if log.get('call_id') == call_id or (
                                    log.get('contact_name') == contact_name and 
                                    log.get('status') == 'initiated'
                                ):
                                    call_logs[i]['status'] = 'answered'
                                    call_logs[i]['answered_at'] = call.get('answered_at')
                                    call_logs[i]['call_duration'] = call.get('duration', 0)
                                    call_logs[i]['telnyx_call_id'] = call.get('id')
                                    save_json_data(CALL_LOGS_FILE, call_logs)
                                    logger.info(f"📋 Updated call log: {contact_name} - ANSWERED")
                                    break
                            return
                    
                    # If we get here, call was not answered
                    logger.info(f"📞 ❌ Call {call_id} for {contact_name} was NOT answered - updating status")
                    
                    # Update call status to not answered
                    for i, log in enumerate(call_logs):
                        if log.get('call_id') == call_id or (
                            log.get('contact_name') == contact_name and 
                            log.get('status') == 'initiated'
                        ):
                            call_logs[i]['status'] = 'no_answer'
                            call_logs[i]['checked_at'] = datetime.now().isoformat()
                            save_json_data(CALL_LOGS_FILE, call_logs)
                            logger.info(f"📋 Updated call log: {contact_name} - NO ANSWER")
                            break
                            
                except Exception as telnyx_error:
                    logger.warning(f"⚠️ Could not check Telnyx status for {contact_name}: {str(telnyx_error)}")
                    # Default to no_answer if we can't verify
                    for i, log in enumerate(call_logs):
                        if log.get('call_id') == call_id or (
                            log.get('contact_name') == contact_name and 
                            log.get('status') == 'initiated'
                        ):
                            call_logs[i]['status'] = 'no_answer'
                            call_logs[i]['checked_at'] = datetime.now().isoformat()
                            call_logs[i]['check_error'] = str(telnyx_error)
                            save_json_data(CALL_LOGS_FILE, call_logs)
                            break
                    
            except Exception as e:
                logger.error(f"❌ Error checking call status for {contact_name}: {str(e)}")

        def make_single_call(contact, campaign_phone_numbers, contact_index):
            """Enhanced single call function with better error handling and resource management"""
            thread_id = f"T{contact_index}"
            try:
                contact_name = contact.get('name', 'Customer')
                contact_phone = contact.get('phone_number')
                contact_notes = contact.get('notes', '')
                
                logger.info(f"📞 {thread_id}: Starting call to {contact_name} at {contact_phone}")
                
                # Validate phone number
                if not contact_phone or len(contact_phone) < 10:
                    error_msg = f"Invalid phone number: {contact_phone}"
                    logger.error(f"❌ {thread_id}: {error_msg}")
                    with results_lock:
                        call_results['failed'] += 1
                        call_results['errors'].append(f"{contact_name}: {error_msg}")
                    return
                
                # Distribute calls across multiple campaign phone numbers
                selected_phone = campaign_phone_numbers[contact_index % len(campaign_phone_numbers)]
                
                logger.info(f"📞 {thread_id}: Using outbound number {selected_phone} for {contact_name}")
                
                # Prepare enhanced call metadata
                call_metadata = {
                    'target_phone': contact_phone,
                    'from_phone': selected_phone,
                    'contact_name': contact_name,
                    'contact_notes': contact_notes,
                    'agent_name': agent_data['name'],
                    'agent_voice': agent_data.get('voice_id', 'Tara'),
                    'agent_model': agent_data.get('model_id', 'llama-3.3-70b-versatile'),
                    'system_prompt': agent_data.get('system_prompt', ''),
                    'emotional_prompt': agent_data.get('emotional_prompt', DEFAULT_EMOTIONAL_PROMPT),
                    'campaign_id': campaign_id,
                    'first_message': campaign.get('first_message', ''),
                    'thread_id': thread_id,
                    'contact_index': contact_index
                }
                
                # Create personalized system prompt for this specific call
                base_prompt = agent_data.get('system_prompt', '')
                emotional_prompt = agent_data.get('emotional_prompt', DEFAULT_EMOTIONAL_PROMPT)
                
                personalized_prompt = f"{emotional_prompt}\n\n{base_prompt}\n\nYou are speaking with {contact_name}."
                if contact_notes:
                    personalized_prompt += f" Additional context: {contact_notes}"
                
                # Create a unique temporary agent for this specific call
                import time
                import random
                timestamp = int(time.time())
                random_id = random.randint(1000, 9999)
                clean_contact_name = contact_name.replace(' ', '_').replace('.', '').replace('-', '_')[:10]
                temp_agent_name = f"{agent_data['name']}_{clean_contact_name}_{contact_index}_{timestamp}_{random_id}"
                
                logger.info(f"🤖 {thread_id}: Creating unique agent '{temp_agent_name}' for {contact_name}")
                
                # Start dynamic agent for this call with timeout
                try:
                    result = start_dynamic_agent_internal(
                        agent_name=temp_agent_name,
                        llm_prompt=personalized_prompt,
                        agent_type='phone'
                    )
                    
                    if not result.get('success'):
                        error_msg = f"Failed to create agent: {result.get('error', 'Unknown error')}"
                        logger.error(f"❌ {thread_id}: {error_msg}")
                        with results_lock:
                            call_results['failed'] += 1
                            call_results['errors'].append(f"{contact_name}: {error_msg}")
                        return
                    
                    # Brief wait for agent to initialize
                    time.sleep(0.5)
                    
                    # Make the actual call using existing LiveKit system
                    logger.info(f"📞 {thread_id}: Dispatching LiveKit call for {contact_name}")
                    
                    call_data = {
                        'agent_name_for_dispatch': temp_agent_name,
                        'phone_number': contact_phone,
                        'agent_id': result.get('agent_id'),
                        'from_phone': selected_phone,
                        'sip_trunk_id': sip_trunk_mapping.get(selected_phone, DEFAULT_SIP_TRUNK_ID),
                        'campaign_id': campaign_id,
                        'call_id': f"call_{campaign_id}_{contact_index}_{timestamp}_{random_id}",
                        'contact_name': contact_name
                    }
                    
                    # Make the call request with extended timeout and retry logic
                    import requests
                    max_retries = 2
                    retry_delay = 1
                    
                    for attempt in range(max_retries):
                        try:
                            logger.info(f"🌐 {thread_id}: Making HTTP request (attempt {attempt + 1}/{max_retries})")
                            response = requests.post(
                                'http://localhost:9090/make_call', 
                                json=call_data, 
                                timeout=10  # Increased timeout
                            )
                            
                            if response.status_code == 200:
                                logger.info(f"✅ {thread_id}: LIVE CALL INITIATED successfully for {contact_name}")
                                
                                # Log successful call initiation (not answered yet)
                                call_id = log_call({
                                    'contact_name': contact_name,
                                    'contact_phone': contact_phone,
                                    'from_phone': selected_phone,
                                    'agent_name': agent_data['name'],
                                    'campaign_id': campaign_id,
                                    'status': 'initiated',  # Just initiated, not answered
                                    'contact_notes': contact_notes,
                                    'agent_instance': temp_agent_name,
                                    'call_id': call_data['call_id']
                                })
                                
                                                                 # Call status will be updated via webhook - no need for delayed checking
                                
                                with results_lock:
                                    call_results['successful'] += 1
                                    call_results['completed_contacts'].append({
                                        'name': contact_name,
                                        'phone': contact_phone,
                                        'status': 'initiated',  # Call dispatched, waiting for answer
                                        'agent': temp_agent_name,
                                        'call_id': call_data['call_id']
                                    })
                                
                                # Let call establish naturally - no blocking wait
                                logger.info(f"⏳ {thread_id}: Call dispatched successfully, will check status later")
                                return
                                
                            else:
                                error_msg = f"HTTP {response.status_code}: {response.text[:200]}"
                                logger.warning(f"⚠️ {thread_id}: Call attempt {attempt + 1} failed: {error_msg}")
                                
                                if attempt < max_retries - 1:
                                    time.sleep(retry_delay)
                                    retry_delay *= 2  # Exponential backoff
                                else:
                                    # Final failure
                                    logger.error(f"❌ {thread_id}: All call attempts failed for {contact_name}")
                                    
                                    log_call({
                                        'contact_name': contact_name,
                                        'contact_phone': contact_phone,
                                        'from_phone': selected_phone,
                                        'agent_name': agent_data['name'],
                                        'campaign_id': campaign_id,
                                        'status': 'failed',
                                        'contact_notes': contact_notes,
                                        'error': error_msg
                                    })
                                    
                                    with results_lock:
                                        call_results['failed'] += 1
                                        call_results['errors'].append(f"{contact_name}: {error_msg}")
                                        call_results['completed_contacts'].append({
                                            'name': contact_name,
                                            'phone': contact_phone,
                                            'status': 'failed',
                                            'error': error_msg
                                        })
                        
                        except requests.exceptions.Timeout:
                            error_msg = "Request timeout"
                            logger.warning(f"⚠️ {thread_id}: Attempt {attempt + 1} timed out")
                            if attempt == max_retries - 1:
                                with results_lock:
                                    call_results['failed'] += 1
                                    call_results['errors'].append(f"{contact_name}: {error_msg}")
                        
                        except requests.exceptions.RequestException as req_error:
                            error_msg = f"Request error: {str(req_error)}"
                            logger.warning(f"⚠️ {thread_id}: Attempt {attempt + 1} failed: {error_msg}")
                            if attempt == max_retries - 1:
                                with results_lock:
                                    call_results['failed'] += 1
                                    call_results['errors'].append(f"{contact_name}: {error_msg}")
                
                except Exception as agent_error:
                    error_msg = f"Agent creation error: {str(agent_error)}"
                    logger.error(f"❌ {thread_id}: {error_msg}")
                    with results_lock:
                        call_results['failed'] += 1
                        call_results['errors'].append(f"{contact_name}: {error_msg}")
        
            except Exception as e:
                error_msg = f"Unexpected error: {str(e)}"
                logger.error(f"❌ {thread_id}: {error_msg}", exc_info=True)
                with results_lock:
                    call_results['failed'] += 1
                    call_results['errors'].append(f"{contact.get('name', 'Unknown')}: {error_msg}")
        
        def process_campaign_calls():
            """Enhanced campaign processing with better monitoring and resource management"""
            try:
                campaign_phone_numbers = campaign.get('telnyx_numbers', [])
                
                if not campaign_phone_numbers:
                    error_msg = 'No phone numbers configured'
                    logger.error(f"❌ Campaign {campaign_id}: {error_msg}")
                    campaign['status'] = 'failed'
                    campaign['error'] = error_msg
                    save_json_data(CAMPAIGNS_FILE, campaigns_storage)
                    return
                
                logger.info(f"📞 Campaign {campaign_id}: Using {len(campaign_phone_numbers)} phone numbers: {', '.join(campaign_phone_numbers)}")
                
                # Create and start all threads
                threads = []
                for index, contact in enumerate(campaign['contacts']):
                    thread = threading.Thread(
                        target=make_single_call, 
                        args=(contact, campaign_phone_numbers, index),
                        name=f"CallThread-{index}-{contact.get('name', 'Unknown')}"
                    )
                    thread.daemon = False  # Ensure threads can complete
                    threads.append(thread)
                
                # Start all calls simultaneously
                start_time = time.time()
                logger.info(f"🚀 Campaign {campaign_id}: Starting {len(threads)} concurrent calls")
                
                for i, thread in enumerate(threads):
                    thread.start()
                    logger.info(f"🧵 Started thread {i}: {thread.name}")
                
                # Monitor thread completion with enhanced timeout
                timeout_per_call = 45  # Increased timeout per call
                total_timeout = timeout_per_call
                
                logger.info(f"⏳ Campaign {campaign_id}: Waiting for all calls to complete (timeout: {total_timeout}s)")
                
                # Wait for all threads with individual monitoring
                for i, thread in enumerate(threads):
                    remaining_timeout = max(0, total_timeout - (time.time() - start_time))
                    if remaining_timeout > 0:
                        thread.join(timeout=remaining_timeout)
                        
                        if thread.is_alive():
                            logger.warning(f"⚠️ Thread {i} ({thread.name}) still running after timeout")
                        else:
                            logger.info(f"✅ Thread {i} ({thread.name}) completed")
                    else:
                        logger.warning(f"⚠️ Thread {i} ({thread.name}) skipped due to overall timeout")
                
                # Final campaign status update
                total_time = time.time() - start_time
                campaign['status'] = 'completed'
                campaign['completed_at'] = datetime.now().isoformat()
                campaign['total_duration'] = round(total_time, 2)
                campaign['call_results'] = call_results
                
                logger.info(f"✅ Campaign {campaign_id} completed in {total_time:.1f}s:")
                logger.info(f"   📊 Successful: {call_results['successful']}/{call_results['total']}")
                logger.info(f"   📊 Failed: {call_results['failed']}/{call_results['total']}")
                
                if call_results['errors']:
                    logger.warning(f"   ⚠️ Errors encountered:")
                    for error in call_results['errors'][:5]:  # Show first 5 errors
                        logger.warning(f"     - {error}")
                
                # Save final campaign status
                save_json_data(CAMPAIGNS_FILE, campaigns_storage)
                
            except Exception as e:
                error_msg = f"Campaign processing error: {str(e)}"
                logger.error(f"❌ Campaign {campaign_id}: {error_msg}", exc_info=True)
                campaign['status'] = 'failed'
                campaign['error'] = error_msg
                campaign['completed_at'] = datetime.now().isoformat()
                save_json_data(CAMPAIGNS_FILE, campaigns_storage)
        
        # Start the enhanced concurrent calling process
        calling_thread = threading.Thread(target=process_campaign_calls, name=f"CampaignThread-{campaign_id}")
        calling_thread.daemon = False
        calling_thread.start()
        
        return jsonify({
            'message': 'Enhanced campaign started successfully - calls being initiated concurrently',
            'campaign': {
                'id': campaign_id,
                'status': campaign['status'],
                'contactsCount': len(campaign['contacts']),
                'phoneNumbers': campaign.get('telnyx_numbers', []),
                'features': [
                    'Concurrent calling',
                    'Unique agent per call',
                    'Distributed phone numbers',
                    'Enhanced error handling',
                    'Call retry logic'
                ]
            }
        })
        
    except Exception as e:
        logger.error(f"Error starting enhanced campaign: {str(e)}", exc_info=True)
        return jsonify({'error': str(e)}), 500

@app.route('/api/campaigns/<campaign_id>/stop', methods=['POST'])
def stop_campaign(campaign_id):
    """Stop a campaign"""
    try:
        if campaign_id not in campaigns_storage:
            return jsonify({'error': 'Campaign not found'}), 404
        
        campaign = campaigns_storage[campaign_id]
        campaign['status'] = 'stopped'
        campaign['stopped_at'] = datetime.now().isoformat()
        
        return jsonify({
            'message': 'Campaign stopped successfully',
            'campaign': {
                'id': campaign_id,
                'status': campaign['status']
            }
        })
        
    except Exception as e:
        logger.error(f"Error stopping campaign: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sip-trunks', methods=['GET'])
def get_sip_trunk_mapping():
    """Get the current SIP trunk mapping"""
    return jsonify({
        'mapping': sip_trunk_mapping,
        'default_trunk_id': DEFAULT_SIP_TRUNK_ID
    })

@app.route('/api/sip-trunks', methods=['POST'])
def update_sip_trunk_mapping():
    """Update SIP trunk mapping for a phone number"""
    try:
        data = request.json
        phone_number = data.get('phone_number')
        sip_trunk_id = data.get('sip_trunk_id')
        
        if not phone_number or not sip_trunk_id:
            return jsonify({'error': 'phone_number and sip_trunk_id are required'}), 400
        
        sip_trunk_mapping[phone_number] = sip_trunk_id
        
        return jsonify({
            'message': 'SIP trunk mapping updated successfully',
            'phone_number': phone_number,
            'sip_trunk_id': sip_trunk_id
        })
        
    except Exception as e:
        logger.error(f"Error updating SIP trunk mapping: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sip-trunks/auto-create', methods=['POST'])
def auto_create_sip_trunk():
    """Automatically create SIP trunk and update mapping"""
    try:
        data = request.json
        phone_number = data.get('phone_number')
        trunk_config = data.get('trunk_config')
        
        if not phone_number or not trunk_config:
            return jsonify({'error': 'phone_number and trunk_config are required'}), 400
        
        logger.info(f"🚀 Auto-creating SIP trunk for {phone_number}")
        
        # Create temporary trunk config file
        import tempfile
        import os
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(trunk_config, f, indent=2)
            temp_file = f.name
        
        try:
            # Run lk sip outbound create command
            cmd = ["lk", "sip", "outbound", "create", temp_file]
            logger.info(f"Running command: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=False)
            
            if result.returncode == 0:
                # Parse the output to get SIP trunk ID
                output = result.stdout.strip()
                logger.info(f"LiveKit SIP trunk creation output: {output}")
                
                # Try to extract SIP trunk ID from output
                sip_trunk_id = None
                try:
                    # Parse JSON output if possible
                    output_json = json.loads(output)
                    sip_trunk_id = output_json.get('sipTrunkId') or output_json.get('id')
                except json.JSONDecodeError:
                    # Try to extract from text output
                    import re
                    match = re.search(r'ST_[a-zA-Z0-9]+', output)
                    if match:
                        sip_trunk_id = match.group(0)
                
                if sip_trunk_id:
                    # Update the SIP trunk mapping
                    sip_trunk_mapping[phone_number] = sip_trunk_id
                    
                    # Save to persistent storage
                    save_json_data(SIP_TRUNKS_FILE, sip_trunk_mapping)
                    
                    logger.info(f"✅ Successfully created SIP trunk {sip_trunk_id} for {phone_number}")
                    
                    return jsonify({
                        'success': True,
                        'message': f'SIP trunk created successfully for {phone_number}',
                        'phone_number': phone_number,
                        'sip_trunk_id': sip_trunk_id,
                        'livekit_output': output
                    })
                else:
                    logger.error(f"Could not extract SIP trunk ID from output: {output}")
                    return jsonify({
                        'success': False,
                        'error': 'Could not extract SIP trunk ID from LiveKit output',
                        'livekit_output': output
                    }), 500
            else:
                logger.error(f"LiveKit command failed: {result.stderr}")
                return jsonify({
                    'success': False,
                    'error': 'Failed to create SIP trunk in LiveKit',
                    'livekit_error': result.stderr,
                    'livekit_output': result.stdout
                }), 500
                
        finally:
            # Clean up temporary file
            if os.path.exists(temp_file):
                os.unlink(temp_file)
        
    except Exception as e:
        logger.error(f"Error auto-creating SIP trunk: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/campaigns/<campaign_id>/call', methods=['POST'])
def start_campaign_call(campaign_id):
    """Start a call for a specific campaign using LiveKit dispatch"""
    try:
        if campaign_id not in campaigns_storage:
            return jsonify({'error': 'Campaign not found'}), 404
        
        data = request.json
        target_phone = data.get('target_phone')
        
        if not target_phone:
            return jsonify({'error': 'target_phone is required'}), 400
        
        campaign = campaigns_storage[campaign_id]
        campaign_phone = campaign.get('telnyx_number')
        
        # Get the appropriate SIP trunk ID for this phone number
        sip_trunk_id = sip_trunk_mapping.get(campaign_phone, DEFAULT_SIP_TRUNK_ID)
        
        # Here you would integrate with your LiveKit calling system
        # Following the pattern from your existing guide:
        
        # 1. Create a new room
        # 2. Dispatch agent with the correct SIP trunk ID
        # 3. Use the campaign's system message and voice settings
        
        call_data = {
            'campaign_id': campaign_id,
            'target_phone': target_phone,
            'from_phone': campaign_phone,
            'sip_trunk_id': sip_trunk_id,
            'system_message': campaign.get('system_message'),
            'first_message': campaign.get('first_message'),
            'voice': campaign.get('voice'),
            'ai_provider': campaign.get('ai_provider'),
            'status': 'initiated'
        }
        
        logger.info(f"Campaign call initiated: {call_data}")
        
        # TODO: Integrate with your LiveKit dispatch system here
        # lk dispatch create --new-room --agent-name outbound-caller --metadata target_phone
        
        return jsonify({
            'message': 'Call initiated successfully',
            'call_data': call_data
        })
        
    except Exception as e:
        logger.error(f"Error starting campaign call: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/livekit/dispatch', methods=['POST'])
def dispatch_livekit_call():
    """Dispatch a LiveKit outbound call with specific SIP trunk"""
    try:
        data = request.json
        
        required_fields = ['target_phone', 'from_phone', 'sip_trunk_id']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400
        
        target_phone = data['target_phone']
        from_phone = data['from_phone']
        sip_trunk_id = data['sip_trunk_id']
        
        # Additional call parameters
        system_message = data.get('system_message', 'You are a helpful AI assistant.')
        voice = data.get('voice', 'Tara')
        agent_name = data.get('agent_name', 'outbound-caller')
        
        # Create call metadata
        call_metadata = {
            'target_phone': target_phone,
            'from_phone': from_phone,
            'sip_trunk_id': sip_trunk_id,
            'system_message': system_message,
            'voice': voice
        }
        
        # TODO: Here you would call your LiveKit CLI or use LiveKit SDK
        # Example command that would be executed:
        # lk dispatch create --new-room --agent-name {agent_name} --metadata '{json.dumps(call_metadata)}'
        
        logger.info(f"LiveKit dispatch requested: {call_metadata}")
        
        return jsonify({
            'message': 'LiveKit call dispatched successfully',
            'call_metadata': call_metadata,
            'status': 'dispatched'
        })
        
    except Exception as e:
        logger.error(f"Error dispatching LiveKit call: {str(e)}")
        return jsonify({'error': str(e)}), 500

def create_sip_trunk_config(phone_number, connection_id):
    """Create SIP trunk configuration for a phone number using your real Telnyx credentials"""
    
    # Create the SIP trunk configuration using your working credentials
    trunk_config = {
        "trunk": {
            "name": f"cbtest_{phone_number.replace('+', '').replace('-', '')}",
            "address": "itscb.sip.telnyx.com",
            "numbers": [phone_number],
            "auth_username": "itscb",
            "auth_password": "Iamcb123"
        }
    }
    
    return None, trunk_config  # We'll get the real SIP trunk ID from LiveKit response

def create_livekit_sip_trunk(phone_number, trunk_config):
    """Create SIP trunk in LiveKit using CLI and return the SIP trunk ID"""
    import tempfile
    import re
    
    try:
        # Create temporary config file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(trunk_config, f, indent=2)
            temp_file = f.name
        
        try:
            # Run lk command (using environment credentials)
            cmd = ["lk", "sip", "outbound", "create", temp_file]
            logger.info(f"🚀 Creating SIP trunk for {phone_number}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=False)
            
            if result.returncode == 0:
                # Extract SIP trunk ID from output
                output = result.stdout.strip()
                logger.info(f"LiveKit output: {output}")
                
                # Try to extract SIP trunk ID
                sip_trunk_id = None
                try:
                    # Parse JSON output if possible
                    output_json = json.loads(output)
                    sip_trunk_id = output_json.get('sipTrunkId') or output_json.get('id')
                except json.JSONDecodeError:
                    # Try to extract from text output (like "SIPTrunkID: ST_...")
                    match = re.search(r'ST_[a-zA-Z0-9]+', output)
                    if match:
                        sip_trunk_id = match.group(0)
                
                if sip_trunk_id:
                    logger.info(f"✅ Successfully created SIP trunk {sip_trunk_id} for {phone_number}")
                    return sip_trunk_id
                else:
                    logger.error(f"Could not extract SIP trunk ID from output: {output}")
                    return None
            else:
                logger.error(f"Failed to create SIP trunk for {phone_number}: {result.stderr}")
                return None
                
        except Exception as e:
            logger.error(f"Error running lk command: {str(e)}")
            return None
            
    except Exception as e:
        logger.error(f"Error creating SIP trunk: {str(e)}")
        return None
    finally:
        # Clean up temp file
        try:
            if os.path.exists(temp_file):
                os.unlink(temp_file)
        except:
            pass

def auto_create_sip_trunks_for_numbers(phone_numbers_data):
    """Automatically create SIP trunks for numbers that don't have them"""
    created_trunks = []
    
    for number_data in phone_numbers_data:
        phone_number = number_data.get('phone_number')
        connection_id = number_data.get('connection_id')
        
        if phone_number and phone_number not in sip_trunk_mapping:
            logger.info(f"🚀 Auto-creating SIP trunk for new number: {phone_number}")
            
            # Create SIP trunk configuration using your real Telnyx credentials
            _, trunk_config = create_sip_trunk_config(phone_number, connection_id)
            
            # Try to create the SIP trunk in LiveKit and get the real SIP trunk ID
            sip_trunk_id = create_livekit_sip_trunk(phone_number, trunk_config)
            
            if sip_trunk_id:
                # Store the mapping
                sip_trunk_mapping[phone_number] = sip_trunk_id
                # Save to persistent storage
                save_json_data(SIP_TRUNKS_FILE, sip_trunk_mapping)
                created_trunks.append({
                    'phone_number': phone_number,
                    'sip_trunk_id': sip_trunk_id,
                    'status': 'created'
                })
                logger.info(f"✅ Auto-created SIP trunk {sip_trunk_id} for {phone_number}")
            else:
                logger.error(f"❌ Failed to create SIP trunk for {phone_number}")
                created_trunks.append({
                    'phone_number': phone_number,
                    'sip_trunk_id': None,
                    'status': 'failed'
                })
    
    return created_trunks

def cleanup_removed_numbers(current_numbers):
    """Remove SIP trunk mappings for numbers that are no longer in Telnyx"""
    removed_numbers = []
    
    # Find numbers in mapping that are no longer in current_numbers
    current_phone_numbers = [num.get('phone_number') for num in current_numbers]
    
    for phone_number in list(sip_trunk_mapping.keys()):
        if phone_number not in current_phone_numbers:
            logger.info(f"🧹 Removing mapping for deleted number: {phone_number}")
            sip_trunk_id = sip_trunk_mapping.pop(phone_number)
            removed_numbers.append({
                'phone_number': phone_number,
                'sip_trunk_id': sip_trunk_id,
                'status': 'removed'
            })
    
    if removed_numbers:
        # Save updated mapping
        save_json_data(SIP_TRUNKS_FILE, sip_trunk_mapping)
        logger.info(f"✅ Cleaned up {len(removed_numbers)} removed numbers from mapping")
    
    return removed_numbers

# Call Logs and Contacts API
@app.route('/api/call-logs', methods=['GET'])
def get_call_logs():
    """Get all call logs with pagination and filtering"""
    try:
        # Get query parameters
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 50))
        campaign_id = request.args.get('campaign_id')
        contact_phone = request.args.get('contact_phone')
        
        # Filter call logs
        filtered_logs = call_logs
        if campaign_id:
            filtered_logs = [log for log in filtered_logs if log.get('campaign_id') == campaign_id]
        if contact_phone:
            filtered_logs = [log for log in filtered_logs if log.get('contact_phone') == contact_phone]
        
        # Sort by timestamp (newest first)
        filtered_logs.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
        
        # Paginate
        start_idx = (page - 1) * limit
        end_idx = start_idx + limit
        paginated_logs = filtered_logs[start_idx:end_idx]
        
        return jsonify({
            'call_logs': paginated_logs,
            'total': len(filtered_logs),
            'page': page,
            'limit': limit,
            'has_more': end_idx < len(filtered_logs)
        })
        
    except Exception as e:
        logger.error(f"Error getting call logs: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/contacts', methods=['GET'])
def get_contacts():
    """Get unique contacts with call history"""
    try:
        # Group calls by contact phone number
        contacts_dict = {}
        
        for log in call_logs:
            phone = log.get('contact_phone', '')
            if phone:
                if phone not in contacts_dict:
                    contacts_dict[phone] = {
                        'phone_number': phone,
                        'name': log.get('contact_name', 'Unknown'),
                        'notes': log.get('notes', ''),
                        'total_calls': 0,
                        'successful_calls': 0,
                        'failed_calls': 0,
                        'last_called': None,
                        'campaigns': set()
                    }
                
                contact = contacts_dict[phone]
                contact['total_calls'] += 1
                
                if log.get('status') == 'initiated':
                    contact['successful_calls'] += 1
                elif log.get('status') == 'failed':
                    contact['failed_calls'] += 1
                
                # Update last called timestamp
                call_time = log.get('timestamp', '')
                if not contact['last_called'] or call_time > contact['last_called']:
                    contact['last_called'] = call_time
                    contact['name'] = log.get('contact_name', contact['name'])  # Update name from latest call
                
                # Track campaigns
                if log.get('campaign_id'):
                    contact['campaigns'].add(log.get('campaign_id'))
        
        # Convert to list and clean up sets
        contacts_list = []
        for contact in contacts_dict.values():
            contact['campaigns'] = list(contact['campaigns'])
            contacts_list.append(contact)
        
        # Sort by last called (newest first)
        contacts_list.sort(key=lambda x: x.get('last_called', ''), reverse=True)
        
        return jsonify({
            'contacts': contacts_list,
            'total': len(contacts_list)
        })
        
    except Exception as e:
        logger.error(f"Error getting contacts: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/contacts/<phone_number>/calls', methods=['GET'])
def get_contact_calls(phone_number):
    """Get call history for a specific contact"""
    try:
        # Find calls for this phone number
        contact_calls = [log for log in call_logs if log.get('contact_phone') == phone_number]
        
        # Sort by timestamp (newest first)
        contact_calls.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
        
        return jsonify({
            'phone_number': phone_number,
            'calls': contact_calls,
            'total': len(contact_calls)
        })
        
    except Exception as e:
        logger.error(f"Error getting calls for contact {phone_number}: {str(e)}")
        return jsonify({'error': str(e)}), 500

# Telnyx Recording API
@app.route('/api/recordings', methods=['GET'])
def get_recordings():
    """Get call recordings from Telnyx"""
    try:
        # Get recordings from Telnyx API
        recordings = telnyx.Recording.list()
        
        recording_list = []
        for recording in recordings.data:
            recording_list.append({
                'id': recording.id,
                'call_leg_id': recording.call_leg_id,
                'call_session_id': recording.call_session_id,
                'recording_started_at': recording.recording_started_at,
                'recording_ended_at': recording.recording_ended_at,
                'duration': recording.duration_millis,
                'file_size': recording.file_size,
                'download_url': recording.download_urls.get('mp3') if recording.download_urls else None,
                'status': recording.status
            })
        
        return jsonify({
            'recordings': recording_list,
            'total': len(recording_list)
        })
        
    except Exception as e:
        logger.error(f"Error getting recordings: {str(e)}")
        return jsonify({'error': str(e)}), 500

# Campaign Statistics and Management Endpoints

@app.route('/api/campaigns/<campaign_id>/statistics', methods=['GET'])
def get_campaign_statistics_endpoint(campaign_id):
    """Get ACCURATE campaign statistics using call log analyzer"""
    try:
        if campaign_id not in campaigns_storage:
            return jsonify({'error': 'Campaign not found'}), 404
        
        # Use the new accurate call log analyzer
        analyzer = CallLogAnalyzer(str(DATA_DIR))
        analysis = analyzer.analyze_campaign(campaign_id)
        
        campaign = campaigns_storage[campaign_id]
        
        if 'error' in analysis:
            # No call logs found - return zero stats
            stats = {
                'totals': {
                    'total_calls': 0,
                    'completed_calls': 0,
                    'failed_calls': 0,
                    'no_answer_calls': 0,
                    'short_calls': 0,
                    'unknown_calls': 0
                },
                'duration': {
                    'total_duration_seconds': 0,
                    'total_duration_minutes': 0,
                    'average_duration_seconds': 0
                },
                'costs': {
                    'total_cost_usd': 0,
                    'average_cost_per_call': 0
                },
                'calls': [],
                'progress_percent': 0,
                'call_statistics': {
                    'total_calls': 0,
                    'completed': 0,
                    'failed': 0,
                    'no_answer': 0
                }
            }
        else:
            # Use accurate analysis results
            call_stats = analysis['call_statistics']
            detailed_outcomes = analysis['detailed_outcomes']
            
            stats = {
                'totals': {
                    'total_calls': call_stats['total_calls'],
                    'completed_calls': call_stats['completed'],
                    'failed_calls': call_stats['failed'],
                    'no_answer_calls': call_stats['no_answer'],
                    'short_calls': detailed_outcomes.get('short_call', 0),
                    'unknown_calls': call_stats.get('unknown', 0)
                },
                'duration': {
                    'total_duration_seconds': analysis['total_duration'],
                    'total_duration_minutes': analysis['total_duration'] / 60,
                    'average_duration_seconds': analysis['total_duration'] / call_stats['total_calls'] if call_stats['total_calls'] > 0 else 0
                },
                'costs': {
                    'total_cost_usd': analysis['total_duration'] * 0.013 / 60,  # Estimated cost
                    'average_cost_per_call': (analysis['total_duration'] * 0.013 / 60) / call_stats['total_calls'] if call_stats['total_calls'] > 0 else 0
                },
                'calls': analysis['call_details']
            }
        
        # Add campaign metadata and progress calculation
        total_contacts = len(campaign.get('contacts', []))
        total_calls_made = stats['totals']['total_calls']
        progress_percent = (total_calls_made / total_contacts * 100) if total_contacts > 0 else 0
        
        stats['campaign_info'] = {
            'id': campaign_id,
            'name': campaign.get('name', 'Unnamed Campaign'),
            'status': campaign.get('status', 'draft'),
            'created_at': campaign.get('created_at'),
            'started_at': campaign.get('started_at'),
            'completed_at': campaign.get('completed_at'),
            'agent_name': campaign.get('agent_name'),
            'total_contacts': total_contacts
        }
        
        # Add progress percentage for frontend progress bar
        stats['progress_percent'] = round(progress_percent, 1)
        stats['call_statistics'] = {
            'total_calls': stats['totals']['total_calls'],
            'completed': stats['totals']['completed_calls'],
            'failed': stats['totals']['failed_calls'],
            'no_answer': stats['totals']['no_answer_calls']
        }
        
        logger.info(f"📊 ACCURATE statistics for campaign {campaign_id}: Total={stats['totals']['total_calls']}, Completed={stats['totals']['completed_calls']}, Failed={stats['totals']['failed_calls']}, No Answer={stats['totals']['no_answer_calls']}, Progress={progress_percent:.1f}%")
        
        return jsonify(stats)
        
    except Exception as e:
        logger.error(f"Error getting campaign statistics: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/campaigns/<campaign_id>/statistics/telnyx', methods=['GET'])
def get_campaign_statistics_telnyx(campaign_id):
    """Get TELNYX-VERIFIED campaign statistics using real Telnyx API data"""
    try:
        if campaign_id not in campaigns_storage:
            return jsonify({'error': 'Campaign not found'}), 404
        
        # Use Telnyx call analyzer for most accurate data
        try:
            telnyx_analyzer = TelnyxCallAnalyzer(api_key=TELNYX_API_KEY, data_dir=str(DATA_DIR))
            analysis = telnyx_analyzer.analyze_campaign_with_telnyx(campaign_id)
        except ValueError as e:
            # Fall back to local analyzer if Telnyx API key is missing
            logger.warning(f"Telnyx analyzer not available: {e}")
            local_analyzer = CallLogAnalyzer(str(DATA_DIR))
            analysis = local_analyzer.analyze_campaign(campaign_id)
            analysis['data_source'] = 'local_logs'
        
        campaign = campaigns_storage[campaign_id]
        
        if 'error' in analysis:
            # No call logs found - return zero stats
            stats = {
                'totals': {
                    'total_calls': 0,
                    'completed_calls': 0,
                    'failed_calls': 0,
                    'no_answer_calls': 0,
                    'short_calls': 0,
                    'in_progress_calls': 0,
                    'unknown_calls': 0
                },
                'duration': {
                    'total_duration_seconds': 0,
                    'total_duration_minutes': 0,
                    'average_duration_seconds': 0
                },
                'costs': {
                    'total_cost_usd': 0,
                    'average_cost_per_call': 0
                },
                'calls': [],
                'data_source': 'none',
                'progress_percent': 0,
                'call_statistics': {
                    'total_calls': 0,
                    'completed': 0,
                    'failed': 0,
                    'no_answer': 0
                }
            }
        else:
            # Use Telnyx-verified analysis results
            call_stats = analysis['call_statistics']
            detailed_outcomes = analysis['detailed_outcomes']
            
            stats = {
                'totals': {
                    'total_calls': call_stats['total_calls'],
                    'completed_calls': call_stats['completed'],
                    'failed_calls': call_stats['failed'],
                    'no_answer_calls': call_stats['no_answer'],
                    'short_calls': detailed_outcomes.get('short_call', 0),
                    'in_progress_calls': call_stats.get('in_progress', 0),
                    'unknown_calls': call_stats.get('unknown', 0)
                },
                'duration': {
                    'total_duration_seconds': analysis['total_duration'],
                    'total_duration_minutes': analysis['total_duration'] / 60,
                    'average_duration_seconds': analysis['total_duration'] / call_stats['total_calls'] if call_stats['total_calls'] > 0 else 0
                },
                'costs': {
                    'total_cost_usd': analysis['total_duration'] * 0.013 / 60,  # Estimated cost
                    'average_cost_per_call': (analysis['total_duration'] * 0.013 / 60) / call_stats['total_calls'] if call_stats['total_calls'] > 0 else 0
                },
                'calls': analysis['call_details'],
                'data_source': analysis.get('data_source', 'telnyx_api')
            }
        
        # Add campaign metadata and progress calculation
        total_contacts = len(campaign.get('contacts', []))
        total_calls_made = stats['totals']['total_calls']
        progress_percent = (total_calls_made / total_contacts * 100) if total_contacts > 0 else 0
        
        stats['campaign_info'] = {
            'id': campaign_id,
            'name': campaign.get('name', 'Unnamed Campaign'),
            'status': campaign.get('status', 'draft'),
            'created_at': campaign.get('created_at'),
            'started_at': campaign.get('started_at'),
            'completed_at': campaign.get('completed_at'),
            'agent_name': campaign.get('agent_name'),
            'total_contacts': total_contacts
        }
        
        # Add progress percentage for frontend progress bar
        stats['progress_percent'] = round(progress_percent, 1)
        stats['call_statistics'] = {
            'total_calls': stats['totals']['total_calls'],
            'completed': stats['totals']['completed_calls'],
            'failed': stats['totals']['failed_calls'],
            'no_answer': stats['totals']['no_answer_calls']
        }
        
        logger.info(f"📊 TELNYX-VERIFIED statistics for campaign {campaign_id}: Data Source={stats['data_source']}, Total={stats['totals']['total_calls']}, Completed={stats['totals']['completed_calls']}, Failed={stats['totals']['failed_calls']}, No Answer={stats['totals']['no_answer_calls']}, Progress={progress_percent:.1f}%")
        
        return jsonify(stats)
        
    except Exception as e:
        logger.error(f"Error getting Telnyx campaign statistics: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/campaigns/<campaign_id>/statistics/enhanced', methods=['GET'])
def get_campaign_statistics_enhanced(campaign_id):
    """Get enhanced campaign statistics with comprehensive metrics"""
    try:
        # Get campaign data
        campaign = campaigns_storage.get(campaign_id)
        if not campaign:
            return jsonify({'error': 'Campaign not found'}), 404

        # Correct call statuses based on actual duration (fallback for webhook failures)
        correct_call_statuses()

        # Reload call logs after correction
        global call_logs
        call_logs = load_json_data(CALL_LOGS_FILE)

        # Get all call logs for this campaign
        campaign_calls = [log for log in call_logs if log.get('campaign_id') == campaign_id]
        
        # Basic campaign info
        campaign_info = {
            'id': campaign_id,
            'name': campaign.get('name', 'Unknown Campaign'),
            'status': campaign.get('status', 'unknown'),
            'created_at': campaign.get('created_at', ''),
            'started_at': campaign.get('started_at'),
            'completed_at': campaign.get('completed_at'),
            'agent_name': campaign.get('agent_name', 'AI Agent'),
            'total_contacts': len(campaign.get('contacts', []))
        }
        
        # Analyze calls
        total_calls = len(campaign_calls)
        # Count both 'completed' and 'answered' as successful calls
        completed_calls = len([c for c in campaign_calls if c.get('status') in ['completed', 'answered']])
        failed_calls = len([c for c in campaign_calls if c.get('status') == 'failed'])
        no_answer_calls = len([c for c in campaign_calls if c.get('status') == 'no_answer'])
        short_calls = len([c for c in campaign_calls if c.get('call_duration', 0) < 10])
        
        # Duration analysis
        total_duration = sum(c.get('call_duration', 0) for c in campaign_calls)
        avg_duration = total_duration / total_calls if total_calls > 0 else 0
        
        # Cost analysis (estimate based on duration)
        cost_per_minute = 0.013  # Telnyx rate
        total_cost = (total_duration / 60) * cost_per_minute
        avg_cost_per_call = total_cost / total_calls if total_calls > 0 else 0
        
        # Progress calculation
        total_contacts = len(campaign.get('contacts', []))
        progress_percent = (total_calls / total_contacts * 100) if total_contacts > 0 else 0
        
        # Recent calls (last 50)
        recent_calls = sorted(campaign_calls, key=lambda x: x.get('timestamp', ''), reverse=True)[:50]
        formatted_calls = []
        for call in recent_calls:
            formatted_calls.append({
                'id': call.get('id', ''),
                'contact_name': call.get('contact_name', 'Unknown'),
                'to_phone': call.get('contact_phone', ''),
                'from_phone': call.get('from_phone', ''),
                'status': call.get('status', 'unknown'),
                'start_time': call.get('timestamp', ''),
                'duration': call.get('call_duration', 0),
                'cost': (call.get('call_duration', 0) / 60) * cost_per_minute,
                'recording_url': call.get('recording_url', '')
            })
        
        # Build enhanced response
        enhanced_stats = {
            'success': True,
            'data': {
                'campaign_info': campaign_info,
                'progress': {
                    'total_contacts': total_contacts,
                    'contacts_processed': total_calls,
                    'progress_percentage': min(progress_percent, 100)
                },
                'summary': {
                    'total_calls': total_calls,
                    'successful_calls': completed_calls,
                    'failed_calls': failed_calls,
                    'no_answer_calls': no_answer_calls,
                    'short_calls': short_calls,
                    'total_duration_seconds': total_duration,
                    'total_duration_minutes': total_duration / 60,
                    'average_duration_seconds': avg_duration,
                    'total_cost': total_cost,
                    'average_cost_per_call': avg_cost_per_call,
                    'success_rate': (completed_calls / total_calls * 100) if total_calls > 0 else 0
                },
                'call_analysis': {
                    'calls_by_status': {
                        'completed': completed_calls,
                        'failed': failed_calls,
                        'no_answer': no_answer_calls,
                        'short': short_calls
                    },
                    'latest_calls': formatted_calls
                }
            }
        }
        
        logger.info(f"📊 Enhanced statistics for campaign {campaign_id}: Total={total_calls}, Completed={completed_calls}, Failed={failed_calls}, Progress={progress_percent:.1f}%")
        
        return jsonify(enhanced_stats)
        
    except Exception as e:
        logger.error(f"Error getting enhanced campaign statistics: {e}")
        return jsonify({
            'success': False,
            'error': 'Failed to get enhanced campaign statistics',
            'message': str(e)
        }), 500

@app.route('/api/campaigns/<campaign_id>/unresponsive', methods=['GET'])
def get_campaign_unresponsive(campaign_id):
    """Get unresponsive contacts for a campaign"""
    try:
        if campaign_id not in campaigns_storage:
            return jsonify({'error': 'Campaign not found'}), 404
        
        if CAMPAIGN_STATS_AVAILABLE:
            unresponsive_contacts = get_unresponsive_contacts(campaign_id, str(DATA_DIR))
        else:
            # Fallback: get failed calls from call logs
            campaign_calls = [log for log in call_logs if log.get('campaign_id') == campaign_id]
            unresponsive_contacts = []
            for call in campaign_calls:
                if call.get('status') == 'failed':
                    unresponsive_contacts.append({
                        'phone_number': call.get('contact_phone', ''),
                        'contact_name': call.get('contact_name', ''),
                        'notes': call.get('notes', ''),
                        'last_attempt': call.get('timestamp', ''),
                        'reason': 'failed'
                    })
        
        return jsonify({
            'campaign_id': campaign_id,
            'unresponsive_contacts': unresponsive_contacts,
            'total_unresponsive': len(unresponsive_contacts)
        })
        
    except Exception as e:
        logger.error(f"Error getting unresponsive contacts: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/campaigns/<campaign_id>/recycle', methods=['POST'])
def recycle_campaign_unresponsive(campaign_id):
    """Recycle unresponsive contacts in a campaign - Fixed to work like CALLBOT2.1"""
    try:
        if campaign_id not in campaigns_storage:
            return jsonify({'error': 'Campaign not found'}), 404

        campaign = campaigns_storage[campaign_id]
        request_data = request.get_json() or {}
        min_retry_interval = request_data.get('min_retry_interval_hours', 0.5)  # Default 30 minutes
        create_new_campaign = request_data.get('create_new_campaign', False)

        # Get all call logs for this campaign (similar to CALLBOT2.1 approach)
        all_calls = [log for log in call_logs if log.get('campaign_id') == campaign_id]

        if not all_calls:
            return jsonify({
                'success': False,
                'error': 'No call logs found for this campaign',
                'total_unresponsive': 0,
                'eligible_for_retry': 0
            })

        # Group calls by phone number to find the latest status for each number (like CALLBOT2.1)
        calls_by_number = {}
        for call in sorted(all_calls, key=lambda x: x.get('timestamp', ''), reverse=True):
            phone_number = call.get('contact_phone', '')
            if phone_number and phone_number not in calls_by_number:
                calls_by_number[phone_number] = call

        # Get numbers to recycle - only those whose calls truly failed or didn't answer
        # DO NOT include 'initiated' or 'completed' - these mean the call was successful
        recyclable_statuses = ['failed', 'no_answer', 'no-answer', 'busy']
        unresponsive_contacts = []

        for phone_number, latest_call in calls_by_number.items():
            call_status = latest_call.get('status', '').lower()
            if call_status in recyclable_statuses:
                # Check retry interval
                last_attempt = latest_call.get('timestamp', '')
                if last_attempt:
                    try:
                        from datetime import datetime, timezone, timedelta
                        last_attempt_dt = datetime.fromisoformat(last_attempt.replace('Z', '+00:00'))
                        current_time = datetime.now(timezone.utc)
                        min_interval = timedelta(hours=min_retry_interval)

                        if current_time - last_attempt_dt >= min_interval:
                            unresponsive_contacts.append({
                                'contact_name': latest_call.get('contact_name', 'Unknown'),
                                'phone_number': phone_number,
                                'reason': call_status,
                                'last_attempt': last_attempt,
                                'notes': latest_call.get('notes', '')
                            })
                    except Exception as e:
                        logger.warning(f"Error parsing timestamp for {phone_number}: {e}")
                        # Include contact if we can't parse time
                        unresponsive_contacts.append({
                            'contact_name': latest_call.get('contact_name', 'Unknown'),
                            'phone_number': phone_number,
                            'reason': call_status,
                            'last_attempt': last_attempt,
                            'notes': latest_call.get('notes', '')
                        })

        if not unresponsive_contacts:
            return jsonify({
                'success': False,
                'error': f'No contacts eligible for retry (minimum {min_retry_interval} hour interval required)',
                'total_unresponsive': len([c for c in calls_by_number.values() if c.get('status', '').lower() in recyclable_statuses]),
                'eligible_for_retry': 0
            })

        # Debug logging to understand what's happening
        logger.info(f"🔍 RECYCLE DEBUG for campaign {campaign_id}:")
        logger.info(f"   📞 Total call logs found: {len(all_calls)}")
        logger.info(f"   📱 Unique phone numbers: {len(calls_by_number)}")

        for phone, call in calls_by_number.items():
            status = call.get('status', 'unknown')
            logger.info(f"   📞 {phone}: {status} (recyclable: {status.lower() in recyclable_statuses})")

        logger.info(f"   ♻️ Found {len(unresponsive_contacts)} contacts to recycle")

        if create_new_campaign:
            # Create a new campaign with unresponsive contacts
            recycled_campaign_id = f"campaign_{int(time.time())}"
            recycled_campaign = {
                'id': recycled_campaign_id,
                'name': f"{campaign.get('name', 'Campaign')} - Recycled",
                'status': 'draft',
                'created_at': datetime.now(timezone.utc).isoformat(),
                'is_recycled_campaign': True,
                'original_campaign_id': campaign_id,
                'original_campaign_name': campaign.get('name'),

                # Copy settings from original campaign
                'telnyx_numbers': campaign.get('telnyx_numbers', []),
                'agent_id': campaign.get('agent_id'),
                'agent_name': campaign.get('agent_name'),
                'first_message': campaign.get('first_message', ''),
                'agent_voice': campaign.get('agent_voice'),
                'agent_model': campaign.get('agent_model'),
                'agent_system_prompt': campaign.get('agent_system_prompt'),
                'agent_emotional_prompt': campaign.get('agent_emotional_prompt'),

                # Convert unresponsive contacts to campaign format
                'contacts': [
                    {
                        "name": contact['contact_name'],
                        "phone_number": contact['phone_number'],
                        "notes": f"Retry: {contact['reason']} | Original notes: {contact.get('notes', '')}",
                        "status": "pending",
                        "retry_attempt": True,
                        "original_attempt_time": contact['last_attempt']
                    }
                    for contact in unresponsive_contacts
                ],

                'recycled_contact_count': len(unresponsive_contacts),
                'recycled_at': datetime.now(timezone.utc).isoformat()
            }

            # Add to campaigns storage
            campaigns_storage[recycled_campaign_id] = recycled_campaign
            save_json_data(CAMPAIGNS_FILE, campaigns_storage)

            result = {
                "success": True,
                "message": f"Recycled campaign created with {len(unresponsive_contacts)} unresponsive contacts",
                "original_campaign_id": campaign_id,
                "recycled_campaign_id": recycled_campaign_id,
                "total_unresponsive": len(unresponsive_contacts),
                "eligible_for_retry": len(unresponsive_contacts),
                "contacts": unresponsive_contacts
            }
        else:
            # Recycle in place (restart with only unresponsive contacts) - like CALLBOT2.1
            campaign['contacts'] = [
                {
                    "name": contact['contact_name'],
                    "phone_number": contact['phone_number'],
                    "notes": f"Retry: {contact['reason']} | Original notes: {contact.get('notes', '')}",
                    "status": "pending",
                    "retry_attempt": True,
                    "original_attempt_time": contact['last_attempt']
                }
                for contact in unresponsive_contacts
            ]

            # Update campaign metadata
            campaign['status'] = 'draft'  # Reset to draft so it can be started again
            campaign['recycled_at'] = datetime.now(timezone.utc).isoformat()
            campaign['is_recycled_campaign'] = True
            campaign['original_contact_count'] = campaign.get('original_contact_count', len(campaign['contacts']))
            campaign['recycled_contact_count'] = len(unresponsive_contacts)

            # Save updated campaign
            save_json_data(CAMPAIGNS_FILE, campaigns_storage)

            result = {
                "success": True,
                "message": f"Campaign recycled with {len(unresponsive_contacts)} unresponsive contacts",
                "campaign_id": campaign_id,
                "total_unresponsive": len(unresponsive_contacts),
                "eligible_for_retry": len(unresponsive_contacts),
                "contacts": unresponsive_contacts
            }

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error recycling campaign: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@app.route('/api/campaigns/<campaign_id>/status', methods=['GET'])
def get_campaign_status(campaign_id):
    """Get real-time campaign status with call progress"""
    try:
        if campaign_id not in campaigns_storage:
            return jsonify({'error': 'Campaign not found'}), 404
        
        campaign = campaigns_storage[campaign_id]
        
        # Get basic campaign info
        status_info = {
            'campaign_id': campaign_id,
            'name': campaign.get('name', 'Unnamed Campaign'),
            'status': campaign.get('status', 'draft'),
            'created_at': campaign.get('created_at'),
            'started_at': campaign.get('started_at'),
            'completed_at': campaign.get('completed_at'),
            'total_contacts': len(campaign.get('contacts', []))
        }
        
        # Use accurate call log analyzer
        analyzer = CallLogAnalyzer(str(DATA_DIR))
        analysis = analyzer.analyze_campaign(campaign_id)
        
        if 'error' in analysis:
            # No call logs - return empty status
            status_counts = {
                'total_calls': 0,
                'completed': 0,
                'failed': 0,
                'no_answer': 0,
                'busy': 0,
                'in_progress': 0
            }
            total_duration = 0
            total_cost = 0.0
            campaign_calls = []
        else:
            # Use accurate analysis
            call_stats = analysis['call_statistics']
            status_counts = {
                'total_calls': call_stats['total_calls'],
                'completed': call_stats['completed'],
                'failed': call_stats['failed'],
                'no_answer': call_stats['no_answer'],
                'busy': 0,  # We don't track busy separately yet
                'in_progress': 0  # We don't track in-progress separately yet
            }
            
            total_duration = analysis['total_duration']
            total_cost = total_duration * 0.013 / 60  # ~$0.013 per minute
            campaign_calls = analysis['call_details']
        
        # Calculate progress percentage
        progress_percent = 0
        if status_info['total_contacts'] > 0:
            completed_calls = status_counts['completed'] + status_counts['failed'] + status_counts['no_answer']
            progress_percent = (completed_calls / status_info['total_contacts']) * 100
        
        # Combine all status information
        status_info.update({
            'call_statistics': status_counts,
            'progress_percent': round(progress_percent, 1),
            'total_duration_seconds': total_duration,
            'total_cost_estimate': round(total_cost, 4),
            'recent_calls': campaign_calls[-10:] if campaign_calls else []  # Last 10 calls
        })
        
        return jsonify(status_info)
        
    except Exception as e:
        logger.error(f"Error getting campaign status: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/campaigns/active', methods=['GET'])
def get_active_campaigns():
    """Get all currently active campaigns"""
    try:
        active_campaigns = []
        
        for campaign_id, campaign in campaigns_storage.items():
            if campaign.get('status') in ['running', 'paused']:
                # Get quick stats for each active campaign
                campaign_calls = [log for log in call_logs if log.get('campaign_id') == campaign_id]
                
                active_campaigns.append({
                    'id': campaign_id,
                    'name': campaign.get('name', 'Unnamed Campaign'),
                    'status': campaign.get('status'),
                    'started_at': campaign.get('started_at'),
                    'total_contacts': len(campaign.get('contacts', [])),
                    'calls_made': len(campaign_calls),
                    'agent_name': campaign.get('agent_name')
                })
        
        return jsonify({
            'active_campaigns': active_campaigns,
            'total_active': len(active_campaigns)
        })
        
    except Exception as e:
        logger.error(f"Error getting active campaigns: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/debug/campaign/<campaign_id>', methods=['GET'])
def debug_campaign(campaign_id):
    """Debug endpoint to see what's happening with a campaign"""
    try:
        # Get campaign data
        campaign = campaigns_storage.get(campaign_id, {})
        
        # Get all call logs for this campaign
        campaign_calls = [log for log in call_logs if log.get('campaign_id') == campaign_id]
        
        # Get data directory contents for this campaign
        data_files = []
        try:
            import os
            data_dir = Path(__file__).parent / "data"
            if data_dir.exists():
                for file in data_dir.glob(f"*{campaign_id}*"):
                    data_files.append(str(file))
        except Exception as e:
            data_files = [f"Error reading data dir: {e}"]
        
        debug_info = {
            'campaign_id': campaign_id,
            'campaign_exists': campaign_id in campaigns_storage,
            'campaign_data': campaign,
            'call_logs_count': len(campaign_calls),
            'call_logs': campaign_calls,
            'data_files': data_files,
            'campaign_stats_available': CAMPAIGN_STATS_AVAILABLE,
            'all_campaigns': list(campaigns_storage.keys())
        }
        
        return jsonify(debug_info)
        
    except Exception as e:
        logger.error(f"Error in debug endpoint: {str(e)}")
        return jsonify({'error': str(e)}), 500

class EnhancedCallTracker:
    def __init__(self, data_dir: str = "data", telnyx_api_key: str = None):
        self.data_dir = Path(data_dir)
        self.telnyx_api_key = telnyx_api_key
        self.call_logs_file = self.data_dir / "enhanced_call_logs.json"
        self.campaigns_file = self.data_dir / "campaigns.json"
        
        # Ensure data directory exists
        self.data_dir.mkdir(exist_ok=True)
        
        # Initialize telnyx if API key available
        if self.telnyx_api_key:
            telnyx.api_key = self.telnyx_api_key
    
    def load_call_logs(self):
        """Load enhanced call logs"""
        if self.call_logs_file.exists():
            try:
                with open(self.call_logs_file, 'r') as f:
                    return json.load(f)
            except:
                return []
        return []
    
    def save_call_logs(self, logs):
        """Save enhanced call logs"""
        try:
            with open(self.call_logs_file, 'w') as f:
                json.dump(logs, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Error saving call logs: {e}")
    
    def log_call_initiated(self, campaign_id: str, contact_name: str, contact_phone: str, 
                          from_phone: str, agent_name: str = None):
        """Log when a call is initiated"""
        logs = self.load_call_logs()
        
        call_id = f"call_{campaign_id}_{len(logs) + 1}_{int(time.time())}"
        
        call_log = {
            'id': call_id,
            'campaign_id': campaign_id,
            'contact_name': contact_name,
            'contact_phone': contact_phone,
            'from_phone': from_phone,
            'agent_name': agent_name,
            'status': 'initiated',
            'initiated_at': datetime.now().isoformat(),
            'duration_seconds': 0,
            'call_answered': False,
            'telnyx_call_id': None,
            'room_name': None,
            'final_status': 'pending',
            'needs_recycling': False
        }
        
        logs.append(call_log)
        self.save_call_logs(logs)
        
        logger.info(f"📞 Call initiated: {contact_name} ({contact_phone}) from {from_phone}")
        return call_id
    
    def update_call_with_room_info(self, call_id: str, room_name: str, telnyx_call_id: str = None):
        """Update call log with room and Telnyx information"""
        logs = self.load_call_logs()
        
        for call in logs:
            if call['id'] == call_id:
                call['room_name'] = room_name
                call['status'] = 'connected'
                call['connected_at'] = datetime.now().isoformat()
                if telnyx_call_id:
                    call['telnyx_call_id'] = telnyx_call_id
                break
        
        self.save_call_logs(logs)
        logger.info(f"📞 Call {call_id} connected to room {room_name}")
    
    def schedule_call_status_check(self, call_id: str, delay_seconds: int = 30):
        """Schedule a status check after call should have completed"""
        def check_status():
            self.check_and_update_call_status(call_id)
        
        Timer(delay_seconds, check_status).start()
        logger.info(f"⏰ Scheduled status check for call {call_id} in {delay_seconds} seconds")
    
    def check_and_update_call_status(self, call_id: str):
        """Check call status using Telnyx API and update accordingly"""
        logs = self.load_call_logs()
        call_log = None
        
        for call in logs:
            if call['id'] == call_id:
                call_log = call
                break
        
        if not call_log:
            logger.error(f"Call log not found for {call_id}")
            return
        
        # If already finalized, skip
        if call_log.get('final_status') != 'pending':
            return
        
        # Check if we have Telnyx call data
        telnyx_call_id = call_log.get('telnyx_call_id')
        room_name = call_log.get('room_name')
        
        # Determine call outcome based on available data
        call_duration = 0
        call_answered = False
        final_status = 'no_answer'
        
        if self.telnyx_api_key and telnyx_call_id:
            # Try to get real Telnyx call data
            try:
                telnyx_data = self.get_telnyx_call_data(telnyx_call_id)
                if telnyx_data and 'duration' in telnyx_data:
                    call_duration = telnyx_data['duration']
                    call_answered = call_duration > 0
                    
                    if call_duration >= 10:  # Call lasted 10+ seconds
                        final_status = 'completed'
                    elif call_duration > 0:
                        final_status = 'short_call'
                    else:
                        final_status = 'no_answer'
                        
            except Exception as e:
                logger.warning(f"Could not get Telnyx data for call {call_id}: {e}")
        
        # Update call log
        call_log['duration_seconds'] = call_duration
        call_log['call_answered'] = call_answered
        call_log['final_status'] = final_status
        call_log['status'] = 'completed'
        call_log['completed_at'] = datetime.now().isoformat()
        call_log['needs_recycling'] = (final_status in ['no_answer', 'failed'])
        
        self.save_call_logs(logs)
        
        logger.info(f"📊 Call {call_id} finalized: {final_status} (Duration: {call_duration}s)")
        
        return final_status
    
    def get_telnyx_call_data(self, call_id: str):
        """Get call data from Telnyx API"""
        try:
            headers = {
                'Authorization': f'Bearer {self.telnyx_api_key}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(
                f"https://api.telnyx.com/v2/calls/{call_id}",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get('data', {})
            else:
                logger.warning(f"Telnyx API returned {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error fetching Telnyx call data: {e}")
            return None
    
    def get_campaign_statistics(self, campaign_id: str):
        """Get comprehensive campaign statistics"""
        logs = self.load_call_logs()
        campaign_calls = [call for call in logs if call['campaign_id'] == campaign_id]
        
        if not campaign_calls:
            return {
                'campaign_id': campaign_id,
                'total_calls': 0,
                'completed': 0,
                'short_calls': 0,
                'no_answer': 0,
                'failed': 0,
                'in_progress': 0,
                'total_duration': 0,
                'needs_recycling': []
            }
        
        stats = {
            'completed': 0,
            'short_calls': 0,
            'no_answer': 0,
            'failed': 0,
            'in_progress': 0
        }
        
        total_duration = 0
        needs_recycling = []
        
        for call in campaign_calls:
            final_status = call.get('final_status', 'pending')
            duration = call.get('duration_seconds', 0)
            
            if final_status == 'pending':
                stats['in_progress'] += 1
            elif final_status in stats:
                stats[final_status] += 1
            
            total_duration += duration
            
            if call.get('needs_recycling', False):
                needs_recycling.append({
                    'contact_name': call['contact_name'],
                    'contact_phone': call['contact_phone'],
                    'reason': final_status
                })
        
        return {
            'campaign_id': campaign_id,
            'total_calls': len(campaign_calls),
            'completed': stats['completed'],
            'short_calls': stats['short_calls'], 
            'no_answer': stats['no_answer'],
            'failed': stats['failed'],
            'in_progress': stats['in_progress'],
            'total_duration': total_duration,
            'needs_recycling': needs_recycling,
            'call_details': campaign_calls
        }
    
    def get_numbers_for_recycling(self, campaign_id: str):
        """Get phone numbers that need to be recycled (didn't answer/failed)"""
        logs = self.load_call_logs()
        campaign_calls = [call for call in logs if call['campaign_id'] == campaign_id]
        
        recycling_numbers = []
        for call in campaign_calls:
            if call.get('needs_recycling', False):
                recycling_numbers.append({
                    'name': call['contact_name'],
                    'phone_number': call['contact_phone'],
                    'reason': call.get('final_status', 'unknown'),
                    'last_attempt': call.get('completed_at', call.get('initiated_at'))
                })
        
        return recycling_numbers

# Create global call tracker instance
enhanced_call_tracker = EnhancedCallTracker(str(DATA_DIR), TELNYX_API_KEY)

# Enhanced campaign monitoring and statistics
@app.route('/api/campaigns/<campaign_id>/monitor', methods=['GET'])
def monitor_campaign(campaign_id):
    """Get real-time campaign monitoring data"""
    try:
        if campaign_id not in campaigns_storage:
            return jsonify({'error': 'Campaign not found'}), 404
        
        campaign = campaigns_storage[campaign_id]
        
        # Get call logs for this campaign
        campaign_call_logs = [
            log for log in call_logs 
            if log.get('campaign_id') == campaign_id
        ]
        
        # Calculate real-time statistics with proper status tracking
        total_contacts = len(campaign.get('contacts', []))
        initiated_calls = len([log for log in campaign_call_logs if log.get('status') == 'initiated'])
        answered_calls = len([log for log in campaign_call_logs if log.get('status') == 'answered'])
        no_answer_calls = len([log for log in campaign_call_logs if log.get('status') == 'no_answer'])
        failed_calls = len([log for log in campaign_call_logs if log.get('status') == 'failed'])
        
        # Get campaign results if available
        call_results = campaign.get('call_results', {})
        
        monitoring_data = {
            'campaign_id': campaign_id,
            'name': campaign.get('name', 'Unnamed Campaign'),
            'status': campaign.get('status', 'unknown'),
            'started_at': campaign.get('started_at'),
            'completed_at': campaign.get('completed_at'),
            'total_duration': campaign.get('total_duration'),
            'contacts': {
                'total': total_contacts,
                'called': len(campaign_call_logs),
                'initiated': initiated_calls,
                'answered': answered_calls,
                'no_answer': no_answer_calls,
                'failed': failed_calls,
                'remaining': max(0, total_contacts - len(campaign_call_logs))
            },
            'answer_rate': round((answered_calls / max(1, len(campaign_call_logs))) * 100, 1) if campaign_call_logs else 0,
            'success_rate': round(((answered_calls + initiated_calls) / max(1, len(campaign_call_logs))) * 100, 1) if campaign_call_logs else 0,
            'phone_numbers': campaign.get('telnyx_numbers', []),
            'agent': {
                'id': campaign.get('agent_id'),
                'name': campaign.get('agent_name')
            },
            'call_results': call_results,
            'recent_calls': campaign_call_logs[-10:] if campaign_call_logs else [],  # Last 10 calls
            'errors': call_results.get('errors', []) if call_results else []
        }
        
        return jsonify(monitoring_data)
        
    except Exception as e:
        logger.error(f"Error monitoring campaign {campaign_id}: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/campaigns/performance', methods=['GET'])
def get_campaigns_performance():
    """Get performance statistics for all campaigns"""
    try:
        performance_data = []
        
        for campaign_id, campaign in campaigns_storage.items():
            # Get call logs for this campaign
            campaign_call_logs = [
                log for log in call_logs 
                if log.get('campaign_id') == campaign_id
            ]
            
            total_contacts = len(campaign.get('contacts', []))
            initiated_calls = len([log for log in campaign_call_logs if log.get('status') == 'initiated'])
            answered_calls = len([log for log in campaign_call_logs if log.get('status') == 'answered'])
            no_answer_calls = len([log for log in campaign_call_logs if log.get('status') == 'no_answer'])
            failed_calls = len([log for log in campaign_call_logs if log.get('status') == 'failed'])
            
            # Calculate duration
            duration = None
            if campaign.get('started_at') and campaign.get('completed_at'):
                try:
                    start_time = datetime.fromisoformat(campaign.get('started_at'))
                    end_time = datetime.fromisoformat(campaign.get('completed_at'))
                    duration = (end_time - start_time).total_seconds()
                except:
                    duration = campaign.get('total_duration')
            
            performance_data.append({
                'campaign_id': campaign_id,
                'name': campaign.get('name', 'Unnamed'),
                'status': campaign.get('status', 'unknown'),
                'created_at': campaign.get('created_at'),
                'started_at': campaign.get('started_at'),
                'completed_at': campaign.get('completed_at'),
                'duration_seconds': duration,
                'contacts_total': total_contacts,
                'calls_attempted': len(campaign_call_logs),
                'calls_initiated': initiated_calls,
                'calls_answered': answered_calls,
                'calls_no_answer': no_answer_calls,
                'calls_failed': failed_calls,
                'answer_rate': round((answered_calls / max(1, len(campaign_call_logs))) * 100, 1) if campaign_call_logs else 0,
                'success_rate': round(((answered_calls + initiated_calls) / max(1, len(campaign_call_logs))) * 100, 1) if campaign_call_logs else 0,
                'phone_numbers_count': len(campaign.get('telnyx_numbers', [])),
                'agent_name': campaign.get('agent_name'),
                'has_errors': bool(campaign.get('call_results', {}).get('errors'))
            })
        
        # Sort by created_at (most recent first)
        performance_data.sort(key=lambda x: x.get('created_at', ''), reverse=True)
        
        # Calculate overall statistics
        total_campaigns = len(performance_data)
        completed_campaigns = len([c for c in performance_data if c['status'] == 'completed'])
        running_campaigns = len([c for c in performance_data if c['status'] == 'running'])
        failed_campaigns = len([c for c in performance_data if c['status'] == 'failed'])
        
        total_calls = sum(c['calls_attempted'] for c in performance_data)
        total_answered = sum(c['calls_answered'] for c in performance_data)
        total_initiated = sum(c['calls_initiated'] for c in performance_data)
        
        return jsonify({
            'summary': {
                'total_campaigns': total_campaigns,
                'completed': completed_campaigns,
                'running': running_campaigns,
                'failed': failed_campaigns,
                'total_calls': total_calls,
                'total_initiated': total_initiated,
                'total_answered': total_answered,
                'overall_answer_rate': round((total_answered / max(1, total_calls)) * 100, 1),
                'overall_success_rate': round(((total_answered + total_initiated) / max(1, total_calls)) * 100, 1)
            },
            'campaigns': performance_data
        })
        
    except Exception as e:
        logger.error(f"Error getting campaigns performance: {str(e)}")
        return jsonify({'error': str(e)}), 500

# Campaign pause/resume functionality for better control
@app.route('/api/campaigns/<campaign_id>/pause', methods=['POST'])
def pause_campaign(campaign_id):
    """Pause a running campaign"""
    try:
        if campaign_id not in campaigns_storage:
            return jsonify({'error': 'Campaign not found'}), 404
        
        campaign = campaigns_storage[campaign_id]
        
        if campaign.get('status') != 'running':
            return jsonify({'error': 'Campaign is not running'}), 400
        
        campaign['status'] = 'paused'
        campaign['paused_at'] = datetime.now().isoformat()
        
        save_json_data(CAMPAIGNS_FILE, campaigns_storage)
        
        logger.info(f"⏸️ Campaign {campaign_id} paused")
        
        return jsonify({
            'message': 'Campaign paused successfully',
            'campaign': {
                'id': campaign_id,
                'status': campaign['status'],
                'paused_at': campaign['paused_at']
            }
        })
        
    except Exception as e:
        logger.error(f"Error pausing campaign {campaign_id}: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/campaigns/<campaign_id>/resume', methods=['POST'])
def resume_campaign(campaign_id):
    """Resume a paused campaign"""
    try:
        if campaign_id not in campaigns_storage:
            return jsonify({'error': 'Campaign not found'}), 404
        
        campaign = campaigns_storage[campaign_id]
        
        if campaign.get('status') != 'paused':
            return jsonify({'error': 'Campaign is not paused'}), 400
        
        campaign['status'] = 'running'
        campaign['resumed_at'] = datetime.now().isoformat()
        
        save_json_data(CAMPAIGNS_FILE, campaigns_storage)
        
        logger.info(f"▶️ Campaign {campaign_id} resumed")
        
        return jsonify({
            'message': 'Campaign resumed successfully',
            'campaign': {
                'id': campaign_id,
                'status': campaign['status'],
                'resumed_at': campaign['resumed_at']
            }
        })
        
    except Exception as e:
        logger.error(f"Error resuming campaign {campaign_id}: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/system/concurrent-calling/health', methods=['GET'])
def check_concurrent_calling_health():
    """Health check for concurrent calling system"""
    try:
        # Check active campaigns
        running_campaigns = [
            cid for cid, campaign in campaigns_storage.items() 
            if campaign.get('status') == 'running'
        ]
        
        # Check available phone numbers
        available_phones = []
        for campaign_id, campaign in campaigns_storage.items():
            if campaign.get('telnyx_numbers'):
                available_phones.extend(campaign.get('telnyx_numbers', []))
        
        unique_phones = list(set(available_phones))
        
        # Check SIP trunk mappings
        sip_trunk_count = len(sip_trunk_mapping)
        
        # Check agent availability
        available_agents = len(agents_storage)
        
        # Check recent call success rate
        recent_calls = call_logs[-100:] if len(call_logs) > 100 else call_logs
        recent_successful = len([log for log in recent_calls if log.get('status') == 'initiated'])
        recent_success_rate = (recent_successful / max(1, len(recent_calls))) * 100 if recent_calls else 100
        
        # Determine overall health
        health_status = 'healthy'
        issues = []
        
        if len(running_campaigns) > 5:
            health_status = 'warning'
            issues.append(f'High number of running campaigns: {len(running_campaigns)}')
        
        if len(unique_phones) < 2:
            health_status = 'warning'  
            issues.append(f'Limited phone numbers available: {len(unique_phones)}')
        
        if recent_success_rate < 70:
            health_status = 'warning'
            issues.append(f'Low recent success rate: {recent_success_rate:.1f}%')
        
        if available_agents == 0:
            health_status = 'critical'
            issues.append('No agents available')
        
        if len(running_campaigns) > 10:
            health_status = 'critical'
            issues.append(f'Too many concurrent campaigns: {len(running_campaigns)}')
        
        health_data = {
            'status': health_status,
            'timestamp': datetime.now().isoformat(),
            'concurrent_calling': {
                'running_campaigns': len(running_campaigns),
                'available_phone_numbers': len(unique_phones),
                'sip_trunk_mappings': sip_trunk_count,
                'available_agents': available_agents,
                'recent_success_rate': round(recent_success_rate, 1),
                'recent_calls_analyzed': len(recent_calls)
            },
            'recommendations': {
                'max_concurrent_campaigns': 5,
                'recommended_phone_numbers': max(2, len(running_campaigns)),
                'optimal_success_rate': 85
            },
            'issues': issues,
            'active_campaigns': [
                {
                    'id': cid,
                    'name': campaigns_storage[cid].get('name', 'Unnamed'),
                    'started_at': campaigns_storage[cid].get('started_at'),
                    'contacts_count': len(campaigns_storage[cid].get('contacts', []))
                }
                for cid in running_campaigns
            ],
            'phone_numbers': unique_phones
        }
        
        return jsonify(health_data)
        
    except Exception as e:
        logger.error(f"Error checking concurrent calling health: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

def transfer_call_to_target(call_control_id: str, from_number: str, to_number: str, contact_name: str) -> bool:
    """Transfer a Telnyx call to the target number"""
    try:
        # Get Telnyx API key
        telnyx_api_key = os.getenv('TELNYX_API_KEY')
        if not telnyx_api_key:
            logger.error("❌ TELNYX_API_KEY not found in environment variables")
            return False
        
        # Prepare transfer request
        transfer_url = f"https://api.telnyx.com/v2/calls/{call_control_id}/actions/transfer"
        headers = {
            "Authorization": f"Bearer {telnyx_api_key}",
            "Content-Type": "application/json"
        }
        
        transfer_data = {
            "to": to_number,
            "from": from_number,
            "command_id": f"direct_{call_control_id}"
        }
        
        logger.info(f"📞 🔄 Transferring call {call_control_id} to {to_number} for {contact_name}")
        logger.info(f"📞 Transfer URL: {transfer_url}")
        logger.info(f"📞 Transfer data: {json.dumps(transfer_data, indent=2)}")
        
        # Make the transfer request
        response = requests.post(transfer_url, headers=headers, json=transfer_data, timeout=10)
        
        if response.status_code == 200:
            logger.info(f"📞 ✅ Transfer successful for {contact_name}: {response.status_code}")
            logger.info(f"📞 Transfer response: {response.text}")
            return True
        else:
            logger.error(f"📞 ❌ Transfer failed for {contact_name}: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"📞 ❌ Exception during call transfer for {contact_name}: {str(e)}")
        return False

# Telnyx webhook handler for real-time call status updates
@app.route('/api/webhooks/telnyx', methods=['POST'])
@app.route('/api/telnyx/webhook', methods=['POST'])  # Alternative endpoint for Telnyx webhook
def handle_telnyx_webhook():
    """Handle Telnyx webhook events for real-time call status tracking"""
    try:
        webhook_data = request.json
        logger.info(f"📞 Telnyx webhook received: {json.dumps(webhook_data, indent=2)}")
        
        if not webhook_data or 'data' not in webhook_data:
            logger.warning("⚠️ Invalid webhook data received")
            return jsonify({'status': 'error', 'message': 'Invalid webhook data'}), 400
        
        event_data = webhook_data['data']
        event_type = event_data.get('event_type')
        payload = event_data.get('payload', {})
        
        call_control_id = payload.get('call_control_id')
        from_number = payload.get('from')
        to_number = payload.get('to')
        call_session_id = payload.get('call_session_id')
        
        logger.info(f"📞 Processing event: {event_type} for call {call_control_id}")
        logger.info(f"📞 From: {from_number} -> To: {to_number}")
        
        # Find the corresponding call log entry
        call_log_entry = None
        for i, log in enumerate(call_logs):
            # Match by phone numbers and recent timestamp (within last hour)
            if (log.get('contact_phone') == to_number and 
                log.get('from_phone') == from_number):
                
                # Check if it's a recent call (within last hour)
                log_time = datetime.fromisoformat(log.get('timestamp', ''))
                time_diff = datetime.now() - log_time
                if time_diff.total_seconds() < 3600:  # Within 1 hour
                    call_log_entry = log
                    call_log_index = i
                    break
        
        if not call_log_entry:
            logger.warning(f"⚠️ No matching call log found for {from_number} -> {to_number}")
            # Still process the webhook for logging purposes
            return jsonify({'status': 'received', 'message': 'No matching call log found'})
        
        # Update call log based on event type
        original_status = call_log_entry.get('status', 'unknown')
        updated = False
        
        if event_type == 'call.initiated':
            logger.info(f"📞 Call initiated: {call_log_entry.get('contact_name', 'Unknown')}")
            call_logs[call_log_index]['status'] = 'initiated'
            call_logs[call_log_index]['telnyx_call_id'] = call_control_id
            call_logs[call_log_index]['call_session_id'] = call_session_id
            updated = True

            # Only transfer if we haven't already transferred this call
            if call_logs[call_log_index].get('transfer_status') != 'initiated':
                # Automatically transfer the call to complete the connection
                try:
                    transfer_success = transfer_call_to_target(call_control_id, from_number, to_number, call_log_entry.get('contact_name', 'Unknown'))
                    if transfer_success:
                        logger.info(f"📞 ✅ Call transfer initiated for {call_log_entry.get('contact_name', 'Unknown')}")
                        call_logs[call_log_index]['transfer_status'] = 'initiated'
                    else:
                        logger.error(f"📞 ❌ Call transfer failed for {call_log_entry.get('contact_name', 'Unknown')}")
                        call_logs[call_log_index]['transfer_status'] = 'failed'
                except Exception as e:
                    logger.error(f"📞 ❌ Error transferring call for {call_log_entry.get('contact_name', 'Unknown')}: {str(e)}")
                    call_logs[call_log_index]['transfer_status'] = 'error'
            else:
                logger.info(f"📞 ⏭️ Skipping transfer for {call_log_entry.get('contact_name', 'Unknown')} - already initiated")
            
        elif event_type == 'call.answered':
            logger.info(f"📞 ✅ CALL ANSWERED: {call_log_entry.get('contact_name', 'Unknown')} ({to_number})")
            call_logs[call_log_index]['status'] = 'answered'
            call_logs[call_log_index]['answered_at'] = datetime.now().isoformat()
            call_logs[call_log_index]['telnyx_call_id'] = call_control_id
            updated = True
            
        elif event_type == 'call.hangup':
            # Determine if call was answered or not
            duration = payload.get('duration_seconds', 0)
            hangup_cause = payload.get('hangup_cause', '')
            
            if duration > 5:  # Call lasted more than 5 seconds, likely answered
                if call_log_entry.get('status') != 'answered':
                    logger.info(f"📞 ✅ Call completed (answered): {call_log_entry.get('contact_name', 'Unknown')} - Duration: {duration}s")
                    call_logs[call_log_index]['status'] = 'answered'
                else:
                    logger.info(f"📞 Call completed: {call_log_entry.get('contact_name', 'Unknown')} - Duration: {duration}s")
            else:
                logger.info(f"📞 ❌ Call not answered: {call_log_entry.get('contact_name', 'Unknown')} - Cause: {hangup_cause}")
                call_logs[call_log_index]['status'] = 'no_answer'
            
            call_logs[call_log_index]['call_duration'] = duration
            call_logs[call_log_index]['hangup_cause'] = hangup_cause
            call_logs[call_log_index]['completed_at'] = datetime.now().isoformat()
            updated = True
            
        elif event_type == 'call.machine.detection.ended':
            detection = payload.get('result', '')
            if detection in ['human', 'human_likely']:
                logger.info(f"📞 👤 Human detected: {call_log_entry.get('contact_name', 'Unknown')}")
                call_logs[call_log_index]['machine_detection'] = 'human'
            else:
                logger.info(f"📞 🤖 Machine detected: {call_log_entry.get('contact_name', 'Unknown')} - {detection}")
                call_logs[call_log_index]['machine_detection'] = detection
                # If it's a machine, mark as no_answer
                if detection in ['machine', 'silence']:
                    call_logs[call_log_index]['status'] = 'no_answer'
            updated = True
            
        elif event_type == 'call.bridged':
            logger.info(f"📞 🌉 Call bridged: {call_log_entry.get('contact_name', 'Unknown')}")
            call_logs[call_log_index]['status'] = 'answered'  # Bridged means answered
            call_logs[call_log_index]['bridged_at'] = datetime.now().isoformat()
            updated = True
            
        elif event_type in ['call.speak.ended', 'call.speak.started']:
            logger.info(f"📞 🗣️ TTS event: {event_type} for {call_log_entry.get('contact_name', 'Unknown')}")
            # Don't change status for TTS events, just log
            
        else:
            logger.info(f"📞 Other event: {event_type} for {call_log_entry.get('contact_name', 'Unknown')}")
        
        # Save updated call logs if anything changed
        if updated:
            save_json_data(CALL_LOGS_FILE, call_logs)
            logger.info(f"📋 Updated call log: {call_log_entry.get('contact_name', 'Unknown')} - Status: {original_status} -> {call_logs[call_log_index]['status']}")
        
        # Add webhook event to call log for debugging
        if 'webhook_events' not in call_logs[call_log_index]:
            call_logs[call_log_index]['webhook_events'] = []
        
        call_logs[call_log_index]['webhook_events'].append({
            'event_type': event_type,
            'timestamp': datetime.now().isoformat(),
            'payload': payload
        })
        
        save_json_data(CALL_LOGS_FILE, call_logs)
        
        return jsonify({
            'status': 'success',
            'message': f'Webhook processed: {event_type}',
            'call_control_id': call_control_id,
            'updated': updated
        })
        
    except Exception as e:
        logger.error(f"❌ Error processing Telnyx webhook: {str(e)}", exc_info=True)
        return jsonify({'status': 'error', 'message': str(e)}), 500

def correct_call_statuses():
    """
    Correct call statuses based on actual call duration when webhooks fail.
    This is a fallback mechanism for when Telnyx webhooks don't deliver all events.
    """
    try:
        call_logs = load_json_data(CALL_LOGS_FILE)
        if not call_logs:
            return

        updated_count = 0
        for i, call_log in enumerate(call_logs):
            # Check if call has real_call_duration but wrong status
            real_duration = call_log.get('real_call_duration', 0)
            current_status = call_log.get('status', '')
            call_duration = call_log.get('call_duration', 0)

            # If real_call_duration > 30 seconds but status is not 'answered' or 'completed'
            if real_duration > 30 and current_status in ['no_answer', 'initiated', 'failed']:
                logger.info(f"🔧 Correcting call status for {call_log.get('contact_name', 'Unknown')}: "
                           f"{current_status} -> answered (duration: {real_duration}s)")
                call_logs[i]['status'] = 'answered'
                call_logs[i]['call_duration'] = real_duration
                call_logs[i]['corrected_by_fallback'] = True
                call_logs[i]['corrected_at'] = datetime.now().isoformat()
                updated_count += 1

            # If real_call_duration < 10 seconds and status is not already no_answer
            elif real_duration < 10 and current_status not in ['no_answer', 'failed']:
                logger.info(f"🔧 Correcting call status for {call_log.get('contact_name', 'Unknown')}: "
                           f"{current_status} -> no_answer (duration: {real_duration}s)")
                call_logs[i]['status'] = 'no_answer'
                call_logs[i]['call_duration'] = real_duration
                call_logs[i]['corrected_by_fallback'] = True
                call_logs[i]['corrected_at'] = datetime.now().isoformat()
                updated_count += 1

        if updated_count > 0:
            save_json_data(CALL_LOGS_FILE, call_logs)
            logger.info(f"🔧 Corrected {updated_count} call statuses based on actual duration")

        return updated_count

    except Exception as e:
        logger.error(f"❌ Error correcting call statuses: {str(e)}", exc_info=True)
        return 0

@app.route('/api/webhooks/telnyx/test', methods=['POST', 'GET'])
def test_telnyx_webhook():
    """Test endpoint for Telnyx webhook configuration"""
    if request.method == 'GET':
        return jsonify({
            'status': 'ready',
            'message': 'Telnyx webhook endpoint is ready',
            'timestamp': datetime.now().isoformat(),
            'endpoint': '/api/webhooks/telnyx'
        })

    # Handle test webhook
    return jsonify({
        'status': 'test_received',
        'data': request.json,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/call-status/correct', methods=['POST'])
def correct_call_statuses_endpoint():
    """Manually trigger call status correction based on actual durations"""
    try:
        corrected_count = correct_call_statuses()
        return jsonify({
            'success': True,
            'message': f'Corrected {corrected_count} call statuses',
            'corrected_count': corrected_count
        })
    except Exception as e:
        logger.error(f"❌ Error in call status correction endpoint: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    # Load environment variables at startup
    load_env_variables()
    
    # Start the server
    app.run(host='0.0.0.0', port=9090, debug=True, use_reloader=False)
