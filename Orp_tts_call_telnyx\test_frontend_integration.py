#!/usr/bin/env python3
"""
Test script for Enhanced Frontend Integration
Tests the new enhanced statistics and frontend features
"""

import requests
import json
import time

BACKEND_URL = "http://localhost:9090"
FRONTEND_URL = "http://localhost:3000"

def test_enhanced_endpoints():
    """Test the enhanced backend endpoints"""
    print("🧪 Testing Enhanced Backend Endpoints")
    print("=" * 50)
    
    try:
        # Test campaigns endpoint
        campaigns_response = requests.get(f"{BACKEND_URL}/api/campaigns")
        if campaigns_response.status_code == 200:
            campaigns = campaigns_response.json()
            print(f"✅ Campaigns endpoint: {len(campaigns)} campaigns found")
            
            if campaigns:
                # Get first campaign ID
                campaign_id = None
                if isinstance(campaigns, dict):
                    campaign_id = list(campaigns.keys())[0]
                elif isinstance(campaigns, list) and campaigns:
                    campaign_id = campaigns[0].get('id', campaigns[0].get('campaign_id'))
                
                if campaign_id:
                    print(f"📊 Testing enhanced statistics for: {campaign_id}")
                    
                    # Test enhanced statistics
                    stats_response = requests.get(f"{BACKEND_URL}/api/campaigns/{campaign_id}/statistics/enhanced")
                    if stats_response.status_code == 200:
                        stats = stats_response.json()
                        if stats.get('success'):
                            data = stats['data']
                            print(f"✅ Enhanced Statistics:")
                            print(f"   📈 Progress: {data['progress']['progress_percentage']:.1f}%")
                            print(f"   🎯 Success Rate: {data['summary']['success_rate']:.1f}%")
                            print(f"   💰 Total Cost: ${data['summary']['total_cost']:.4f}")
                            print(f"   ⏱️ Duration: {data['summary']['total_duration_minutes']:.1f}m")
                            print(f"   📞 Calls Remaining: {data['summary']['calls_remaining']}")
                            print(f"   ♻️ Recyclable Contacts: {data['recyclable_contacts']['count']}")
                            
                            # Test button states
                            print(f"🔘 Button States:")
                            for action, state in data['button_states'].items():
                                status = "✅" if state['enabled'] else "❌"
                                print(f"   {status} {action.capitalize()}: {state['tooltip']}")
                        else:
                            print(f"❌ Enhanced statistics failed: {stats.get('message')}")
                    else:
                        print(f"❌ Enhanced statistics endpoint failed: {stats_response.status_code}")
                else:
                    print("⚠️ No valid campaign ID found")
            else:
                print("⚠️ No campaigns found")
        else:
            print(f"❌ Campaigns endpoint failed: {campaigns_response.status_code}")
    
    except Exception as e:
        print(f"❌ Backend test error: {e}")

def test_frontend_accessibility():
    """Test if frontend is accessible"""
    print("\n🌐 Testing Frontend Accessibility")
    print("=" * 50)
    
    try:
        response = requests.get(FRONTEND_URL, timeout=5)
        if response.status_code == 200:
            print("✅ Frontend is accessible at http://localhost:3000")
            print("📱 Enhanced Campaign Monitor should be available")
            print("🎨 New features include:")
            print("   • Enhanced statistics with cost tracking")
            print("   • Smart action buttons (Start/Pause/Restart/Recycle)")
            print("   • Progress percentage visualization")
            print("   • Recyclable contacts management")
            print("   • Real-time call status updates")
        else:
            print(f"❌ Frontend not accessible: {response.status_code}")
    except Exception as e:
        print(f"⚠️ Frontend accessibility test: {e}")

def test_webhook_functionality():
    """Test webhook processing"""
    print("\n📞 Testing Enhanced Webhook Processing")
    print("=" * 50)
    
    try:
        # Test Telnyx webhook (main endpoint)
        webhook_data = {
            "data": {
                "event_type": "call.initiated",
                "payload": {
                    "call_control_id": "test_call_" + str(int(time.time())),
                    "to": "+917420068477",
                    "from": "+19858539054"
                }
            }
        }
        
        response = requests.post(f"{BACKEND_URL}/api/telnyx/webhook", json=webhook_data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Telnyx webhook processed: {result}")
            if result.get('transfer_initiated'):
                print("🔄 Call transfer was initiated successfully")
            else:
                print("ℹ️ Webhook processed (transfer may require real call)")
        else:
            print(f"❌ Webhook test failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Webhook test error: {e}")

def main():
    print("🚀 Enhanced Campaign System Integration Test")
    print("=" * 60)
    print("Testing the complete enhanced system with:")
    print("✨ CALLBOT2.1-inspired statistics")
    print("🎯 Accurate call tracking")
    print("🔄 Automatic call transfer")
    print("♻️ Smart contact recycling")
    print("🔘 Dynamic action buttons")
    print("💰 Real-time cost tracking")
    print()
    
    # Wait a moment for services to start
    print("⏳ Waiting for services to initialize...")
    time.sleep(3)
    
    # Run tests
    test_enhanced_endpoints()
    test_frontend_accessibility()
    test_webhook_functionality()
    
    print("\n🎉 Integration Test Complete!")
    print("\n📋 Next Steps:")
    print("1. Open http://localhost:3000 to see the enhanced frontend")
    print("2. Navigate to Campaign Monitor to see new features")
    print("3. Test campaign actions using the smart buttons")
    print("4. Monitor real-time statistics and cost tracking")
    print("5. Use the Recyclable tab to retry failed calls")

if __name__ == "__main__":
    main() 