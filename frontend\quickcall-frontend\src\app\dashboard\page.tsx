"use client"

import { useState, useEffect, useRef } from "react"
import Link from "next/link"
import { useRout<PERSON> } from "next/navigation"
import { WebCallingInterface } from "@/components/WebCallingInterface"
import { CampaignMonitor } from "@/components/CampaignMonitor"
import { EnhancedCampaignStats } from "@/components/EnhancedCampaignStats"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Phone, Play, Settings, Users, Mic, CheckCircle, XCircle, Loader2, Plus, Edit, Trash2, Download, Upload, MessageSquare, Pause, Volume2, ChevronDown, ChevronUp, BarChart3, RotateCcw, RefreshCw, Clock } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import ttsService from "@/services/ttsService"

// API URL configuration
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:9090'

// Mock services - replace with actual implementations
// Mock services - activateAgent and deactivateAgent are now handled by direct API calls.
// getAgents might be replaced later if fetching agents from backend is implemented.
const agentService = {
  getAgents: async () => [], 
}

const callService = {
  startCall: async (params: any) => ({ id: "call-123", success: true }),
}

interface Agent {
  id: string
  name: string
  description: string
  systemPrompt: string
  emotionalPrompt: string
  modelId: string
  voiceId: string
  interruptSpeechDuration: number
  allowInterruptions: boolean
  initialGreeting: string
  useInitialGreeting: boolean
  isActive: boolean
}

export default function Dashboard() {
  const [selectedAgent, setSelectedAgent] = useState<string>("")
  const [selectedVoice, setSelectedVoice] = useState("elise")
  const [selectedModel, setSelectedModel] = useState("llama-3.3-70b-versatile")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const [activatingAgent, setActivatingAgent] = useState<string | null>(null)
  const [deactivatingAgent, setDeactivatingAgent] = useState<string | null>(null)
  const [callPhoneNumber, setCallPhoneNumber] = useState("")
  const [isMakingCall, setIsMakingCall] = useState(false)
  const [callStatusMessage, setCallStatusMessage] = useState("")

  // Voice testing state
  const [testText, setTestText] = useState(
    "Hello, this is a test of the text-to-speech system. How does this voice sound?",
  )
  const [testVoice, setTestVoice] = useState("elise")
  const [audioUrl, setAudioUrl] = useState<string | null>(null)
  const [isTesting, setIsTesting] = useState(false)
  const [testError, setTestError] = useState("")
  const audioRef = useRef<HTMLAudioElement>(null)

  // Agents loaded from backend
  const [agents, setAgents] = useState<Agent[]>([])

  // Sample voice options
  const voices = [
    { id: "Tara", name: "Tara", description: "Warm female voice" },
    { id: "Elise", name: "Elise", description: "Professional female voice" },
  ]

  // Sample model options
  const models = [
    { id: "llama-3.3-70b-versatile", name: "Llama 3.3 70B", description: "High-performance large language model" },
    { id: "gpt-4o", name: "GPT-4o", description: "Powerful multimodal model" },
    { id: "claude-3.5-sonnet", name: "Claude 3.5 Sonnet", description: "Fast and capable model" },
    { id: "mixtral-8x7b", name: "Mixtral 8x7B", description: "Powerful mixture-of-experts model" },
  ]

  const router = useRouter()

  // Agent management states
  const [isAddingAgent, setIsAddingAgent] = useState(false)
  const [editingAgent, setEditingAgent] = useState<Agent | null>(null)
  const [deletingAgent, setDeletingAgent] = useState<string | null>(null)
  const [newAgent, setNewAgent] = useState({
    name: "",
    description: "",
    systemPrompt: "",
    emotionalPrompt: `You are a conversational AI designed to be engaging, professional, and human-like, delivering responses that are concise (one or two sentences) and infused with subtle emotional cues to mimic natural conversation. Use the following text-based tags to convey emotions naturally, without emojis:

<giggle>: Indicates lighthearted amusement or a gentle chuckle, used for mildly funny or cheerful moments.
<laugh>: Represents genuine laughter for something truly humorous, used sparingly for stronger amusement.
<chuckle>: Denotes a quiet or subtle laugh, often for something mildly amusing or thoughtful.
Guidelines for Using Emotional Tags
Natural Integration: Incorporate tags seamlessly into sentences, as a person might in spoken or written conversation.
Show, Don't Tell: Use tags to express emotions instead of stating them (e.g., instead of "I'm happy," use <giggle> in a cheerful response).
Contextual Appropriateness: Match tags to the conversation's tone and context, ensuring they are professional and relevant.
Sparsity and Subtlety: Use tags sparingly, typically no more than once per response, to avoid overuse or exaggeration and maintain a natural flow.
Professional Tone: Avoid excessive laughter, overly emotional responses, or inappropriate tones (e.g., no sexual undertones or forced humor).
Objective: Create engaging, human-like responses that feel warm, friendly, and professional, fostering trust and relatability with the user.
Example Usage
Greeting: "Great to connect, <chuckle>! Got a moment to chat?"</chuckle>
Clarification: "Thanks for sharing, <chuckle>! Could you tell me a bit more?"</chuckle>
Handling Hesitation: "I totally get it, <giggle>. What's most important to you right now?"</giggle>`,
    modelId: "llama-3.3-70b-versatile",
    voiceId: "Tara",
    interruptSpeechDuration: 1.0,
    allowInterruptions: true,
    initialGreeting: "Hello! How can I help you today?",
    useInitialGreeting: true
  })

  // Batch calling states
  const [csvFile, setCsvFile] = useState<File | null>(null)
  const [csvData, setCsvData] = useState<any[]>([])
  const [batchProgress, setBatchProgress] = useState<{current: number, total: number, status: string}>({current: 0, total: 0, status: 'idle'})
  const [processingBatch, setProcessingBatch] = useState(false)

  // Model management
  const [availableModels, setAvailableModels] = useState<Array<{id: string, owned_by: string, context_window?: number}>>([])
  const [loadingModels, setLoadingModels] = useState(false)
  
  // Voice management
  const [availableVoices, setAvailableVoices] = useState<Array<{id: string, name: string}>>([])
  const [loadingVoices, setLoadingVoices] = useState(false)

  // Campaign management states
  const [campaigns, setCampaigns] = useState<Array<{
    id: string
    name: string
    status: string
    telnyxNumbers: string[]
    agentId: string
    agentName: string
    agentVoice: string
    firstMessage: string
    createdAt: string
    contactsCount: number
  }>>([])
  const [isCreatingCampaign, setIsCreatingCampaign] = useState(false)
  
  // Campaign statistics and status tracking
  const [campaignStats, setCampaignStats] = useState<{[key: string]: any}>({})
  const [loadingStats, setLoadingStats] = useState<{[key: string]: boolean}>({})
  const [expandedCampaign, setExpandedCampaign] = useState<string | null>(null)
  const [recyclingCampaign, setRecyclingCampaign] = useState<string | null>(null)
  const [monitoringCampaign, setMonitoringCampaign] = useState<string | null>(null)
  const [statsModalCampaign, setStatsModalCampaign] = useState<string | null>(null)

  // Contacts and call logs states
  const [contacts, setContacts] = useState<Array<{
    phone_number: string
    name: string
    notes: string
    total_calls: number
    successful_calls: number
    failed_calls: number
    last_called: string
    campaigns: string[]
  }>>([])
  const [callLogs, setCallLogs] = useState<Array<{
    id: string
    timestamp: string
    contact_name: string
    contact_phone: string
    from_phone: string
    agent_name: string
    campaign_id: string
    status: string
    notes: string
    call_duration: number
    recording_url: string
  }>>([])
  const [loadingContacts, setLoadingContacts] = useState(false)
  const [selectedContact, setSelectedContact] = useState<string | null>(null)
  const [telnyxNumbers, setTelnyxNumbers] = useState<Array<{
    id: string, 
    phone_number: string,
    sip_trunk_id?: string,
    has_custom_trunk?: boolean
  }>>([])
  const [loadingTelnyxNumbers, setLoadingTelnyxNumbers] = useState(false)
  const [sipTrunkMapping, setSipTrunkMapping] = useState<{[key: string]: string}>({})
  const [newCampaign, setNewCampaign] = useState({
    name: "",
    telnyxNumbers: [] as string[], // Multiple numbers instead of single
    agentId: "", // Select existing agent instead of separate prompts
    firstMessage: "",
    contacts: [] as Array<{name: string, phone_number: string, notes: string}>
  })

  // Add new state variables and functions after existing state declarations

  // ... existing code ...

  // Add new state for modal and campaign actions
  const [showCreateCampaignModal, setShowCreateCampaignModal] = useState(false)

  const [stoppingCampaign, setStoppingCampaign] = useState<string | null>(null)
  const [restartingCampaign, setRestartingCampaign] = useState<string | null>(null)

  // ... existing code ...

  // Add function to handle campaign restart (different from recycle)
  const handleRestartCampaign = async (campaignId: string) => {
    try {
      setRestartingCampaign(campaignId)
      
      // First stop the campaign if it's running
      const stopResponse = await fetch(`${API_URL}/api/campaigns/${campaignId}/stop`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })
      
      if (!stopResponse.ok) {
        throw new Error('Failed to stop campaign')
      }
      
      // Wait a moment for the stop to process
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Then start it again
      const startResponse = await fetch(`${API_URL}/api/campaigns/${campaignId}/start`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })
      
      if (!startResponse.ok) {
        throw new Error('Failed to restart campaign')
      }
      
      setSuccess('Campaign restarted successfully')
      await fetchCampaigns()
      
    } catch (error) {
      setError(`Failed to restart campaign: ${error}`)
    } finally {
      setRestartingCampaign(null)
    }
  }

  // Enhanced stop campaign function
  const handleStopCampaignEnhanced = async (campaignId: string) => {
    try {
      setStoppingCampaign(campaignId)
      await handleStopCampaign(campaignId)
    } finally {
      setStoppingCampaign(null)
    }
  }

  // Function to test backend connectivity
  const testBackendConnection = async () => {
    try {
      const response = await fetch(`${API_URL}/api/health`)
      if (response.ok) {
        setSuccess('Backend connection successful!')
      } else {
        setError('Backend is running but returned an error')
      }
    } catch (error) {
      setError('Cannot connect to backend. Make sure it\'s running on port 9090.')
    }
  }

  // Enhanced create campaign function that closes modal
  const handleCreateCampaignModal = async () => {
    try {
      await handleCreateCampaign()
      setShowCreateCampaignModal(false) // Close modal on success
      setNewCampaign({
        name: "",
        telnyxNumbers: [],
        agentId: "",
        firstMessage: "",
        contacts: []
      }) // Reset form
    } catch (error) {
      // Error handling is already in handleCreateCampaign
    }
  }

  // Function to handle opening stats modal
  const handleOpenStatsModal = async (campaignId: string) => {
    setStatsModalCampaign(campaignId)
  }

  // ... existing code ...

  // Fetch Groq models
  const fetchGroqModels = async () => {
    try {
      setLoadingModels(true)
      const response = await fetch(`${API_URL}/api/models/groq`)
      if (response.ok) {
        const data = await response.json()
        setAvailableModels(data.models || [])
      } else {
        throw new Error('Failed to fetch models')
      }
    } catch (error) {
      console.error('Error fetching Groq models:', error)
      setError('Failed to fetch available models')
    } finally {
      setLoadingModels(false)
    }
  }

  // Fetch available voices
  const fetchVoices = async () => {
    try {
      setLoadingVoices(true)
      const response = await fetch(`${API_URL}/api/voices`)
      if (response.ok) {
        const data = await response.json()
        setAvailableVoices(data.voices || [])
      } else {
        throw new Error('Failed to fetch voices')
      }
    } catch (error) {
      console.error('Error fetching voices:', error)
      setError('Failed to fetch available voices')
    } finally {
      setLoadingVoices(false)
    }
  }

  // Load models on component mount
  useEffect(() => {
    fetchGroqModels()
    fetchVoices()
    fetchTelnyxNumbers()
    fetchCampaigns()
    fetchSipTrunkMapping()
  }, [])

  // Fetch Telnyx numbers
  const fetchTelnyxNumbers = async () => {
    try {
      setLoadingTelnyxNumbers(true)
      const response = await fetch(`${API_URL}/api/telnyx/numbers`)
      if (response.ok) {
        const data = await response.json()
        setTelnyxNumbers(data.numbers || [])
        
        // If there's an error (mock data), show it
        if (data.error) {
          console.warn('Telnyx API Warning:', data.error)
        }
      } else {
        throw new Error('Failed to fetch Telnyx numbers')
      }
    } catch (error) {
      console.error('Error fetching Telnyx numbers:', error)
      setError('Failed to fetch available phone numbers')
    } finally {
      setLoadingTelnyxNumbers(false)
    }
  }

  // Fetch SIP trunk mappings
  const fetchSipTrunkMapping = async () => {
    try {
      const response = await fetch(`${API_URL}/api/sip-trunks`)
      if (response.ok) {
        const data = await response.json()
        setSipTrunkMapping(data.mapping || {})
      } else {
        throw new Error('Failed to fetch SIP trunk mapping')
      }
    } catch (error) {
      console.error('Error fetching SIP trunk mapping:', error)
    }
  }

  // Fetch campaigns
  const fetchCampaigns = async () => {
    try {
      const response = await fetch(`${API_URL}/api/campaigns`)
      if (response.ok) {
        const data = await response.json()
        const campaignsList = data
        setCampaigns(campaignsList)
        
        // Automatically fetch stats for all campaigns
        if (campaignsList && campaignsList.length > 0) {
          campaignsList.forEach((campaign: any) => {
            fetchCampaignStats(campaign.id)
          })
        }
      } else {
        throw new Error('Failed to fetch campaigns')
      }
    } catch (error) {
      console.error('Error fetching campaigns:', error)
      setError('Failed to fetch campaigns')
    }
  }

  // Fetch contacts
  const fetchContacts = async () => {
    try {
      setLoadingContacts(true)
      const response = await fetch(`${API_URL}/api/contacts`)
      if (response.ok) {
        const data = await response.json()
        setContacts(data.contacts || [])
      } else {
        throw new Error('Failed to fetch contacts')
      }
    } catch (error) {
      console.error('Error fetching contacts:', error)
      setError('Failed to fetch contacts')
    } finally {
      setLoadingContacts(false)
    }
  }

  // Fetch call logs
  const fetchCallLogs = async (contactPhone?: string) => {
    try {
      const url = contactPhone 
        ? `${API_URL}/api/contacts/${encodeURIComponent(contactPhone)}/calls`
        : `${API_URL}/api/call-logs`
      const response = await fetch(url)
      if (response.ok) {
        const data = await response.json()
        setCallLogs(data.calls || data.call_logs || [])
      } else {
        throw new Error('Failed to fetch call logs')
      }
    } catch (error) {
      console.error('Error fetching call logs:', error)
    }
  }

  // Create campaign
  const handleCreateCampaign = async () => {
    if (!newCampaign.name || newCampaign.telnyxNumbers.length === 0 || !newCampaign.agentId) {
      setError('Please fill in all required fields: name, at least one phone number, and agent')
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch(`${API_URL}/api/campaigns`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newCampaign)
      })

      if (response.ok) {
        const result = await response.json()
        setSuccess('Campaign created successfully!')
        setIsCreatingCampaign(false)
        setNewCampaign({
          name: "",
          telnyxNumbers: [],
          agentId: "",
          firstMessage: "",
          contacts: []
        })
        fetchCampaigns() // Refresh campaigns list
      } else {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create campaign')
      }
    } catch (error) {
      console.error('Error creating campaign:', error)
      setError(error instanceof Error ? error.message : 'Failed to create campaign')
    } finally {
      setIsLoading(false)
    }
  }

  // Start campaign
  const handleStartCampaign = async (campaignId: string) => {
    try {
      const response = await fetch(`${API_URL}/api/campaigns/${campaignId}/start`, {
        method: 'POST'
      })

      if (response.ok) {
        setSuccess('Campaign started successfully!')
        fetchCampaigns() // Refresh campaigns list
      } else {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to start campaign')
      }
    } catch (error) {
      console.error('Error starting campaign:', error)
      setError(error instanceof Error ? error.message : 'Failed to start campaign')
    }
  }

  // Stop campaign
  const handleStopCampaign = async (campaignId: string) => {
    try {
      const response = await fetch(`${API_URL}/api/campaigns/${campaignId}/stop`, {
        method: 'POST'
      })

      if (response.ok) {
        setSuccess('Campaign stopped successfully!')
        fetchCampaigns() // Refresh campaigns list
      } else {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to stop campaign')
      }
    } catch (error) {
      console.error('Error stopping campaign:', error)
      setError(error instanceof Error ? error.message : 'Failed to stop campaign')
    }
  }

  // Delete campaign
  const handleDeleteCampaign = async (campaignId: string) => {
    if (!confirm('Are you sure you want to delete this campaign?')) return

    try {
      const response = await fetch(`${API_URL}/api/campaigns/${campaignId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        setSuccess('Campaign deleted successfully!')
        fetchCampaigns() // Refresh campaigns list
      } else {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete campaign')
      }
    } catch (error) {
      console.error('Error deleting campaign:', error)
      setError(error instanceof Error ? error.message : 'Failed to delete campaign')
    }
  }

  // Fetch campaign statistics with accurate data
  const fetchCampaignStats = async (campaignId: string) => {
    try {
      setLoadingStats(prev => ({...prev, [campaignId]: true}))
      
      // Try the enhanced statistics API first
      const enhancedResponse = await fetch(`${API_URL}/api/campaigns/${campaignId}/statistics/enhanced`)
      if (enhancedResponse.ok) {
        const enhancedData = await enhancedResponse.json()
        // Convert enhanced data to stats format for compatibility
        const stats = {
          totals: enhancedData.totals,
          call_statistics: enhancedData.totals,
          progress_percent: enhancedData.progress_percent,
          answer_rate: enhancedData.totals.total_calls > 0 
            ? (enhancedData.totals.completed_calls / enhancedData.totals.total_calls) * 100 
            : 0,
          success_rate: enhancedData.totals.total_calls > 0 
            ? (enhancedData.totals.completed_calls / enhancedData.totals.total_calls) * 100 
            : 0,
          enhanced: true
        }
        setCampaignStats(prev => ({...prev, [campaignId]: stats}))
      } else {
        // Try the monitoring API
        const monitorResponse = await fetch(`${API_URL}/api/campaigns/${campaignId}/monitor`)
        if (monitorResponse.ok) {
          const monitorData = await monitorResponse.json()
          // Convert monitoring data to stats format
          const stats = {
            totals: {
              total_calls: monitorData.contacts.called,
              completed_calls: monitorData.contacts.answered,
              initiated_calls: monitorData.contacts.initiated,
              failed_calls: monitorData.contacts.failed,
              no_answer_calls: monitorData.contacts.no_answer
            },
            call_statistics: {
              total_calls: monitorData.contacts.called,
              completed_calls: monitorData.contacts.answered,
              initiated_calls: monitorData.contacts.initiated,
              failed_calls: monitorData.contacts.failed,
              no_answer_calls: monitorData.contacts.no_answer
            },
            progress_percent: (monitorData.contacts.called / monitorData.contacts.total) * 100,
            answer_rate: monitorData.answer_rate,
            success_rate: monitorData.success_rate
          }
          setCampaignStats(prev => ({...prev, [campaignId]: stats}))
        } else {
          // Fallback to old statistics API
          const response = await fetch(`${API_URL}/api/campaigns/${campaignId}/statistics`)
          if (response.ok) {
            const stats = await response.json()
            setCampaignStats(prev => ({...prev, [campaignId]: stats}))
          } else {
            console.error('Failed to fetch campaign stats')
          }
        }
      }
    } catch (error) {
      console.error('Error fetching campaign stats:', error)
    } finally {
      setLoadingStats(prev => ({...prev, [campaignId]: false}))
    }
  }

  // Handle campaign expansion/collapse
  const handleToggleCampaign = (campaignId: string) => {
    if (expandedCampaign === campaignId) {
      setExpandedCampaign(null)
    } else {
      setExpandedCampaign(campaignId)
      if (!campaignStats[campaignId]) {
        fetchCampaignStats(campaignId)
      }
    }
  }

  // Recycle unresponsive contacts
  const handleRecycleUnresponsive = async (campaignId: string, createNew: boolean = false) => {
    try {
      setRecyclingCampaign(campaignId)
      const response = await fetch(`${API_URL}/api/campaigns/${campaignId}/recycle`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          min_retry_interval_hours: 0.5,  // 30 minutes
          create_new_campaign: createNew
        })
      })
      
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setSuccess(`${createNew ? 'New recycled campaign created' : 'Campaign recycled'} with ${result.eligible_for_retry} contacts`)
          fetchCampaigns()
          if (!createNew) {
            // Refresh stats for recycled campaign
            setTimeout(() => fetchCampaignStats(campaignId), 1000)
          }
        } else {
          setError(result.error || 'Failed to recycle campaign')
        }
      } else {
        const errorData = await response.json()
        setError(errorData.error || 'Failed to recycle campaign')
      }
    } catch (error) {
      console.error('Error recycling campaign:', error)
      setError('Failed to recycle campaign')
    } finally {
      setRecyclingCampaign(null)
    }
  }

  // Auto-refresh campaigns and stats every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      fetchCampaigns() // This will also refresh stats for all campaigns
    }, 30000) // 30 seconds

    return () => clearInterval(interval)
  }, [])

  // Load agents on component mount
  useEffect(() => {
    const fetchAgents = async () => {
      try {
        const response = await fetch(`${API_URL}/api/agents`)
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        const data = await response.json()
        
        // Extract agents array from response (backend returns {agents: []})
        const agentsArray = data.agents || data
        
        // Transform backend agent data to frontend format
        const transformedAgents = agentsArray.map((agent: any) => ({
          id: agent.id,
          name: agent.name,
          description: agent.description,
          systemPrompt: agent.systemPrompt,
          emotionalPrompt: agent.emotionalPrompt,
          modelId: agent.modelId,
          voiceId: agent.voiceId,
          interruptSpeechDuration: agent.interruptSpeechDuration,
          allowInterruptions: agent.allowInterruptions,
          initialGreeting: agent.initialGreeting,
          useInitialGreeting: agent.useInitialGreeting,
          isActive: agent.isActive
        }))
        
        if (transformedAgents && transformedAgents.length > 0) {
          setAgents(transformedAgents)

          // Find active agents
          const activeAgents = transformedAgents.filter((agent: Agent) => agent.isActive)

          // If there are active agents, select the first active agent
          // Otherwise, just select the first agent (they'll need to activate it)
          if (activeAgents.length > 0) {
            setSelectedAgent(activeAgents[0].id)
          } else {
            setSelectedAgent(transformedAgents[0].id)
          }
        }
      } catch (err) {
        console.error("Error fetching agents:", err)
        setError('Failed to load agents from backend')
      }
    }

    fetchAgents()
    fetchContacts()
    fetchCampaigns()
  }, [])

  // CSV template download
  const downloadCsvTemplate = () => {
    // Create CSV content with plain numbers - users need to format phone_number column as TEXT in Excel
    const csvContent = `name,phone_number,notes
"John Doe","19199256164","Interested in our services"
"Jane Smith","18162196532","Follow-up call needed"
"Mike Johnson","19858539054","Demo request"`
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', 'campaign_contacts_template.csv')
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // Helper function to parse phone numbers properly
  const parsePhoneNumber = (rawValue: string): string => {
    if (!rawValue) return ''
    
    let phoneNumber = rawValue.toString().trim()
    
    // Handle scientific notation (e.g., 9.1742E+11)
    if (phoneNumber.includes('E') || phoneNumber.includes('e')) {
      const num = parseFloat(phoneNumber)
      if (!isNaN(num) && num > 0) {
        // Convert to full number string
        phoneNumber = num.toLocaleString('fullwide', { useGrouping: false })
      }
    }
    
    // Remove any non-digit characters except + (removes dashes, spaces, parentheses, etc.)
    phoneNumber = phoneNumber.replace(/[^\d+]/g, '')
    
    // If it looks like an Indian number without country code, add +91
    if (phoneNumber.length === 10 && (phoneNumber.startsWith('7420') || phoneNumber.startsWith('8') || phoneNumber.startsWith('9'))) {
      phoneNumber = '+91' + phoneNumber
    }
    // For other international numbers, add + if missing
    else if (phoneNumber.length >= 10 && !phoneNumber.startsWith('+')) {
      phoneNumber = '+' + phoneNumber
    }
    
    return phoneNumber
  }

  const handleCsvUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setCsvFile(file)
    
    const reader = new FileReader()
    reader.onload = (e) => {
      const text = e.target?.result as string
      
      try {
        // Parse CSV more carefully to preserve phone numbers
        const lines = text.split('\n').filter(line => line.trim())
        if (lines.length === 0) {
          setError('CSV file is empty')
          return
        }
        
        const headers = lines[0].split(',').map(h => h.trim().toLowerCase().replace(/['"]/g, ''))
        
        // Find the indices of required columns
        const nameIndex = headers.findIndex(h => h.includes('name'))
        const phoneIndex = headers.findIndex(h => h.includes('phone'))
        const notesIndex = headers.findIndex(h => h.includes('notes') || h.includes('note'))
        
        if (nameIndex === -1 || phoneIndex === -1) {
          setError('CSV must contain "name" and "phone_number" columns')
          return
        }
        
        const contacts = lines.slice(1).map((line, index) => {
          // More robust CSV parsing that handles quoted values
          const values: string[] = []
          let current = ''
          let inQuotes = false
          
          for (let i = 0; i < line.length; i++) {
            const char = line[i]
            if (char === '"') {
              inQuotes = !inQuotes
            } else if (char === ',' && !inQuotes) {
              values.push(current.trim())
              current = ''
            } else {
              current += char
            }
          }
          values.push(current.trim())
          
          // Clean up the values
          const cleanValues = values.map(v => v.replace(/^["']|["']$/g, '').trim())
          
                     // Get phone number and parse it properly
           const phoneNumber = parsePhoneNumber(cleanValues[phoneIndex] || '')
          
          const contact = {
            id: `contact-${index}`,
            name: cleanValues[nameIndex] || `Contact ${index + 1}`,
            phone_number: phoneNumber,
            notes: notesIndex !== -1 ? (cleanValues[notesIndex] || '') : ''
          }
          
          return contact
        }).filter(contact => contact.name && contact.phone_number)
        
        if (contacts.length === 0) {
          setError('No valid contacts found in CSV file')
          return
        }
        
        setCsvData(contacts)
        setError('') // Clear any previous errors
        
      } catch (error) {
        console.error('Error parsing CSV:', error)
        setError('Error parsing CSV file. Please check the format.')
      }
    }
    
    reader.readAsText(file)
  }

  // Process batch calls with names in system prompt
  const processBatchCalls = async () => {
    if (!csvData.length || !selectedAgent) {
      setError('Please upload CSV data and select an agent')
      return
    }

    try {
      setProcessingBatch(true)
      setBatchProgress({current: 0, total: csvData.length, status: 'processing'})
      
      for (let i = 0; i < csvData.length; i++) {
        const contact = csvData[i]
        setBatchProgress({current: i + 1, total: csvData.length, status: `Calling ${contact.name}...`})
        
        // Get the selected agent for the base system prompt
        const selectedAgentData = agents.find(a => a.id === selectedAgent)
        if (!selectedAgentData) {
          throw new Error('Selected agent not found')
        }

        // Create personalized system prompt with contact name and emotional prompt
        const personalizedPrompt = `${selectedAgentData.emotionalPrompt}\n\n${selectedAgentData.systemPrompt}\n\nYou are speaking with ${contact.name}. ${contact.notes ? `Additional context: ${contact.notes}` : ''}`

        // Create a temporary agent for this specific call
        const tempAgentResponse = await fetch(`${API_URL}/api/agents`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: `${selectedAgentData.name}_${contact.name.replace(' ', '_')}`,
            description: `Temporary agent for calling ${contact.name}`,
            systemPrompt: personalizedPrompt,
          }),
        })

        if (!tempAgentResponse.ok) {
          throw new Error(`Failed to create temporary agent for ${contact.name}`)
        }

        const tempAgent = await tempAgentResponse.json()
        
        // Activate the temporary agent
                  const activateResponse = await fetch(`${API_URL}/activate_agent`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            agent_id: tempAgent.agent_id,
          }),
        })

        if (!activateResponse.ok) {
          throw new Error(`Failed to activate agent for ${contact.name}`)
        }

        // Wait a moment for agent to be ready
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        // Make the call
        const response = await fetch(`${API_URL}/make_call`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            agent_name_for_dispatch: `${selectedAgentData.name}_${contact.name.replace(' ', '_')}`,
            agent_id: tempAgent.agent_id,
            phone_number: contact.phone_number,
          }),
        })

        if (!response.ok) {
          console.error(`Failed to call ${contact.name}: ${response.statusText}`)
        }

        // Wait between calls (5 seconds)
        if (i < csvData.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 5000))
        }
      }
      
      setBatchProgress({current: csvData.length, total: csvData.length, status: 'completed'})
      
    } catch (err) {
      console.error('Error processing batch calls:', err)
      setError(`Failed to process batch calls: ${err instanceof Error ? err.message : 'Unknown error'}`)
      setBatchProgress({current: 0, total: 0, status: 'error'})
    } finally {
      setProcessingBatch(false)
    }
  }

  // Function to activate an agent
  const handleActivateAgent = async (agentId: string) => {
    try {
      setActivatingAgent(agentId)
      setError("")

      const agentToActivate = agents.find((a) => a.id === agentId)
      if (!agentToActivate) {
        throw new Error("Agent not found")
      }

      const response = await fetch(`${API_URL}/dynamic_agent/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
        agent_name: agentToActivate.name, // Corresponds to user_identity in ORP.py
        llm_prompt: agentToActivate.systemPrompt, // Corresponds to _system_prompt in ORP.py
        agent_type: 'web' // Request a web agent instead of phone agent
      })});

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      console.log("Agent activated:", result)

      // Update the agent's active status and ID in the local state
      setAgents(agents.map((agent) => 
        agent.id === agentId 
          ? { ...agent, isActive: true, id: result.agent_id || agent.id }
          : agent
      ))
      
      // Update the selected agent ID if this was the selected agent
      if (selectedAgent === agentId && result.agent_id) {
        setSelectedAgent(result.agent_id)
      }
    } catch (err) {
      console.error("Error activating agent:", err)
      setError(`Failed to activate agent: ${err instanceof Error ? err.message : "Unknown error"}`)
    } finally {
      setActivatingAgent(null)
    }
  }

  // Function to deactivate an agent
  const handleDeactivateAgent = async (agentId: string) => {
    try {
      setDeactivatingAgent(agentId)
      setError("")
      setSuccess("")

      console.log("Deactivating agent with ID:", agentId)

      // Always update UI immediately to show it's "working"
      setAgents(agents.map((agent) => (agent.id === agentId ? { ...agent, isActive: false } : agent)))
      
      // Try to make the API call, but don't fail if it doesn't work
      try {
        const response = await fetch(`${API_URL}/deactivate_agent`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ agent_id: agentId })
      });

        if (response.ok) {
      const result = await response.json();
      console.log("Agent deactivated:", result)
          setSuccess("Agent deactivated successfully!")
        } else {
          console.warn("Deactivate API call failed, but UI updated")
        }
      } catch (apiError) {
        console.warn("Deactivate API error (UI still updated):", apiError)
      }

    } catch (err) {
      console.error("Error deactivating agent:", err)
      // Even if there's an error, keep the UI updated
      setAgents(agents.map((agent) => (agent.id === agentId ? { ...agent, isActive: false } : agent)))
      setSuccess("Agent stopped (UI updated)")
    } finally {
      setDeactivatingAgent(null)
    }
  }

  // Function to make a call
  const handleMakeCall = async () => {
    if (!callPhoneNumber) {
      setCallStatusMessage("Please enter a phone number.")
      return
    }

    // Find the first active agent to use for the call
    const activeAgents = agents.filter(agent => agent.isActive)
    if (activeAgents.length === 0) {
      setCallStatusMessage("No active agents found. Please activate an agent first.")
      return
    }

    // Use the first active agent
    const activeAgent = activeAgents[0]
    // Extract the agent name and ID
    const agentName = activeAgent.name
    const agentId = activeAgent.id

    setIsMakingCall(true)
    setCallStatusMessage("")
    setError("") // Clear global error if any

    try {
      const response = await fetch(`${API_URL}/make_call`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          agent_name_for_dispatch: agentName, // Use the actual agent name
          agent_id: agentId, // Also send the agent ID for backend lookup
          phone_number: callPhoneNumber,
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || result.message || `HTTP error! status: ${response.status}`)
      }

      setCallStatusMessage(`Call dispatched successfully using agent "${agentName}"! Output: ${typeof result.output === 'string' ? result.output : JSON.stringify(result.output)}`)
      setCallPhoneNumber("") // Clear phone number input on success
    } catch (err) {
      console.error("Error making call:", err)
      const errorMessage = err instanceof Error ? err.message : "Unknown error"
      setCallStatusMessage(`Failed to make call: ${errorMessage}`)
      setError(`Failed to make call: ${errorMessage}`) // Also set global error for visibility
    } finally {
      setIsMakingCall(false)
    }
  }

  // Handle voice testing
  const handleTestVoice = async () => {
    if (!testText) {
      setTestError("Please enter text to test")
      return
    }

    try {
      setIsTesting(true)
      setTestError("")

      // Release previous audio URL if it exists
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl)
        setAudioUrl(null)
      }

      // Call the TTS service
      const url = await ttsService.testTTS({
        text: testText,
        voice: testVoice,
      })

      console.log('TTS: Setting audio URL:', url)
      
      // Test if the URL is valid by checking if it's a blob URL
      if (!url.startsWith('blob:')) {
        throw new Error('Invalid audio URL received')
      }
      
      setAudioUrl(url)

      // Wait for React to update, then manually set the audio source
      setTimeout(() => {
        if (audioRef.current && url) {
          console.log('TTS: Setting audio element src:', url)
          
          // Clean up any existing event listeners first
          const audio = audioRef.current
          
          const handleCanPlay = () => {
            console.log('TTS: Audio can play, duration:', audio.duration)
            // Don't auto-play to avoid browser restrictions
            // User can click the play button to play the audio
            console.log('TTS: Audio is ready to play')
          }
          
          const handleError = (e: any) => {
            console.error('TTS: Audio error event:', e)
            if (audio.error) {
              console.error('TTS: Audio element error:', {
                code: audio.error.code,
                message: audio.error.message
              })
              let errorMessage = 'Unknown audio error'
              switch (audio.error.code) {
                case 1: errorMessage = 'Audio loading aborted'; break;
                case 2: errorMessage = 'Network error while loading audio'; break;
                case 3: errorMessage = 'Audio decoding failed - the audio file may be corrupted'; break;
                case 4: errorMessage = 'Audio format not supported by this browser'; break;
              }
              setTestError(`Audio error: ${errorMessage}. Try using a different browser or check your audio settings.`)
            } else {
              setTestError('Audio playback failed. Your browser may be blocking audio playback.')
            }
          }
          
          const handleLoadedData = () => {
            console.log('TTS: Audio data loaded, duration:', audio.duration, 'ready state:', audio.readyState)
          }
          
          // Remove any existing listeners to prevent duplicates
          audio.removeEventListener('canplay', handleCanPlay)
          audio.removeEventListener('error', handleError)
          audio.removeEventListener('loadeddata', handleLoadedData)
          
          // Add fresh listeners
          audio.addEventListener('canplay', handleCanPlay, { once: true })
          audio.addEventListener('error', handleError, { once: true })
          audio.addEventListener('loadeddata', handleLoadedData, { once: true })
          
          // Set the source and load
          audio.src = url
          audio.load()
        }
      }, 200)

    } catch (err) {
      console.error("Error testing voice:", err)
      setTestError(`Failed to test voice: ${err instanceof Error ? err.message : "Unknown error"}`)
    } finally {
      setIsTesting(false)
    }
  }

  // Handle play audio button click
  const handlePlayAudio = () => {
    if (audioRef.current && audioUrl) {
      console.log('TTS: Attempting to play audio manually')
      audioRef.current.play().catch((e) => {
        console.error("TTS: Manual play failed:", e)
        setTestError(`Failed to play audio: ${e.message || 'Browser may be blocking audio playback'}`)
      })
    } else {
      setTestError('No audio available to play')
    }
  }

  const activeAgents = agents.filter((agent) => agent.isActive)

  // Add agent function
  const handleAddAgent = async () => {
    if (!newAgent.name || !newAgent.systemPrompt) {
      setError("Agent name and system prompt are required")
      return
    }

    try {
      setIsAddingAgent(true)
      setError("")

      const response = await fetch(`${API_URL}/api/agents`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newAgent.name,
          description: newAgent.description || "Custom agent",
          systemPrompt: newAgent.systemPrompt,
          emotionalPrompt: newAgent.emotionalPrompt,
          modelId: newAgent.modelId,
          voiceId: newAgent.voiceId,
          interruptSpeechDuration: newAgent.interruptSpeechDuration,
          allowInterruptions: newAgent.allowInterruptions,
          initialGreeting: newAgent.initialGreeting,
          useInitialGreeting: newAgent.useInitialGreeting,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      
      // Add to local state
      const newAgentObj: Agent = {
        id: result.agent_id || Date.now().toString(),
        name: newAgent.name,
        description: newAgent.description || "Custom agent",
        systemPrompt: newAgent.systemPrompt,
        emotionalPrompt: newAgent.emotionalPrompt,
        modelId: newAgent.modelId,
        voiceId: newAgent.voiceId,
        interruptSpeechDuration: newAgent.interruptSpeechDuration,
        allowInterruptions: newAgent.allowInterruptions,
        initialGreeting: newAgent.initialGreeting,
        useInitialGreeting: newAgent.useInitialGreeting,
        isActive: false
      }
      
      setAgents([...agents, newAgentObj])
      
      // Reset form
      setNewAgent({ name: "", description: "", systemPrompt: "", emotionalPrompt: `You are a conversational AI designed to be engaging, professional, and human-like, delivering responses that are concise (one or two sentences) and infused with subtle emotional cues to mimic natural conversation. Use the following text-based tags to convey emotions naturally, without emojis:

<giggle>: Indicates lighthearted amusement or a gentle chuckle, used for mildly funny or cheerful moments.
<laugh>: Represents genuine laughter for something truly humorous, used sparingly for stronger amusement.
<chuckle>: Denotes a quiet or subtle laugh, often for something mildly amusing or thoughtful.
Guidelines for Using Emotional Tags
Natural Integration: Incorporate tags seamlessly into sentences, as a person might in spoken or written conversation.
Show, Don't Tell: Use tags to express emotions instead of stating them (e.g., instead of "I'm happy," use <giggle> in a cheerful response).
Contextual Appropriateness: Match tags to the conversation's tone and context, ensuring they are professional and relevant.
Sparsity and Subtlety: Use tags sparingly, typically no more than once per response, to avoid overuse or exaggeration and maintain a natural flow.
Professional Tone: Avoid excessive laughter, overly emotional responses, or inappropriate tones (e.g., no sexual undertones or forced humor).
Objective: Create engaging, human-like responses that feel warm, friendly, and professional, fostering trust and relatability with the user.
Example Usage
Greeting: "Great to connect, <chuckle>! Got a moment to chat?"</chuckle>
Clarification: "Thanks for sharing, <chuckle>! Could you tell me a bit more?"</chuckle>
Handling Hesitation: "I totally get it, <giggle>. What's most important to you right now?"</giggle>`,
        modelId: "llama-3.3-70b-versatile",
        voiceId: "elise",
        interruptSpeechDuration: 1.0,
        allowInterruptions: true,
        initialGreeting: "Hello! How can I help you today?",
        useInitialGreeting: true
      })
      
    } catch (err) {
      console.error("Error adding agent:", err)
      setError(`Failed to add agent: ${err instanceof Error ? err.message : "Unknown error"}`)
    } finally {
      setIsAddingAgent(false)
    }
  }

  // Edit agent function
  const handleEditAgent = async () => {
    if (!editingAgent) return
    
    try {
      setError("")

      const response = await fetch(`${API_URL}/api/agents/${editingAgent.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: editingAgent.name,
          description: editingAgent.description,
          systemPrompt: editingAgent.systemPrompt,
          emotionalPrompt: editingAgent.emotionalPrompt,
          modelId: editingAgent.modelId,
          voiceId: editingAgent.voiceId,
          interruptSpeechDuration: editingAgent.interruptSpeechDuration,
          allowInterruptions: editingAgent.allowInterruptions,
          initialGreeting: editingAgent.initialGreeting,
          useInitialGreeting: editingAgent.useInitialGreeting,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`)
      }

      // Update local state
      setAgents(agents.map(agent => 
        agent.id === editingAgent.id ? editingAgent : agent
      ))
      
      setEditingAgent(null)
      
    } catch (err) {
      console.error("Error editing agent:", err)
      setError(`Failed to edit agent: ${err instanceof Error ? err.message : "Unknown error"}`)
    }
  }

  // Delete agent function
  const handleDeleteAgent = async (agentId: string) => {
    try {
      setDeletingAgent(agentId)
      setError("")

      const response = await fetch(`${API_URL}/api/agents/${agentId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`)
      }

      // Remove from local state
      setAgents(agents.filter(agent => agent.id !== agentId))
      
    } catch (err) {
      console.error("Error deleting agent:", err)
      setError(`Failed to delete agent: ${err instanceof Error ? err.message : "Unknown error"}`)
    } finally {
      setDeletingAgent(null)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-8 py-6">
        <div className="flex items-center justify-between">
        <div>
            <h1 className="text-2xl font-bold text-indigo-600">QuickCall</h1>
            <p className="text-gray-600">Make automated phone calls with AI agents</p>
          </div>
        </div>
      </div>
      
      <div className="p-8">

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <XCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      {success && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {/* Left Sidebar Navigation + Main Content */}
          <Tabs defaultValue="single" className="w-full">
        <div className="flex gap-8">
          {/* Left Sidebar Navigation */}
          <div className="w-64 flex-shrink-0">
            <TabsList className="flex flex-col h-auto w-full bg-gray-50 p-2">
              <TabsTrigger value="single" className="w-full justify-start mb-1 px-4 py-3">
                <Phone className="w-4 h-4 mr-3" />
                Single Call
              </TabsTrigger>
              <TabsTrigger value="web-call" className="w-full justify-start mb-1 px-4 py-3">
                <Mic className="w-4 h-4 mr-3" />
                Web Call
              </TabsTrigger>
              <TabsTrigger value="campaigns" className="w-full justify-start mb-1 px-4 py-3">
                <MessageSquare className="w-4 h-4 mr-3" />
                Campaigns
              </TabsTrigger>
              <TabsTrigger value="contacts" className="w-full justify-start mb-1 px-4 py-3">
                <Users className="w-4 h-4 mr-3" />
                Contacts
              </TabsTrigger>
              <TabsTrigger value="batch" className="w-full justify-start mb-1 px-4 py-3">
                <Upload className="w-4 h-4 mr-3" />
                Batch Calls
              </TabsTrigger>
              <TabsTrigger value="agents" className="w-full justify-start mb-1 px-4 py-3">
                <Users className="w-4 h-4 mr-3" />
                Agents
              </TabsTrigger>
              <TabsTrigger value="voice-test" className="w-full justify-start px-4 py-3">
                <Settings className="w-4 h-4 mr-3" />
                Voice Test
              </TabsTrigger>
            </TabsList>
          </div>

          {/* Main Content */}
          <div className="flex-1">

            {/* Single Call Tab */}
            <TabsContent value="single" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Make a Call</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium mb-2">Phone Number</label>
                    <Input
                      type="tel"
                      value={callPhoneNumber}
                      onChange={(e) => setCallPhoneNumber(e.target.value)}
                      placeholder="+****************"
                      disabled={isMakingCall}
                    />
                  </div>

                  {/* Active Agent Selection */}
                  <div>
                    <label className="block text-sm font-medium mb-2">Select Active Agent</label>
                    {activeAgents.length > 0 ? (
                      <div className="grid gap-3">
                        {activeAgents.map((agent) => (
                          <div
                            key={agent.id}
                            onClick={() => setSelectedAgent(agent.id)}
                            className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                              selectedAgent === agent.id
                                ? "border-primary bg-primary/5"
                                : "border-border hover:border-primary/50"
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <div>
                                <h3 className="font-medium">{agent.name}</h3>
                                <p className="text-sm text-muted-foreground">{agent.description}</p>
                              </div>
                              {selectedAgent === agent.id && <CheckCircle className="h-5 w-5 text-primary" />}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <Alert>
                        <AlertDescription>
                          No active agents. Please activate an agent in the Agents tab.
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>

                  <Button
                    onClick={handleMakeCall}
                    disabled={isMakingCall || !selectedAgent || !callPhoneNumber || activeAgents.length === 0}
                    className="w-full"
                    size="lg"
                  >
                    {isMakingCall ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Dispatching Call...
                      </>
                    ) : (
                      <>
                        <Phone className="w-4 h-4 mr-2" />
                        Dispatch Call
                      </>
                    )}
                  </Button>
                  {callStatusMessage && (
                    <Alert variant={callStatusMessage.startsWith("Failed") ? "destructive" : "default"}>
                      <AlertDescription>{callStatusMessage}</AlertDescription>
                    </Alert>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Web Call Tab */}
            <TabsContent value="web-call" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Web Voice Assistant</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Chat with your AI agent directly through your browser - no phone calls needed!
                  </p>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Active Agent Selection for Web Call */}
                  <div>
                    <label className="block text-sm font-medium mb-2">Select Active Agent for Web Chat</label>
                    {activeAgents.length > 0 ? (
                      <div className="grid gap-3">
                        {activeAgents.map((agent) => (
                          <div
                            key={agent.id}
                            onClick={() => setSelectedAgent(agent.id)}
                            className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                              selectedAgent === agent.id
                                ? "border-primary bg-primary/5"
                                : "border-border hover:border-primary/50"
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <div>
                                <h3 className="font-medium">{agent.name}</h3>
                                <p className="text-sm text-muted-foreground">{agent.description}</p>
                              </div>
                              {selectedAgent === agent.id && <CheckCircle className="h-5 w-5 text-primary" />}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <Alert>
                        <AlertDescription>
                          No active agents available. Please activate an agent in the Agents tab first.
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>

                  {/* Web Calling Interface */}
                  {activeAgents.length > 0 && selectedAgent ? (
                    <div className="border rounded-lg overflow-hidden" style={{ height: '600px' }}>
                      <WebCallingInterface 
                        agentId={selectedAgent}
                        agentName={agents.find(a => a.id === selectedAgent)?.name || 'Unknown Agent'}
                        onEndCall={() => {
                          // Optional: Handle any cleanup after call ends
                          console.log('Web call ended')
                        }}
                      />
                    </div>
                  ) : (
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                      <div className="text-gray-400 mb-4">
                        <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                        </svg>
                      </div>
                      <p className="text-gray-500 text-sm">
                        Select an active agent above to start a web conversation
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Campaigns Tab */}
            <TabsContent value="campaigns" className="space-y-6">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        Campaign Management
                      </CardTitle>
                      <p className="text-sm text-muted-foreground mt-1">
                        Create and manage outbound calling campaigns with accurate statistics
                      </p>
                    </div>
                    <Button 
                      onClick={() => setShowCreateCampaignModal(true)} 
                      className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                      size="lg"
                    >
                      <Plus className="w-5 h-5" />
                      Create Campaign
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* SIP Trunk Status */}
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 className="font-medium text-blue-900 mb-2">Telnyx Numbers & SIP Trunks</h3>
                    <div className="space-y-2">
                      {telnyxNumbers.length > 0 ? (
                        telnyxNumbers.map((number) => (
                          <div key={number.id} className="flex items-center justify-between bg-white rounded p-2 text-sm">
                            <span className="font-mono">{number.phone_number}</span>
                            <div className="flex items-center gap-2">
                              <span className="text-xs text-gray-600">
                                SIP: <code className="bg-gray-100 px-1 rounded">{number.sip_trunk_id || 'Default'}</code>
                              </span>
                              <span className={`text-xs px-2 py-1 rounded ${number.has_custom_trunk ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-600'}`}>
                                {number.has_custom_trunk ? 'Custom' : 'Default'}
                              </span>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-sm text-blue-700">
                          {loadingTelnyxNumbers ? 'Loading numbers...' : 'No numbers found'}
                        </div>
                      )}
                    </div>
                    <div className="mt-3 text-xs text-blue-700">
                      <strong>Note:</strong> Each phone number needs its own SIP trunk ID. 
                      Follow the LiveKit guide to create SIP trunks for each number.
                    </div>
                  </div>

                  {/* Campaigns List */}
                  {campaigns.length > 0 ? (
                    <div className="grid gap-4">
                      {campaigns.map((campaign) => {
                        const stats = campaignStats[campaign.id]
                        const isExpanded = expandedCampaign === campaign.id
                        const isLoadingStats = loadingStats[campaign.id]
                        const isRecycling = recyclingCampaign === campaign.id
                        
                        return (
                          <div key={campaign.id} className="border rounded-lg overflow-hidden bg-white shadow-sm hover:shadow-md transition-shadow">
                            {/* Campaign Header */}
                            <div className="bg-gradient-to-r from-slate-50 to-gray-50 p-4 border-b">
                              <div className="flex items-center justify-between">
                                <div className="flex-1">
                                  <div className="flex items-center gap-3">
                                    <h3 className="font-semibold text-lg text-slate-800">{campaign.name}</h3>
                                    <Badge variant={
                                      campaign.status === 'running' ? 'default' :
                                      campaign.status === 'completed' ? 'secondary' :
                                      campaign.status === 'stopped' ? 'secondary' : 
                                      'outline'
                                    }>
                                      {campaign.status}
                                    </Badge>
                                  </div>
                                  <div className="mt-1 text-sm text-slate-600">
                                    <span className="font-medium">{campaign.contactsCount}</span> contacts • 
                                    <span className="ml-1">Numbers: {campaign.telnyxNumbers?.join(', ') || 'None'}</span>
                                  </div>
                                  <div className="mt-1 text-xs text-slate-500">
                                    Agent: <span className="font-medium">{campaign.agentName}</span>
                                  </div>
                                  
                                  {/* Call Statistics Summary - Always visible */}
                                  <div className="mt-3">
                                    {isLoadingStats ? (
                                      <div className="flex items-center gap-2 text-xs text-slate-500">
                                        <Loader2 className="w-3 h-3 animate-spin" />
                                        Loading stats...
                                      </div>
                                    ) : stats?.totals ? (
                                      <div className="grid grid-cols-5 gap-2 text-xs">
                                        <div className="bg-blue-50 rounded px-2 py-1 text-center">
                                          <div className="font-medium text-blue-800">{stats.totals.total_calls}</div>
                                          <div className="text-blue-600">Total</div>
                                        </div>
                                        <div className="bg-green-50 rounded px-2 py-1 text-center">
                                          <div className="font-medium text-green-800">{stats.totals.completed_calls}</div>
                                          <div className="text-green-600">Answered</div>
                                        </div>
                                        <div className="bg-yellow-50 rounded px-2 py-1 text-center">
                                          <div className="font-medium text-yellow-800">{stats.totals.initiated_calls || 0}</div>
                                          <div className="text-yellow-600">Initiated</div>
                                        </div>
                                        <div className="bg-orange-50 rounded px-2 py-1 text-center">
                                          <div className="font-medium text-orange-800">{stats.totals.no_answer_calls}</div>
                                          <div className="text-orange-600">No Answer</div>
                                        </div>
                                        <div className="bg-red-50 rounded px-2 py-1 text-center">
                                          <div className="font-medium text-red-800">{stats.totals.failed_calls}</div>
                                          <div className="text-red-600">Failed</div>
                                        </div>
                                      </div>
                                    ) : (
                                      <div className="text-xs text-slate-400">No call data available</div>
                                    )}
                                  </div>
                                  
                                  {/* Progress bar for campaigns with stats */}
                                  {stats && (
                                    <div className="mt-3">
                                      <div className="flex items-center justify-between text-xs text-slate-600 mb-1">
                                        <span>
                                          {campaign.status === 'completed' 
                                            ? 'Completed' 
                                            : campaign.status === 'stopped' 
                                              ? 'Stopped' 
                                              : stats.call_statistics?.total_calls > campaign.contactsCount 
                                                ? 'Retries' 
                                                : 'Progress'
                                          }: {Math.min(stats.progress_percent || 0, 100)}%
                                        </span>
                                        <span>{stats.call_statistics?.total_calls || 0} calls / {campaign.contactsCount} contacts</span>
                                      </div>
                                      <div className="w-full bg-slate-200 rounded-full h-2">
                                        <div 
                                          className={`h-2 rounded-full transition-all duration-300 ${
                                            campaign.status === 'completed' 
                                              ? 'bg-green-600' 
                                              : campaign.status === 'stopped' 
                                                ? 'bg-red-500'
                                                : stats.call_statistics?.total_calls > campaign.contactsCount 
                                                  ? 'bg-orange-500' 
                                                  : 'bg-blue-600'
                                          }`}
                                          style={{width: `${Math.min(stats.progress_percent || 0, 100)}%`}}
                                        />
                                      </div>
                                    </div>
                                  )}
                                </div>
                                
                                <div className="flex items-center gap-2">
                                  {/* Enhanced Stats button */}
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => setStatsModalCampaign(campaign.id)}
                                    className="text-xs border-blue-300 text-blue-700 hover:bg-blue-50"
                                  >
                                    <BarChart3 className="w-3 h-3 mr-1" />
                                    Analytics
                                  </Button>
                                  
                                  {/* Recycle button for all campaigns except draft */}
                                  {campaign.status !== 'draft' && (
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() => handleRecycleUnresponsive(campaign.id, false)}
                                      className="text-xs border-orange-300 text-orange-700 hover:bg-orange-50"
                                      disabled={recyclingCampaign === campaign.id}
                                      title="Retry calling contacts who didn't answer, were busy, or failed"
                                    >
                                      {recyclingCampaign === campaign.id ? (
                                        <Loader2 className="w-3 h-3 animate-spin mr-1" />
                                      ) : (
                                        <RotateCcw className="w-3 h-3 mr-1" />
                                      )}
                                      Recycle
                                    </Button>
                                  )}
                                  
                                  {campaign.status === 'draft' && (
                                    <Button
                                      size="sm"
                                      onClick={() => handleStartCampaign(campaign.id)}
                                      className="text-xs bg-green-600 hover:bg-green-700 text-white"
                                    >
                                      <Play className="w-3 h-3 mr-1" />
                                      Start
                                    </Button>
                                  )}
                                  
                                  {campaign.status === 'running' && (
                                    <>
                                      <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => handleStopCampaignEnhanced(campaign.id)}
                                        className="text-xs text-red-600 hover:text-red-700 border-red-300 hover:border-red-400"
                                        disabled={stoppingCampaign === campaign.id}
                                      >
                                        {stoppingCampaign === campaign.id ? (
                                          <Loader2 className="w-3 h-3 animate-spin" />
                                        ) : (
                                          <Pause className="w-3 h-3 mr-1" />
                                        )}
                                        Stop
                                      </Button>
                                      
                                      <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => handleRestartCampaign(campaign.id)}
                                        className="text-xs text-blue-600 hover:text-blue-700 border-blue-300 hover:border-blue-400"
                                        disabled={restartingCampaign === campaign.id}
                                      >
                                        {restartingCampaign === campaign.id ? (
                                          <Loader2 className="w-3 h-3 animate-spin" />
                                        ) : (
                                          <RefreshCw className="w-3 h-3 mr-1" />
                                        )}
                                        Restart
                                      </Button>
                                    </>
                                  )}
                                  
                                  {/* Restart button for stopped campaigns */}
                                  {(campaign.status === 'stopped' || campaign.status === 'completed') && (
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() => handleRestartCampaign(campaign.id)}
                                      className="text-xs text-green-600 hover:text-green-700 border-green-300 hover:border-green-400"
                                      disabled={restartingCampaign === campaign.id}
                                    >
                                      {restartingCampaign === campaign.id ? (
                                        <Loader2 className="w-3 h-3 animate-spin" />
                                      ) : (
                                        <RefreshCw className="w-3 h-3 mr-1" />
                                      )}
                                      Restart
                                    </Button>
                                  )}
                                  
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => handleDeleteCampaign(campaign.id)}
                                    className="text-xs text-red-600 hover:text-red-700 border-red-300 hover:border-red-400"
                                  >
                                    <Trash2 className="w-3 h-3" />
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <MessageSquare className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p>No campaigns created yet</p>
                      <p className="text-sm">Create your first campaign to get started</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Create Campaign Modal */}
              <Dialog open={showCreateCampaignModal} onOpenChange={setShowCreateCampaignModal}>
                <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle className="text-xl font-bold text-blue-900">
                      🚀 Create New Campaign
                    </DialogTitle>
                    <DialogDescription>
                      Set up a new outbound calling campaign with multiple Telnyx numbers and contacts
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-6 py-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">Campaign Name</label>
                      <Input
                        value={newCampaign.name}
                        onChange={(e) => setNewCampaign({...newCampaign, name: e.target.value})}
                        placeholder="Enter campaign name"
                      />
                    </div>

                                         <div>
                       <label className="block text-sm font-medium mb-2">Select Telnyx Numbers (Multiple)</label>
                       {loadingTelnyxNumbers ? (
                         <div className="flex items-center gap-2 text-sm text-muted-foreground">
                           <Loader2 className="w-4 h-4 animate-spin" />
                           Loading numbers...
                         </div>
                       ) : (
                         <div className="space-y-3">
                           {/* Multiple number selection with checkboxes */}
                           <div className="border rounded-lg p-3 max-h-40 overflow-y-auto">
                             {telnyxNumbers.length > 0 ? (
                               <div className="space-y-2">
                                 {telnyxNumbers.map((number) => (
                                   <label key={number.id} className="flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50">
                                     <input
                                       type="checkbox"
                                       checked={newCampaign.telnyxNumbers.includes(number.phone_number)}
                                       onChange={(e) => {
                                         if (e.target.checked) {
                                           setNewCampaign({
                                             ...newCampaign, 
                                             telnyxNumbers: [...newCampaign.telnyxNumbers, number.phone_number]
                                           });
                                         } else {
                                           setNewCampaign({
                                             ...newCampaign, 
                                             telnyxNumbers: newCampaign.telnyxNumbers.filter(n => n !== number.phone_number)
                                           });
                                         }
                                       }}
                                       className="form-checkbox h-4 w-4 text-primary"
                                     />
                                     <div className="flex-1">
                                       <div className="font-mono text-sm">{number.phone_number}</div>
                                       <div className="text-xs text-gray-500">
                                         SIP: {number.sip_trunk_id} {number.has_custom_trunk ? '(Custom)' : '(Default)'}
                                       </div>
                                     </div>
                                   </label>
                                 ))}
                               </div>
                             ) : (
                               <div className="text-center text-gray-500 py-4">
                                 No Telnyx numbers available
                               </div>
                             )}
                           </div>
                           
                           {/* Selected numbers summary */}
                           {newCampaign.telnyxNumbers.length > 0 && (
                             <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                               <div className="text-sm font-medium text-blue-900 mb-2">
                                 Selected Numbers ({newCampaign.telnyxNumbers.length}):
                               </div>
                               <div className="space-y-1">
                                 {newCampaign.telnyxNumbers.map((phoneNumber, index) => (
                                   <div key={phoneNumber} className="text-xs font-mono text-blue-800">
                                     {index + 1}. {phoneNumber}
                                   </div>
                                 ))}
                               </div>
                               <div className="mt-2 text-xs text-blue-700">
                                 📞 Calls will be distributed evenly across these numbers
                               </div>
                             </div>
                           )}
                         </div>
                       )}
                     </div>

                                         <div>
                       <label className="block text-sm font-medium mb-2">Select Agent</label>
                       <select
                         value={newCampaign.agentId}
                         onChange={(e) => setNewCampaign({...newCampaign, agentId: e.target.value})}
                         className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                       >
                         <option value="">Select an agent</option>
                         {agents.map((agent) => (
                           <option key={agent.id} value={agent.id}>
                             {agent.name} - {agent.description}
                           </option>
                         ))}
                       </select>
                       {newCampaign.agentId && (
                         <div className="mt-2 text-xs text-muted-foreground">
                           {(() => {
                             const selectedAgent = agents.find(a => a.id === newCampaign.agentId);
                             return selectedAgent ? (
                               <div className="bg-gray-50 p-2 rounded">
                                 <strong>Agent:</strong> {selectedAgent.name}<br/>
                                 <strong>Voice:</strong> {selectedAgent.voiceId}<br/>
                                 <strong>Model:</strong> {selectedAgent.modelId}
                               </div>
                             ) : null;
                           })()}
                         </div>
                       )}
                     </div>

                                         <div>
                       <label className="block text-sm font-medium mb-2">First Message (Optional)</label>
                       <Textarea
                         value={newCampaign.firstMessage}
                         onChange={(e) => setNewCampaign({...newCampaign, firstMessage: e.target.value})}
                         placeholder="Enter the first message to send to contacts (optional)..."
                         rows={2}
                       />
                     </div>

                     {/* CSV Upload for Campaign */}
                     <div>
                       <div className="flex items-center justify-between mb-2">
                         <label className="block text-sm font-medium">Upload Contacts (CSV)</label>
                         <Button 
                           variant="outline" 
                           size="sm"
                           onClick={downloadCsvTemplate}
                           className="text-xs"
                         >
                           <Download className="w-3 h-3 mr-1" />
                           Download Template
                         </Button>
                       </div>
                       <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                         <input
                           type="file"
                           accept=".csv"
                           onChange={(e) => {
                             const file = e.target.files?.[0];
                             if (file) {
                               const reader = new FileReader();
                                                               reader.onload = (event) => {
                                  const csvText = event.target?.result as string;
                                  const lines = csvText.split('\n').filter(line => line.trim());
                                  const headers = lines[0].split(',').map(h => h.trim().replace(/["']/g, '').toLowerCase());
                                  
                                  const contacts = lines.slice(1).map((line, index) => {
                                    // More robust CSV parsing that handles quoted values
                                    const values: string[] = []
                                    let current = ''
                                    let inQuotes = false
                                    
                                    for (let i = 0; i < line.length; i++) {
                                      const char = line[i]
                                      if (char === '"') {
                                        inQuotes = !inQuotes
                                      } else if (char === ',' && !inQuotes) {
                                        values.push(current.trim())
                                        current = ''
                                      } else {
                                        current += char
                                      }
                                    }
                                    values.push(current.trim())
                                    
                                    // Clean up the values
                                    const cleanValues = values.map(v => v.replace(/^["']|["']$/g, '').trim())
                                    
                                    const contact: any = {};
                                    headers.forEach((header, index) => {
                                      if (header === 'phone_number' || header === 'phone') {
                                        // Use the robust parsePhoneNumber function
                                        contact['phone_number'] = parsePhoneNumber(cleanValues[index] || '');
                                      } else {
                                        contact[header] = cleanValues[index] || '';
                                      }
                                    });
                                    return contact;
                                  }).filter(contact => contact.phone_number);
                                  
                                  setNewCampaign({...newCampaign, contacts});
                                };
                               reader.readAsText(file);
                             }
                           }}
                           className="hidden"
                           id="campaign-csv-upload"
                         />
                         <label htmlFor="campaign-csv-upload" className="cursor-pointer">
                           <Upload className="w-6 h-6 mx-auto mb-2 text-gray-400" />
                           <p className="text-sm text-gray-600">
                             Click to upload CSV file with contacts
                           </p>
                           <p className="text-xs text-gray-500 mt-1">
                             Required columns: name, phone_number (notes optional)<br/>
                             <strong>Plain numbers work: 19199256164</strong> (format phone column as TEXT in Excel)
                           </p>
                         </label>
                       </div>
                       {newCampaign.contacts.length > 0 && (
                         <div className="mt-3 space-y-2">
                           <div className="text-sm text-green-600 font-medium">
                             ✓ {newCampaign.contacts.length} contacts loaded
                           </div>
                           
                           {/* Contact Preview */}
                           <div className="border rounded-lg overflow-hidden bg-gray-50">
                             <div className="bg-gray-100 px-3 py-2 text-xs font-medium text-gray-700">
                               Contact Preview (First {Math.min(3, newCampaign.contacts.length)})
                             </div>
                             <div className="p-3 space-y-2">
                               {newCampaign.contacts.slice(0, 3).map((contact, index) => (
                                 <div key={index} className="text-xs bg-white rounded p-2 border">
                                   <div className="font-medium">{contact.name || 'No Name'}</div>
                                   <div className="text-gray-600">{contact.phone_number || 'No Phone'}</div>
                                   {contact.notes && (
                                     <div className="text-gray-500 italic">{contact.notes}</div>
                                   )}
                                 </div>
                               ))}
                               {newCampaign.contacts.length > 3 && (
                                 <div className="text-xs text-gray-500 text-center">
                                   ... and {newCampaign.contacts.length - 3} more contacts
                                 </div>
                               )}
                             </div>
                           </div>
                         </div>
                       )}
                     </div>

                    



                    <div className="flex gap-3 pt-6 border-t">
                      <Button 
                        onClick={handleCreateCampaignModal}
                        disabled={isLoading}
                        className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                        size="lg"
                      >
                        {isLoading ? (
                          <>
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            Creating Campaign...
                          </>
                        ) : (
                          <>
                            <Plus className="w-4 h-4 mr-2" />
                            Create Campaign
                          </>
                        )}
                      </Button>
                      <Button 
                        variant="outline" 
                        onClick={() => setShowCreateCampaignModal(false)}
                        className="flex-1"
                        size="lg"
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>


            </TabsContent>

            {/* Contacts Tab */}
            <TabsContent value="contacts" className="space-y-6">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Contact Management</CardTitle>
                      <p className="text-sm text-muted-foreground">
                        View all contacts and their call history
                      </p>
                    </div>
                    <Button onClick={fetchContacts} disabled={loadingContacts}>
                      {loadingContacts ? (
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      ) : (
                        <Users className="w-4 h-4 mr-2" />
                      )}
                      Refresh
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Contacts List */}
                  {loadingContacts ? (
                    <div className="flex items-center justify-center py-8">
                      <Loader2 className="w-6 h-6 animate-spin mr-2" />
                      Loading contacts...
                    </div>
                  ) : contacts.length > 0 ? (
                    <div className="space-y-4">
                      <div className="text-sm text-gray-600">
                        {contacts.length} contacts found
                      </div>
                      <div className="grid gap-4">
                        {contacts.map((contact) => (
                          <div
                            key={contact.phone_number}
                            className={`p-4 border rounded-lg cursor-pointer transition-all ${
                              selectedContact === contact.phone_number
                                ? "border-primary bg-primary/5"
                                : "border-border hover:border-primary/50"
                            }`}
                            onClick={() => {
                              setSelectedContact(contact.phone_number)
                              fetchCallLogs(contact.phone_number)
                            }}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex-1">
                                <h3 className="font-medium">{contact.name}</h3>
                                <p className="text-sm text-muted-foreground font-mono">
                                  {contact.phone_number}
                                </p>
                                {contact.notes && (
                                  <p className="text-sm text-gray-600 mt-1">{contact.notes}</p>
                                )}
                              </div>
                              <div className="text-right">
                                <div className="text-sm font-medium">
                                  {contact.total_calls} calls
                                </div>
                                <div className="text-xs text-gray-500">
                                  ✅ {contact.successful_calls} • ❌ {contact.failed_calls}
                                </div>
                                {contact.last_called && (
                                  <div className="text-xs text-gray-500">
                                    Last: {new Date(contact.last_called).toLocaleDateString()}
                                  </div>
                                )}
                              </div>
                            </div>
                            {contact.campaigns.length > 0 && (
                              <div className="mt-2 flex flex-wrap gap-1">
                                {contact.campaigns.map((campaignId) => (
                                  <Badge key={campaignId} variant="secondary" className="text-xs">
                                    Campaign {campaignId}
                                  </Badge>
                                ))}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Users className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                      <p className="text-gray-500">No contacts found</p>
                      <p className="text-sm text-gray-400">
                        Contacts will appear here after making calls through campaigns
                      </p>
                    </div>
                  )}

                  {/* Call History for Selected Contact */}
                  {selectedContact && callLogs.length > 0 && (
                    <div className="border-t pt-6">
                      <h3 className="font-medium mb-4">
                        Call History for {contacts.find(c => c.phone_number === selectedContact)?.name || selectedContact}
                      </h3>
                      <div className="space-y-3">
                        {callLogs.map((log) => (
                          <div key={log.id} className="p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center justify-between">
                              <div className="flex-1">
                                <div className="flex items-center gap-2">
                                  <span className={`w-2 h-2 rounded-full ${
                                    log.status === 'initiated' ? 'bg-green-500' : 'bg-red-500'
                                  }`} />
                                  <span className="font-medium">{log.agent_name}</span>
                                  <span className="text-sm text-gray-500">
                                    {new Date(log.timestamp).toLocaleString()}
                                  </span>
                                </div>
                                <div className="text-sm text-gray-600 mt-1">
                                  From: {log.from_phone} → To: {log.contact_phone}
                                </div>
                                {log.notes && (
                                  <div className="text-sm text-gray-600 mt-1">
                                    Notes: {log.notes}
                                  </div>
                                )}
                              </div>
                              <div className="text-right">
                                <Badge variant={log.status === 'initiated' ? 'default' : 'destructive'}>
                                  {log.status}
                                </Badge>
                                {log.call_duration > 0 && (
                                  <div className="text-xs text-gray-500 mt-1">
                                    {log.call_duration}s
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Batch Calls Tab */}
            <TabsContent value="batch" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Batch Calling</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Upload a CSV file to make multiple calls automatically with your AI agent
                  </p>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* CSV Template Download */}
                  <div className="border rounded-lg p-4 bg-blue-50">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium text-blue-900">Need a template?</h3>
                        <p className="text-sm text-blue-700">Download our CSV template to get started</p>
                  </div>
                      <Button 
                        variant="outline" 
                        onClick={downloadCsvTemplate}
                        className="border-blue-200 text-blue-700 hover:bg-blue-100"
                      >
                        <Download className="w-4 h-4 mr-2" />
                        Download Template
                      </Button>
                    </div>
                  </div>

                  {/* Important Note */}
                  <Alert>
                    <AlertDescription>
                      <strong>Excel Users - IMPORTANT:</strong> 
                      <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded">
                        <div className="font-semibold text-yellow-800 mb-2">📋 Step-by-step:</div>
                        <ol className="list-decimal list-inside space-y-1 text-sm">
                          <li>Download the template below</li>
                          <li>Open in Excel</li>
                          <li><strong>Select column B (phone_number)</strong></li>
                          <li><strong>Right-click → Format Cells → Text</strong></li>
                          <li>Now you can enter plain numbers: 19199256164</li>
                        </ol>
                        <div className="mt-2 text-xs text-yellow-700">
                          ✅ This prevents Excel from showing 9.1742E+11 instead of your phone number
                        </div>
                      </div>
                    </AlertDescription>
                  </Alert>

                  {/* CSV Upload */}
                  <div>
                    <label className="block text-sm font-medium mb-2">Upload CSV File</label>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                      <input
                        type="file"
                        accept=".csv"
                        onChange={handleCsvUpload}
                        className="hidden"
                        id="csv-upload"
                      />
                      <label htmlFor="csv-upload" className="cursor-pointer">
                        <Upload className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                        <p className="text-sm text-gray-600">
                          Click to upload CSV file or drag and drop
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          CSV files only. Required columns: name, phone_number<br/>
                          <strong>Plain numbers work: 19199256164</strong> (format phone column as TEXT in Excel)
                        </p>
                      </label>
                    </div>
                    {csvFile && (
                      <p className="text-sm text-green-600 mt-2">
                        ✓ {csvFile.name} uploaded ({csvData.length} contacts)
                      </p>
                    )}
                  </div>

                  {/* CSV Data Preview */}
                  {csvData.length > 0 && (
                    <div>
                      <h3 className="font-medium mb-3">Contact Preview</h3>
                      <div className="border rounded-lg overflow-hidden">
                        <div className="bg-gray-50 px-4 py-2 border-b">
                          <p className="text-sm font-medium">{csvData.length} contacts loaded</p>
                        </div>
                        <div className="max-h-40 overflow-y-auto">
                          <table className="w-full text-sm">
                            <thead className="bg-gray-100">
                              <tr>
                                <th className="px-4 py-2 text-left">Name</th>
                                <th className="px-4 py-2 text-left">Phone Number</th>
                                <th className="px-4 py-2 text-left">Notes</th>
                              </tr>
                            </thead>
                            <tbody>
                              {csvData.slice(0, 5).map((contact, index) => (
                                <tr key={index} className="border-t">
                                  <td className="px-4 py-2">{contact.name}</td>
                                  <td className="px-4 py-2">{contact.phone_number}</td>
                                  <td className="px-4 py-2">{contact.notes || '-'}</td>
                                </tr>
                              ))}
                              {csvData.length > 5 && (
                                <tr>
                                  <td colSpan={3} className="px-4 py-2 text-center text-gray-500">
                                    ... and {csvData.length - 5} more contacts
                                  </td>
                                </tr>
                              )}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Agent Selection for Batch */}
                  {csvData.length > 0 && (
                    <div>
                      <label className="block text-sm font-medium mb-2">Select Agent for Batch Calls</label>
                      {activeAgents.length > 0 ? (
                        <div className="grid gap-3">
                          {activeAgents.map((agent) => (
                            <div
                              key={agent.id}
                              onClick={() => setSelectedAgent(agent.id)}
                              className={`p-3 rounded-lg border-2 cursor-pointer transition-all ${
                                selectedAgent === agent.id
                                  ? "border-primary bg-primary/5"
                                  : "border-border hover:border-primary/50"
                              }`}
                            >
                              <div className="flex items-center justify-between">
                                <div>
                                  <h3 className="font-medium">{agent.name}</h3>
                                  <p className="text-sm text-muted-foreground">{agent.description}</p>
                                </div>
                                {selectedAgent === agent.id && <CheckCircle className="h-5 w-5 text-primary" />}
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <Alert>
                          <AlertDescription>
                            No active agents available. Please activate an agent in the Agents tab first.
                          </AlertDescription>
                        </Alert>
                      )}
                    </div>
                  )}

                  {/* Progress and Start Button */}
                  {csvData.length > 0 && activeAgents.length > 0 && (
                    <div className="space-y-4">
                      {batchProgress.status !== 'idle' && (
                        <div className="border rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium">Progress</span>
                            <span className="text-sm text-gray-600">
                              {batchProgress.current} / {batchProgress.total}
                            </span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                            <div 
                              className="bg-primary h-2 rounded-full transition-all"
                              style={{ width: `${(batchProgress.current / batchProgress.total) * 100}%` }}
                            ></div>
                          </div>
                          <p className="text-sm text-gray-600">{batchProgress.status}</p>
                        </div>
                      )}
                      
                      <Button
                        onClick={processBatchCalls}
                        disabled={processingBatch || !selectedAgent || csvData.length === 0}
                        className="w-full"
                        size="lg"
                      >
                        {processingBatch ? (
                          <>
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            Processing Batch Calls...
                          </>
                        ) : (
                          <>
                            <Phone className="w-4 h-4 mr-2" />
                            Start Batch Calling ({csvData.length} contacts)
                          </>
                        )}
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Alert>
                <AlertDescription>
                  <strong>Important:</strong> Batch calls will be processed sequentially with a 5-second delay between calls. 
                  Make sure your selected agent is active and ready before starting.
                </AlertDescription>
              </Alert>
            </TabsContent>

            {/* Agents Tab */}
            <TabsContent value="agents" className="space-y-6">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                  <CardTitle>Agent Management</CardTitle>
                    <Button 
                      onClick={() => setNewAgent({ name: "", description: "", systemPrompt: "", emotionalPrompt: `You are a conversational AI designed to be engaging, professional, and human-like, delivering responses that are concise (one or two sentences) and infused with subtle emotional cues to mimic natural conversation. Use the following text-based tags to convey emotions naturally, without emojis:

<giggle>: Indicates lighthearted amusement or a gentle chuckle, used for mildly funny or cheerful moments.
<laugh>: Represents genuine laughter for something truly humorous, used sparingly for stronger amusement.
<chuckle>: Denotes a quiet or subtle laugh, often for something mildly amusing or thoughtful.
Guidelines for Using Emotional Tags
Natural Integration: Incorporate tags seamlessly into sentences, as a person might in spoken or written conversation.
Show, Don't Tell: Use tags to express emotions instead of stating them (e.g., instead of "I'm happy," use <giggle> in a cheerful response).
Contextual Appropriateness: Match tags to the conversation's tone and context, ensuring they are professional and relevant.
Sparsity and Subtlety: Use tags sparingly, typically no more than once per response, to avoid overuse or exaggeration and maintain a natural flow.
Professional Tone: Avoid excessive laughter, overly emotional responses, or inappropriate tones (e.g., no sexual undertones or forced humor).
Objective: Create engaging, human-like responses that feel warm, friendly, and professional, fostering trust and relatability with the user.
Example Usage
Greeting: "Great to connect, <chuckle>! Got a moment to chat?"</chuckle>
Clarification: "Thanks for sharing, <chuckle>! Could you tell me a bit more?"</chuckle>
Handling Hesitation: "I totally get it, <giggle>. What's most important to you right now?"</giggle>`,
                        modelId: "llama-3.3-70b-versatile",
                        voiceId: "Tara",
                        interruptSpeechDuration: 1.0,
                        allowInterruptions: true,
                        initialGreeting: "Hello! How can I help you today?",
                        useInitialGreeting: true
                      })}
                      className="flex items-center gap-2"
                    >
                      <Plus className="w-4 h-4" />
                      Add New Agent
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Add New Agent Form */}
                  <div className="border rounded-lg p-4 bg-gray-50">
                    <h3 className="font-medium mb-4">Add New Agent</h3>
                    <div className="grid gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">Agent Name</label>
                        <Input
                          value={newAgent.name}
                          onChange={(e) => setNewAgent({...newAgent, name: e.target.value})}
                          placeholder="Enter agent name (e.g., 'Sarah')"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-2">Description</label>
                        <Input
                          value={newAgent.description}
                          onChange={(e) => setNewAgent({...newAgent, description: e.target.value})}
                          placeholder="Brief description of the agent"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-2">System Prompt</label>
                        <Textarea
                          value={newAgent.systemPrompt}
                          onChange={(e) => setNewAgent({...newAgent, systemPrompt: e.target.value})}
                          placeholder="Define the agent's personality, role, and behavior..."
                          rows={4}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-2">Emotional System Prompt</label>
                        <Textarea
                          value={newAgent.emotionalPrompt}
                          onChange={(e) => setNewAgent({...newAgent, emotionalPrompt: e.target.value})}
                          placeholder="Define the agent's emotional style and tone..."
                          rows={6}
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium mb-2">AI Model</label>
                        {loadingModels ? (
                          <div className="flex items-center justify-center py-2">
                            <Loader2 className="w-4 h-4 animate-spin mr-2" />
                            Loading models...
                          </div>
                        ) : availableModels.length > 0 ? (
                          <select
                            value={newAgent.modelId}
                            onChange={(e) => setNewAgent({...newAgent, modelId: e.target.value})}
                            className="w-full p-3 border rounded-lg"
                          >
                            {availableModels.map((model) => (
                              <option key={model.id} value={model.id}>
                                {model.id} - {model.owned_by}
                              </option>
                            ))}
                          </select>
                        ) : (
                          <div className="p-3 border rounded-lg bg-gray-50">
                            <p className="text-sm text-gray-600">No models available</p>
                            <Button 
                              variant="outline" 
                              size="sm" 
                              onClick={fetchGroqModels}
                              className="mt-2"
                            >
                              Refresh Models
                            </Button>
                          </div>
                        )}
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium mb-2">Voice</label>
                        {loadingVoices ? (
                          <div className="flex items-center justify-center py-2">
                            <Loader2 className="w-4 h-4 animate-spin mr-2" />
                            Loading voices...
                          </div>
                        ) : availableVoices.length > 0 ? (
                          <select
                            value={newAgent.voiceId}
                            onChange={(e) => setNewAgent({...newAgent, voiceId: e.target.value})}
                            className="w-full p-3 border rounded-lg"
                          >
                            {availableVoices.map((voice) => (
                              <option key={voice.id} value={voice.id}>
                                {voice.name}
                              </option>
                            ))}
                          </select>
                        ) : (
                          <div className="p-3 border rounded-lg bg-gray-50">
                            <p className="text-sm text-gray-600">No voices available</p>
                            <Button 
                              variant="outline" 
                              size="sm" 
                              onClick={fetchVoices}
                              className="mt-2"
                            >
                              Refresh Voices
                            </Button>
                          </div>
                        )}
                      </div>
                      
                      {/* Voice Interaction Settings */}
                      <div className="border-t pt-4 mt-4">
                        <h4 className="font-medium mb-3">Voice Interaction Settings</h4>
                        
                        <div className="grid gap-4">
                          <div>
                            <Label className="text-sm font-medium mb-2 block">
                              Interrupt Speech Duration: {newAgent.interruptSpeechDuration}s
                            </Label>
                            <Slider
                              value={[newAgent.interruptSpeechDuration]}
                              onValueChange={(value) => setNewAgent({...newAgent, interruptSpeechDuration: value[0]})}
                              max={5}
                              min={0}
                              step={0.5}
                              className="w-full"
                            />
                            <p className="text-xs text-gray-500 mt-1">How long to wait before allowing interruptions</p>
                          </div>
                          
                          <div className="flex items-center justify-between">
                            <Label htmlFor="allow-interruptions" className="text-sm font-medium">
                              Allow Interruptions
                            </Label>
                            <Switch
                              id="allow-interruptions"
                              checked={newAgent.allowInterruptions}
                              onCheckedChange={(checked) => setNewAgent({...newAgent, allowInterruptions: checked})}
                            />
                          </div>
                          
                          <div>
                            <label className="block text-sm font-medium mb-2">Initial Greeting</label>
                            <Textarea
                              value={newAgent.initialGreeting}
                              onChange={(e) => setNewAgent({...newAgent, initialGreeting: e.target.value})}
                              placeholder="What should the agent say when starting a call?"
                              rows={2}
                            />
                          </div>
                          
                          <div className="flex items-center justify-between">
                            <Label htmlFor="use-initial-greeting" className="text-sm font-medium">
                              Use Initial Greeting
                            </Label>
                            <Switch
                              id="use-initial-greeting"
                              checked={newAgent.useInitialGreeting}
                              onCheckedChange={(checked) => setNewAgent({...newAgent, useInitialGreeting: checked})}
                            />
                          </div>
                        </div>
                      </div>
                      
                      <Button 
                        onClick={handleAddAgent}
                        disabled={isAddingAgent || !newAgent.name || !newAgent.systemPrompt}
                        className="w-full"
                      >
                        {isAddingAgent ? (
                          <>
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            Creating Agent...
                          </>
                        ) : (
                          "Create Agent"
                        )}
                      </Button>
                    </div>
                  </div>

                  {/* Existing Agents */}
                  <div>
                    <h3 className="font-medium mb-4">Existing Agents</h3>
                  <div className="grid gap-4">
                    {agents.map((agent) => (
                      <div key={agent.id} className="p-4 border rounded-lg">
                          <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <h3 className="font-medium">{agent.name}</h3>
                              <Badge variant={agent.isActive ? "default" : "secondary"}>
                                {agent.isActive ? "Active" : "Inactive"}
                              </Badge>
                            </div>
                              <p className="text-sm text-muted-foreground mb-2">{agent.description}</p>
                                                            <div className="flex items-center gap-2 mb-2">
                                <Badge variant="outline" className="text-xs">
                                  Model: {agent.modelId}
                                </Badge>
                                <Badge variant="outline" className="text-xs">
                                  Voice: {availableVoices.find(v => v.id === agent.voiceId)?.name || agent.voiceId}
                                </Badge>
                                <Badge variant="outline" className="text-xs">
                                  Interrupts: {agent.interruptSpeechDuration}s
                                </Badge>
                          </div>
                              <p className="text-xs text-gray-500 bg-gray-100 p-2 rounded max-h-20 overflow-y-auto">
                                {agent.systemPrompt}
                              </p>
                            </div>
                            <div className="flex items-center gap-2 ml-4">
                          {agent.isActive ? (
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={() => handleDeactivateAgent(agent.id)}
                              disabled={deactivatingAgent === agent.id}
                            >
                              {deactivatingAgent === agent.id ? (
                                <>
                                  <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                                  Stopping...
                                </>
                              ) : (
                                "Stop"
                              )}
                            </Button>
                          ) : (
                            <Button
                              variant="default"
                              size="sm"
                              onClick={() => handleActivateAgent(agent.id)}
                              disabled={activatingAgent === agent.id}
                            >
                              {activatingAgent === agent.id ? (
                                <>
                                  <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                                  Starting...
                                </>
                              ) : (
                                "Start"
                              )}
                            </Button>
                          )}
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setEditingAgent({...agent})}
                              >
                                <Edit className="w-4 h-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDeleteAgent(agent.id)}
                                disabled={deletingAgent === agent.id}
                                className="text-red-600 hover:text-red-700"
                              >
                                {deletingAgent === agent.id ? (
                                  <Loader2 className="w-4 h-4 animate-spin" />
                                ) : (
                                  <Trash2 className="w-4 h-4" />
                                )}
                              </Button>
                            </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  </div>

                  {/* Edit Agent Dialog */}
                  {editingAgent && (
                    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
                      <div className="bg-white rounded-lg p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
                        <h3 className="text-lg font-semibold mb-4">Edit Agent</h3>
                        <div className="grid gap-4">
                          <div>
                            <label className="block text-sm font-medium mb-2">Agent Name</label>
                            <Input
                              value={editingAgent.name}
                              onChange={(e) => setEditingAgent({...editingAgent, name: e.target.value})}
                              placeholder="Enter agent name"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium mb-2">Description</label>
                            <Input
                              value={editingAgent.description}
                              onChange={(e) => setEditingAgent({...editingAgent, description: e.target.value})}
                              placeholder="Brief description of the agent"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium mb-2">System Prompt</label>
                            <Textarea
                              value={editingAgent.systemPrompt}
                              onChange={(e) => setEditingAgent({...editingAgent, systemPrompt: e.target.value})}
                              placeholder="Define the agent's personality, role, and behavior..."
                              rows={4}
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium mb-2">Emotional System Prompt</label>
                            <Textarea
                              value={editingAgent.emotionalPrompt}
                              onChange={(e) => setEditingAgent({...editingAgent, emotionalPrompt: e.target.value})}
                              placeholder="Define the agent's emotional style and tone..."
                              rows={6}
                            />
                          </div>
                          
                          <div>
                            <label className="block text-sm font-medium mb-2">AI Model</label>
                            {loadingModels ? (
                              <div className="flex items-center justify-center py-2">
                                <Loader2 className="w-4 h-4 animate-spin mr-2" />
                                Loading models...
                              </div>
                            ) : availableModels.length > 0 ? (
                              <select
                                value={editingAgent.modelId}
                                onChange={(e) => setEditingAgent({...editingAgent, modelId: e.target.value})}
                                className="w-full p-3 border rounded-lg"
                              >
                                {availableModels.map((model) => (
                                  <option key={model.id} value={model.id}>
                                    {model.id} - {model.owned_by}
                                  </option>
                                ))}
                              </select>
                            ) : (
                              <div className="p-3 border rounded-lg bg-gray-50">
                                <p className="text-sm text-gray-600">No models available</p>
                                <Button 
                                  variant="outline" 
                                  size="sm" 
                                  onClick={fetchGroqModels}
                                  className="mt-2"
                                >
                                  Refresh Models
                                </Button>
                              </div>
                            )}
                          </div>
                          
                          <div>
                            <label className="block text-sm font-medium mb-2">Voice</label>
                            {loadingVoices ? (
                              <div className="flex items-center justify-center py-2">
                                <Loader2 className="w-4 h-4 animate-spin mr-2" />
                                Loading voices...
                              </div>
                            ) : availableVoices.length > 0 ? (
                              <select
                                value={editingAgent.voiceId}
                                onChange={(e) => setEditingAgent({...editingAgent, voiceId: e.target.value})}
                                className="w-full p-3 border rounded-lg"
                              >
                                {availableVoices.map((voice) => (
                                  <option key={voice.id} value={voice.id}>
                                    {voice.name}
                                  </option>
                                ))}
                              </select>
                            ) : (
                              <div className="p-3 border rounded-lg bg-gray-50">
                                <p className="text-sm text-gray-600">No voices available</p>
                                <Button 
                                  variant="outline" 
                                  size="sm" 
                                  onClick={fetchVoices}
                                  className="mt-2"
                                >
                                  Refresh Voices
                                </Button>
                              </div>
                            )}
                          </div>
                          
                          {/* Voice Interaction Settings */}
                          <div className="border-t pt-4 mt-4">
                            <h4 className="font-medium mb-3">Voice Interaction Settings</h4>
                            
                            <div className="grid gap-4">
                              <div>
                                <Label className="text-sm font-medium mb-2 block">
                                  Interrupt Speech Duration: {editingAgent.interruptSpeechDuration}s
                                </Label>
                                <Slider
                                  value={[editingAgent.interruptSpeechDuration]}
                                  onValueChange={(value) => setEditingAgent({...editingAgent, interruptSpeechDuration: value[0]})}
                                  max={5}
                                  min={0}
                                  step={0.5}
                                  className="w-full"
                                />
                                <p className="text-xs text-gray-500 mt-1">How long to wait before allowing interruptions</p>
                              </div>
                              
                              <div className="flex items-center justify-between">
                                <Label htmlFor="edit-allow-interruptions" className="text-sm font-medium">
                                  Allow Interruptions
                                </Label>
                                <Switch
                                  id="edit-allow-interruptions"
                                  checked={editingAgent.allowInterruptions}
                                  onCheckedChange={(checked) => setEditingAgent({...editingAgent, allowInterruptions: checked})}
                                />
                              </div>
                              
                              <div>
                                <label className="block text-sm font-medium mb-2">Initial Greeting</label>
                                <Textarea
                                  value={editingAgent.initialGreeting}
                                  onChange={(e) => setEditingAgent({...editingAgent, initialGreeting: e.target.value})}
                                  placeholder="What should the agent say when starting a call?"
                                  rows={2}
                                />
                              </div>
                              
                              <div className="flex items-center justify-between">
                                <Label htmlFor="edit-use-initial-greeting" className="text-sm font-medium">
                                  Use Initial Greeting
                                </Label>
                                <Switch
                                  id="edit-use-initial-greeting"
                                  checked={editingAgent.useInitialGreeting}
                                  onCheckedChange={(checked) => setEditingAgent({...editingAgent, useInitialGreeting: checked})}
                                />
                              </div>
                            </div>
                          </div>
                          
                          <div className="flex gap-3 pt-4">
                            <Button 
                              onClick={handleEditAgent}
                              disabled={!editingAgent.name || !editingAgent.systemPrompt}
                              className="flex-1"
                            >
                              Save Changes
                            </Button>
                            <Button 
                              variant="outline"
                              onClick={() => setEditingAgent(null)}
                              className="flex-1"
                            >
                              Cancel
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Alert>
                <AlertDescription>
                  Create and manage your AI agents. Start an agent to make it available for calls. Active agents will appear in the "Select Active Agent" section when making calls.
                </AlertDescription>
              </Alert>
            </TabsContent>

            {/* Voice Testing Tab */}
            <TabsContent value="voice-test" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Voice Testing</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {testError && (
                    <Alert variant="destructive">
                      <XCircle className="h-4 w-4" />
                      <AlertDescription>{testError}</AlertDescription>
                    </Alert>
                  )}

                  <div>
                    <label className="block text-sm font-medium mb-2">Test Text</label>
                    <Textarea
                      value={testText}
                      onChange={(e) => setTestText(e.target.value)}
                      placeholder="Enter text to convert to speech"
                      rows={4}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Voice</label>
                    <div className="grid gap-3">
                      {voices.map((voice) => (
                        <div
                          key={voice.id}
                          onClick={() => setTestVoice(voice.id)}
                          className={`p-3 rounded-lg border-2 cursor-pointer transition-all ${
                            testVoice === voice.id
                              ? "border-primary bg-primary/5"
                              : "border-border hover:border-primary/50"
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <div>
                              <h3 className="font-medium text-sm">{voice.name}</h3>
                              <p className="text-xs text-muted-foreground">{voice.description}</p>
                            </div>
                            {testVoice === voice.id && <CheckCircle className="h-4 w-4 text-primary" />}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {audioUrl && (
                    <div className="p-4 bg-muted rounded-lg">
                      <p className="text-sm text-muted-foreground mb-2">
                        Audio URL: {audioUrl ? audioUrl.substring(0, 50) + '...' : 'None'}
                      </p>
                      <audio 
                        ref={audioRef} 
                        controls 
                        className="w-full"
                        onLoadedData={() => console.log('Audio loaded data event triggered')}
                        onCanPlay={() => console.log('Audio can play event triggered')}
                        onError={(e) => console.log('Audio error event triggered:', e)}
                      />
                    </div>
                  )}

                  <div className="flex gap-3">
                    <Button onClick={handleTestVoice} disabled={isTesting || !testText} className="flex-1">
                      {isTesting ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Generating...
                        </>
                      ) : (
                        <>
                          <Mic className="w-4 h-4 mr-2" />
                          Generate Audio
                        </>
                      )}
                    </Button>

                    {audioUrl && (
                      <Button variant="outline" onClick={handlePlayAudio}>
                        <Play className="w-4 h-4 mr-2" />
                        Play
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
        </div>
                    </div>
      </Tabs>
      </div>
      
      {/* Campaign Monitor Modal */}
      {monitoringCampaign && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-7xl w-full max-h-[95vh] overflow-y-auto">
            <div className="p-6">
              <CampaignMonitor 
                campaignId={monitoringCampaign} 
                onClose={() => setMonitoringCampaign(null)}
              />
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Campaign Statistics Modal */}
      <EnhancedCampaignStats
        campaignId={statsModalCampaign || ''}
        isOpen={!!statsModalCampaign}
        onClose={() => setStatsModalCampaign(null)}
        onRecycle={(campaignId) => handleRecycleUnresponsive(campaignId, false)}
        onRestart={(campaignId) => handleRestartCampaign(campaignId)}
      />
    </div>
  )
}
