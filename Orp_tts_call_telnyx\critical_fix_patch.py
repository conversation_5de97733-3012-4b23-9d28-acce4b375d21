#!/usr/bin/env python3
"""
CRITICAL PATCH FOR SIMPLE_BACKEND.PY CALLING BOTTLENECK

This script shows the EXACT code replacements needed to fix the main issue.

PROBLEM: Lines 1862-1864 in make_single_call() create HTTP self-requests that cause bottlenecks
SOLUTION: Replace with direct subprocess calls

INSTRUCTIONS:
1. Open Orp_tts_call_telnyx/simple_backend.py
2. Find lines around 1862-1864 that contain:
   ```
   import requests
   response = requests.post('http://localhost:9090/make_call', json=call_data, timeout=10)
   ```
3. Replace with the code shown below
"""

import subprocess
import json
import os
import logging

logger = logging.getLogger(__name__)

# =============================================================================
# REPLACE THIS CODE IN YOUR simple_backend.py
# =============================================================================

def REPLACE_THIS_IN_make_single_call():
    """
    FIND THIS CODE IN simple_backend.py around line 1862:
    
    ```python
    # Simulate the make_call request internally
    import requests
    response = requests.post('http://localhost:9090/make_call', json=call_data, timeout=10)
    
    if response.status_code == 200:
        mode_text = "PERSONALIZED" if use_personalized_ai else "SHARED"
        logger.info(f"✅ {mode_text} CALL INITIATED: {contact_name} ({contact_phone}) with agent {agent_name_for_call}")
    ```
    
    REPLACE WITH THE CODE BELOW:
    """
    pass

# =============================================================================
# NEW CODE TO REPLACE THE HTTP SELF-REQUEST
# =============================================================================

def direct_livekit_call(call_data):
    """
    Direct LiveKit dispatch - NO HTTP REQUEST
    Replace the requests.post() call with this function
    """
    try:
        # Extract data for LiveKit command
        agent_name_for_dispatch = call_data.get('agent_name_for_dispatch')
        phone_number = call_data.get('phone_number')
        from_phone = call_data.get('from_phone')
        sip_trunk_id = call_data.get('sip_trunk_id')
        campaign_id = call_data.get('campaign_id')
        call_id = call_data.get('call_id')
        contact_name = call_data.get('contact_name')
        
        # Build structured metadata for the agent
        if from_phone or sip_trunk_id != 'DEFAULT_SIP_TRUNK_ID':
            metadata = {
                "target_phone": phone_number,
                "from_phone": from_phone,
                "sip_trunk_id": sip_trunk_id,
                "campaign_id": campaign_id,
                "call_id": call_id,
                "contact_name": contact_name
            }
            metadata_str = json.dumps(metadata)
            logger.info(f"Using structured metadata: {metadata_str}")
        else:
            metadata_str = phone_number
            logger.info(f"Using simple metadata: {metadata_str}")

        # Build LiveKit dispatch command
        cmd = [
            "lk", "dispatch", "create",
            "--new-room",
            "--agent-name", agent_name_for_dispatch,
            "--metadata", metadata_str
        ]
        
        process_cwd = os.path.dirname(__file__)  # Directory of simple_backend.py

        logger.info(f"Executing command: {' '.join(cmd)} in CWD: {process_cwd}")
        result = subprocess.run(cmd, cwd=process_cwd, capture_output=True, text=True, check=False, timeout=20)

        if result.returncode == 0:
            logger.info(f"lk dispatch command successful. Output: {result.stdout}")
            return {"success": True, "status_code": 200, "output": result.stdout}
        else:
            logger.error(f"lk dispatch command failed. Return code: {result.returncode}. Error: {result.stderr}. Output: {result.stdout}")
            return {
                "success": False, 
                "status_code": 500,
                "error": result.stderr.strip(), 
                "output": result.stdout.strip(),
                "returncode": result.returncode
            }

    except FileNotFoundError:
        logger.error(f"lk command not found. Ensure LiveKit CLI is installed and in PATH.")
        return {"success": False, "status_code": 500, "error": "lk command not found"}
    except subprocess.TimeoutExpired:
        logger.error(f"lk dispatch command timed out after 20 seconds")
        return {"success": False, "status_code": 500, "error": "Command timed out"}
    except Exception as e:
        logger.error(f"Error executing lk dispatch command: {e}", exc_info=True)
        return {"success": False, "status_code": 500, "error": f"An unexpected error occurred: {str(e)}"}

# =============================================================================
# EXACT REPLACEMENT CODE FOR simple_backend.py
# =============================================================================

REPLACEMENT_CODE = '''
                # FIXED: Direct LiveKit dispatch - NO HTTP REQUEST
                try:
                    # Use direct LiveKit dispatch instead of HTTP self-request
                    result = direct_livekit_call(call_data)
                    
                    if result.get("success") and result.get("status_code") == 200:
                        mode_text = "PERSONALIZED" if use_personalized_ai else "SHARED"
                        logger.info(f"✅ {mode_text} CALL INITIATED: {contact_name} ({contact_phone}) with agent {agent_name_for_call}")
                        
                        # Log to campaign statistics
                        try:
                            from campaign_statistics import CampaignStats
                            campaign_stats = CampaignStats(campaign_id, str(DATA_DIR))
                            call_id = campaign_stats.log_call_start(contact_phone, contact_name, contact_notes)
                            logger.info(f"📊 Campaign stats logged: {call_id}")
                        except Exception as stats_error:
                            logger.error(f"❌ Failed to log campaign stats: {stats_error}")
                        
                        # Log successful call to main logs
                        log_call({
                            'contact_name': contact_name,
                            'contact_phone': contact_phone,
                            'from_phone': selected_phone,
                            'agent_name': agent_data['name'],
                            'campaign_id': campaign_id,
                            'status': 'initiated',
                            'contact_notes': contact_notes,
                            'ai_mode': 'personalized' if use_personalized_ai else 'shared',
                            'agent_name_for_call': agent_name_for_call
                        })
                    else:
                        logger.error(f"❌ Call failed for {contact_name}: {result.get('status_code')} - {result.get('error')}")
                        
                        # Log to campaign statistics as failed
                        try:
                            from campaign_statistics import CampaignStats
                            campaign_stats = CampaignStats(campaign_id, str(DATA_DIR))
                            call_id = campaign_stats.log_call_start(contact_phone, contact_name, contact_notes)
                            campaign_stats.log_call_end(call_id, "failed", 0, 0.0, "call_initiation_failed")
                            logger.info(f"📊 Campaign stats logged (failed): {call_id}")
                        except Exception as stats_error:
                            logger.error(f"❌ Failed to log campaign stats: {stats_error}")
                        
                        # Log failed call to main logs
                        log_call({
                            'contact_name': contact_name,
                            'contact_phone': contact_phone,
                            'from_phone': selected_phone,
                            'agent_name': agent_data['name'],
                            'campaign_id': campaign_id,
                            'status': 'failed',
                            'contact_notes': contact_notes,
                            'ai_mode': 'personalized' if use_personalized_ai else 'shared'
                        })
                        
                except Exception as call_error:
                    logger.error(f"❌ LiveKit call error for {contact_name}: {str(call_error)}")
'''

print("🔧 CRITICAL PATCH FOR CALLING BOTTLENECK")
print("=" * 50)
print()
print("📍 STEP 1: Add this function to simple_backend.py (around line 820)")
print("Add the direct_livekit_call() function shown above")
print()
print("📍 STEP 2: Find and replace in make_single_call() function:")
print("FIND (around line 1862):")
print("    # Simulate the make_call request internally")
print("    import requests")
print("    response = requests.post('http://localhost:9090/make_call', json=call_data, timeout=10)")
print()
print("REPLACE WITH:")
print(REPLACEMENT_CODE)
print()
print("🎯 THIS WILL ELIMINATE THE HTTP BOTTLENECK!")
print("Your system should handle 20+ concurrent calls after this fix.")
print()
print("💡 ADDITIONAL OPTIMIZATIONS:")
print("1. Change max_concurrent_calls from unlimited to 8-10")
print("2. Add time.sleep(0.5) between call batches")
print("3. Use ThreadPoolExecutor instead of unlimited threading")

if __name__ == "__main__":
    print("\n🚀 Run this to test the fix:")
    print("cd Orp_tts_call_telnyx")
    print("python fix_calling_bottleneck.py") 
"""
CRITICAL PATCH FOR SIMPLE_BACKEND.PY CALLING BOTTLENECK

This script shows the EXACT code replacements needed to fix the main issue.

PROBLEM: Lines 1862-1864 in make_single_call() create HTTP self-requests that cause bottlenecks
SOLUTION: Replace with direct subprocess calls

INSTRUCTIONS:
1. Open Orp_tts_call_telnyx/simple_backend.py
2. Find lines around 1862-1864 that contain:
   ```
   import requests
   response = requests.post('http://localhost:9090/make_call', json=call_data, timeout=10)
   ```
3. Replace with the code shown below
"""

import subprocess
import json
import os
import logging

logger = logging.getLogger(__name__)

# =============================================================================
# REPLACE THIS CODE IN YOUR simple_backend.py
# =============================================================================

def REPLACE_THIS_IN_make_single_call():
    """
    FIND THIS CODE IN simple_backend.py around line 1862:
    
    ```python
    # Simulate the make_call request internally
    import requests
    response = requests.post('http://localhost:9090/make_call', json=call_data, timeout=10)
    
    if response.status_code == 200:
        mode_text = "PERSONALIZED" if use_personalized_ai else "SHARED"
        logger.info(f"✅ {mode_text} CALL INITIATED: {contact_name} ({contact_phone}) with agent {agent_name_for_call}")
    ```
    
    REPLACE WITH THE CODE BELOW:
    """
    pass

# =============================================================================
# NEW CODE TO REPLACE THE HTTP SELF-REQUEST
# =============================================================================

def direct_livekit_call(call_data):
    """
    Direct LiveKit dispatch - NO HTTP REQUEST
    Replace the requests.post() call with this function
    """
    try:
        # Extract data for LiveKit command
        agent_name_for_dispatch = call_data.get('agent_name_for_dispatch')
        phone_number = call_data.get('phone_number')
        from_phone = call_data.get('from_phone')
        sip_trunk_id = call_data.get('sip_trunk_id')
        campaign_id = call_data.get('campaign_id')
        call_id = call_data.get('call_id')
        contact_name = call_data.get('contact_name')
        
        # Build structured metadata for the agent
        if from_phone or sip_trunk_id != 'DEFAULT_SIP_TRUNK_ID':
            metadata = {
                "target_phone": phone_number,
                "from_phone": from_phone,
                "sip_trunk_id": sip_trunk_id,
                "campaign_id": campaign_id,
                "call_id": call_id,
                "contact_name": contact_name
            }
            metadata_str = json.dumps(metadata)
            logger.info(f"Using structured metadata: {metadata_str}")
        else:
            metadata_str = phone_number
            logger.info(f"Using simple metadata: {metadata_str}")

        # Build LiveKit dispatch command
        cmd = [
            "lk", "dispatch", "create",
            "--new-room",
            "--agent-name", agent_name_for_dispatch,
            "--metadata", metadata_str
        ]
        
        process_cwd = os.path.dirname(__file__)  # Directory of simple_backend.py

        logger.info(f"Executing command: {' '.join(cmd)} in CWD: {process_cwd}")
        result = subprocess.run(cmd, cwd=process_cwd, capture_output=True, text=True, check=False, timeout=20)

        if result.returncode == 0:
            logger.info(f"lk dispatch command successful. Output: {result.stdout}")
            return {"success": True, "status_code": 200, "output": result.stdout}
        else:
            logger.error(f"lk dispatch command failed. Return code: {result.returncode}. Error: {result.stderr}. Output: {result.stdout}")
            return {
                "success": False, 
                "status_code": 500,
                "error": result.stderr.strip(), 
                "output": result.stdout.strip(),
                "returncode": result.returncode
            }

    except FileNotFoundError:
        logger.error(f"lk command not found. Ensure LiveKit CLI is installed and in PATH.")
        return {"success": False, "status_code": 500, "error": "lk command not found"}
    except subprocess.TimeoutExpired:
        logger.error(f"lk dispatch command timed out after 20 seconds")
        return {"success": False, "status_code": 500, "error": "Command timed out"}
    except Exception as e:
        logger.error(f"Error executing lk dispatch command: {e}", exc_info=True)
        return {"success": False, "status_code": 500, "error": f"An unexpected error occurred: {str(e)}"}

# =============================================================================
# EXACT REPLACEMENT CODE FOR simple_backend.py
# =============================================================================

REPLACEMENT_CODE = '''
                # FIXED: Direct LiveKit dispatch - NO HTTP REQUEST
                try:
                    # Use direct LiveKit dispatch instead of HTTP self-request
                    result = direct_livekit_call(call_data)
                    
                    if result.get("success") and result.get("status_code") == 200:
                        mode_text = "PERSONALIZED" if use_personalized_ai else "SHARED"
                        logger.info(f"✅ {mode_text} CALL INITIATED: {contact_name} ({contact_phone}) with agent {agent_name_for_call}")
                        
                        # Log to campaign statistics
                        try:
                            from campaign_statistics import CampaignStats
                            campaign_stats = CampaignStats(campaign_id, str(DATA_DIR))
                            call_id = campaign_stats.log_call_start(contact_phone, contact_name, contact_notes)
                            logger.info(f"📊 Campaign stats logged: {call_id}")
                        except Exception as stats_error:
                            logger.error(f"❌ Failed to log campaign stats: {stats_error}")
                        
                        # Log successful call to main logs
                        log_call({
                            'contact_name': contact_name,
                            'contact_phone': contact_phone,
                            'from_phone': selected_phone,
                            'agent_name': agent_data['name'],
                            'campaign_id': campaign_id,
                            'status': 'initiated',
                            'contact_notes': contact_notes,
                            'ai_mode': 'personalized' if use_personalized_ai else 'shared',
                            'agent_name_for_call': agent_name_for_call
                        })
                    else:
                        logger.error(f"❌ Call failed for {contact_name}: {result.get('status_code')} - {result.get('error')}")
                        
                        # Log to campaign statistics as failed
                        try:
                            from campaign_statistics import CampaignStats
                            campaign_stats = CampaignStats(campaign_id, str(DATA_DIR))
                            call_id = campaign_stats.log_call_start(contact_phone, contact_name, contact_notes)
                            campaign_stats.log_call_end(call_id, "failed", 0, 0.0, "call_initiation_failed")
                            logger.info(f"📊 Campaign stats logged (failed): {call_id}")
                        except Exception as stats_error:
                            logger.error(f"❌ Failed to log campaign stats: {stats_error}")
                        
                        # Log failed call to main logs
                        log_call({
                            'contact_name': contact_name,
                            'contact_phone': contact_phone,
                            'from_phone': selected_phone,
                            'agent_name': agent_data['name'],
                            'campaign_id': campaign_id,
                            'status': 'failed',
                            'contact_notes': contact_notes,
                            'ai_mode': 'personalized' if use_personalized_ai else 'shared'
                        })
                        
                except Exception as call_error:
                    logger.error(f"❌ LiveKit call error for {contact_name}: {str(call_error)}")
'''

print("🔧 CRITICAL PATCH FOR CALLING BOTTLENECK")
print("=" * 50)
print()
print("📍 STEP 1: Add this function to simple_backend.py (around line 820)")
print("Add the direct_livekit_call() function shown above")
print()
print("📍 STEP 2: Find and replace in make_single_call() function:")
print("FIND (around line 1862):")
print("    # Simulate the make_call request internally")
print("    import requests")
print("    response = requests.post('http://localhost:9090/make_call', json=call_data, timeout=10)")
print()
print("REPLACE WITH:")
print(REPLACEMENT_CODE)
print()
print("🎯 THIS WILL ELIMINATE THE HTTP BOTTLENECK!")
print("Your system should handle 20+ concurrent calls after this fix.")
print()
print("💡 ADDITIONAL OPTIMIZATIONS:")
print("1. Change max_concurrent_calls from unlimited to 8-10")
print("2. Add time.sleep(0.5) between call batches")
print("3. Use ThreadPoolExecutor instead of unlimited threading")

if __name__ == "__main__":
    print("\n🚀 Run this to test the fix:")
    print("cd Orp_tts_call_telnyx")
    print("python fix_calling_bottleneck.py") 