#!/usr/bin/env python3
"""
Test the statistics endpoint that the frontend is calling
"""

import requests
import json

def test_statistics_endpoint():
    """Test the statistics endpoint"""
    
    campaign_id = "campaign_1749386900"  # dhf<PERSON>eyery campaign
    base_url = "http://localhost:9090"
    
    print(f"🔍 Testing statistics endpoint for campaign: {campaign_id}")
    print("=" * 50)
    
    try:
        # Test the main statistics endpoint
        stats_url = f"{base_url}/api/campaigns/{campaign_id}/statistics"
        print(f"📊 Calling: {stats_url}")
        
        response = requests.get(stats_url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Statistics data received:")
            print(json.dumps(data, indent=2))
            
            # Specifically check the call_statistics that frontend uses
            if 'call_statistics' in data:
                call_stats = data['call_statistics']
                print(f"\n📋 Call Statistics Summary:")
                print(f"  Total Calls: {call_stats.get('total_calls', 'N/A')}")
                print(f"  Completed: {call_stats.get('completed', 'N/A')}")
                print(f"  Failed: {call_stats.get('failed', 'N/A')}")
                print(f"  No Answer: {call_stats.get('no_answer', 'N/A')}")
        else:
            print(f"❌ Statistics endpoint failed: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_statistics_endpoint() 