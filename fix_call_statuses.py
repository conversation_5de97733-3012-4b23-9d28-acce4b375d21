#!/usr/bin/env python3
"""
Fix historical call statuses based on duration analysis
This will update calls that had webhook events showing they were answered but status was marked as no_answer
"""

import json
from datetime import datetime

def fix_call_statuses():
    # Load call logs
    with open('data/call_logs.json', 'r') as f:
        call_logs = json.load(f)
    
    fixed_count = 0
    
    for i, call in enumerate(call_logs):
        # Check if call has webhook events showing it was answered
        webhook_events = call.get('webhook_events', [])
        if not webhook_events:
            continue
            
        # Look for call.answered or call.hangup events
        call_answered = False
        call_duration = 0
        
        for event in webhook_events:
            if event.get('event_type') == 'call.answered':
                call_answered = True
            elif event.get('event_type') == 'call.hangup':
                payload = event.get('payload', {})
                start_time = payload.get('start_time', '')
                end_time = payload.get('end_time', '')
                
                # Calculate duration
                if start_time and end_time:
                    try:
                        start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                        end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                        call_duration = (end_dt - start_dt).total_seconds()
                    except:
                        call_duration = 0
        
        # Fix status based on analysis
        current_status = call.get('status', 'initiated')
        
        # If call was answered OR duration >= 8 seconds, mark as completed
        if (call_answered or call_duration >= 8) and current_status == 'no_answer':
            call_logs[i]['status'] = 'completed'
            call_logs[i]['call_duration'] = call_duration
            fixed_count += 1
            print(f"Fixed call for {call.get('contact_name', 'Unknown')} ({call.get('contact_phone')}) - Duration: {call_duration}s")
    
    # Save updated call logs
    with open('data/call_logs.json', 'w') as f:
        json.dump(call_logs, f, indent=2)
    
    print(f"\n✅ Fixed {fixed_count} call statuses")

if __name__ == '__main__':
    fix_call_statuses() 