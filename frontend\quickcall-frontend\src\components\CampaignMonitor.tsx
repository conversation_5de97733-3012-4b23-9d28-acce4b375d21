"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Bad<PERSON> } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { 
  Phone, CheckCircle, XCircle, Clock, Pause, Play, RefreshCw, Users, 
  BarChart3, AlertTriangle, DollarSign, Timer, Recycle, RotateCcw,
  TrendingUp, Target
} from "lucide-react"

interface CampaignMonitorProps {
  campaignId: string
  onClose?: () => void
}

interface MonitoringData {
  campaign_id: string
  name: string
  status: string
  started_at?: string
  completed_at?: string
  total_duration?: number
  contacts: {
    total: number
    called: number
    initiated: number
    answered: number
    no_answer: number
    failed: number
    remaining: number
  }
  answer_rate: number
  success_rate: number
  phone_numbers: string[]
  agent: {
    id: string
    name: string
  }
  recent_calls: Array<{
    id?: string
    contact_name: string
    contact_phone: string
    status: string
    timestamp: string
    duration?: number
    cost?: number
  }>
  errors: string[]
  // Enhanced fields
  enhanced?: boolean
  total_cost?: number
  total_duration_minutes?: number
  progress_percentage?: number
  button_states?: {
    [key: string]: {
      enabled: boolean
      tooltip: string
    }
  }
  recyclable_contacts?: {
    count: number
    contacts: Array<{
      name: string
      phone: string
      last_status: string
    }>
  }
  calls_by_status?: {
    [key: string]: number
  }
}

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:9090'

export function CampaignMonitor({ campaignId, onClose }: CampaignMonitorProps) {
  const [monitoringData, setMonitoringData] = useState<MonitoringData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null)

  const fetchMonitoringData = async () => {
    try {
      // Try enhanced statistics first
      const enhancedResponse = await fetch(`${API_URL}/api/campaigns/${campaignId}/statistics/enhanced`)
      if (enhancedResponse.ok) {
        const enhancedData = await enhancedResponse.json()
        
        // Convert enhanced data to monitoring data format
        if (enhancedData.success && enhancedData.data) {
          const data = enhancedData.data
          
          // Safely extract call status data
          const callsByStatus = data.call_analysis?.calls_by_status || {}
          const latestCalls = data.call_analysis?.latest_calls || []
          
          const converted = {
            campaign_id: campaignId,
            name: 'Campaign ' + campaignId,
            status: 'running', // We'll get this from campaigns list
            contacts: {
              total: data.progress?.total_contacts || 0,
              called: data.progress?.contacts_processed || 0,
              initiated: callsByStatus.in_progress || 0,
              answered: callsByStatus.answered || callsByStatus.completed || 0,
              no_answer: callsByStatus.no_answer || 0,
              failed: callsByStatus.failed || 0,
              remaining: data.summary?.calls_remaining || 0
            },
            answer_rate: data.summary?.success_rate || 0,
            success_rate: data.summary?.success_rate || 0,
            phone_numbers: [],
            agent: { id: 'unknown', name: 'AI Agent' },
            recent_calls: latestCalls,
            errors: [],
            // Enhanced fields
            enhanced: true,
            total_cost: data.summary?.total_cost || 0,
            total_duration_minutes: data.summary?.total_duration_minutes || 0,
            progress_percentage: data.progress?.progress_percentage || 0,
            button_states: data.button_states || {},
            recyclable_contacts: data.recyclable_contacts || { count: 0, contacts: [] },
            calls_by_status: callsByStatus // Add this for the enhanced UI
          }
          
          setMonitoringData(converted)
          setError(null)
          setLoading(false)
          return
        }
      }
      
      // Fallback to original monitor endpoint
      const response = await fetch(`${API_URL}/api/campaigns/${campaignId}/monitor`)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      const data = await response.json()
      setMonitoringData({ ...data, enhanced: false })
      setError(null)
    } catch (err) {
      console.error('Error fetching monitoring data:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  const pauseCampaign = async () => {
    try {
      const response = await fetch(`${API_URL}/api/campaigns/${campaignId}/pause`, {
        method: 'POST'
      })
      if (response.ok) {
        fetchMonitoringData() // Refresh data
      }
    } catch (err) {
      console.error('Error pausing campaign:', err)
    }
  }

  const resumeCampaign = async () => {
    try {
      const response = await fetch(`${API_URL}/api/campaigns/${campaignId}/resume`, {
        method: 'POST'
      })
      if (response.ok) {
        fetchMonitoringData() // Refresh data
      }
    } catch (err) {
      console.error('Error resuming campaign:', err)
    }
  }

  const performAction = async (action: string) => {
    try {
      const response = await fetch(`${API_URL}/api/campaigns/${campaignId}/${action}`, {
        method: 'POST'
      })
      if (response.ok) {
        fetchMonitoringData() // Refresh data
      }
    } catch (err) {
      console.error(`Error performing ${action}:`, err)
    }
  }

  useEffect(() => {
    fetchMonitoringData()
    
    // Set up auto-refresh every 10 seconds for running campaigns
    const interval = setInterval(() => {
      if (monitoringData?.status === 'running') {
        fetchMonitoringData()
      }
    }, 10000)
    
    setRefreshInterval(interval)
    
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [campaignId])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-green-500'
      case 'completed': return 'bg-blue-500'
      case 'paused': return 'bg-yellow-500'
      case 'failed': return 'bg-red-500'
      default: return 'bg-gray-500'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <Play className="h-4 w-4" />
      case 'completed': return <CheckCircle className="h-4 w-4" />
      case 'paused': return <Pause className="h-4 w-4" />
      case 'failed': return <XCircle className="h-4 w-4" />
      default: return <Clock className="h-4 w-4" />
    }
  }

  const formatDuration = (seconds?: number) => {
    if (!seconds) return 'N/A'
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}m ${secs}s`
  }

  const formatTime = (isoString?: string) => {
    if (!isoString) return 'N/A'
    return new Date(isoString).toLocaleTimeString()
  }

  const formatCurrency = (amount?: number) => {
    if (!amount) return '$0.00'
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 4
    }).format(amount)
  }

  if (loading) {
    return (
      <Card className="w-full max-w-4xl">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className="h-5 w-5 animate-spin" />
            Loading Campaign Monitor...
          </CardTitle>
        </CardHeader>
      </Card>
    )
  }

  if (error || !monitoringData) {
    return (
      <Card className="w-full max-w-4xl">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <XCircle className="h-5 w-5" />
            Error Loading Campaign
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-600 mb-4">{error || 'Campaign not found'}</p>
          <div className="bg-blue-50 border border-blue-200 rounded p-3 mb-4">
            <p className="text-blue-800 text-sm font-medium">💡 Troubleshooting Tips:</p>
            <ul className="text-blue-700 text-sm mt-2 space-y-1">
              <li>• Make sure the backend server is running on port 9090</li>
              <li>• Check if the campaign ID exists in the system</li>
              <li>• Verify the enhanced statistics endpoint is available</li>
              <li>• Try refreshing the page or restarting the servers</li>
            </ul>
          </div>
          <div className="flex gap-2">
            <Button onClick={fetchMonitoringData} variant="outline">
              <RefreshCw className="h-4 w-4 mr-1" />
              Retry
            </Button>
            {onClose && (
              <Button onClick={onClose} variant="outline">Close</Button>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  const progressPercentage = monitoringData.enhanced 
    ? monitoringData.progress_percentage || 0
    : (monitoringData.contacts.called / monitoringData.contacts.total) * 100

  return (
    <div className="w-full max-w-6xl space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Badge className={`${getStatusColor(monitoringData.status)} text-white`}>
                {getStatusIcon(monitoringData.status)}
                <span className="ml-1 capitalize">{monitoringData.status}</span>
              </Badge>
              <div>
                <CardTitle className="text-xl">{monitoringData.name}</CardTitle>
                <p className="text-sm text-gray-600">Campaign ID: {monitoringData.campaign_id}</p>
              </div>
            </div>
            <div className="flex gap-2">
              {/* Enhanced Action Buttons */}
              {monitoringData.enhanced && monitoringData.button_states && Object.keys(monitoringData.button_states).length > 0 ? (
                Object.entries(monitoringData.button_states).map(([action, state]) => {
                  const icons = {
                    start: <Play className="h-4 w-4" />,
                    pause: <Pause className="h-4 w-4" />,
                    restart: <RotateCcw className="h-4 w-4" />,
                    recycle: <Recycle className="h-4 w-4" />
                  }
                  
                  return (
                    <Button
                      key={action}
                      onClick={() => performAction(action)}
                      disabled={!state?.enabled}
                      variant={action === 'start' ? 'default' : 'outline'}
                      size="sm"
                      title={state?.tooltip || ''}
                    >
                      <span className="mr-1">{icons[action as keyof typeof icons]}</span>
                      {action.charAt(0).toUpperCase() + action.slice(1)}
                    </Button>
                  )
                })
              ) : (
                /* Fallback buttons for non-enhanced mode */
                <>
                  {monitoringData.status === 'running' && (
                    <Button onClick={pauseCampaign} variant="outline" size="sm">
                      <Pause className="h-4 w-4 mr-1" />
                      Pause
                    </Button>
                  )}
                  {monitoringData.status === 'paused' && (
                    <Button onClick={resumeCampaign} size="sm">
                      <Play className="h-4 w-4 mr-1" />
                      Resume
                    </Button>
                  )}
                </>
              )}
              
              <Button onClick={fetchMonitoringData} variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-1" />
                Refresh
              </Button>
              {onClose && (
                <Button onClick={onClose} variant="outline" size="sm">
                  Close
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Enhanced Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium">Progress</span>
            </div>
            <p className="text-2xl font-bold text-blue-700">{progressPercentage.toFixed(1)}%</p>
            <p className="text-sm text-gray-600">{monitoringData.contacts.called}/{monitoringData.contacts.total} contacts</p>
          </CardContent>
        </Card>

        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <Target className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium">Success Rate</span>
            </div>
            <p className="text-2xl font-bold text-green-700">{monitoringData.success_rate.toFixed(1)}%</p>
            <p className="text-sm text-gray-600">{monitoringData.contacts.answered} answered</p>
          </CardContent>
        </Card>

        {monitoringData.enhanced && (
          <Card className="border-purple-200 bg-purple-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-2">
                <DollarSign className="h-4 w-4 text-purple-600" />
                <span className="text-sm font-medium">Total Cost</span>
              </div>
              <p className="text-2xl font-bold text-purple-700">{formatCurrency(monitoringData.total_cost)}</p>
              <p className="text-sm text-gray-600">Live tracking</p>
            </CardContent>
          </Card>
        )}

        {monitoringData.enhanced && (
          <Card className="border-orange-200 bg-orange-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-2">
                <Timer className="h-4 w-4 text-orange-600" />
                <span className="text-sm font-medium">Duration</span>
              </div>
              <p className="text-2xl font-bold text-orange-700">{monitoringData.total_duration_minutes?.toFixed(1) || '0'}m</p>
              <p className="text-sm text-gray-600">Active time</p>
            </CardContent>
          </Card>
        )}

        <Card className="border-gray-200 bg-gray-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <Phone className="h-4 w-4 text-gray-600" />
              <span className="text-sm font-medium">Remaining</span>
            </div>
            <p className="text-2xl font-bold text-gray-700">{monitoringData.contacts.remaining}</p>
            <p className="text-sm text-gray-600">calls left</p>
          </CardContent>
        </Card>
      </div>

      {/* Progress and Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Campaign Progress
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <div className="flex justify-between text-sm mb-2">
              <span>Calls Made: {monitoringData.contacts.called}/{monitoringData.contacts.total}</span>
              <span>{Math.round(progressPercentage)}% Complete</span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
          </div>

          {/* Enhanced Call Status Breakdown */}
          {monitoringData.enhanced && monitoringData.calls_by_status && Object.keys(monitoringData.calls_by_status).length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-gray-700">Call Status Breakdown</h4>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2">
                {Object.entries(monitoringData.calls_by_status).map(([status, count]) => (
                  <div key={status} className="text-center p-2 bg-gray-50 rounded">
                    <p className="text-lg font-bold">{count || 0}</p>
                    <p className="text-xs text-gray-600 capitalize">{status.replace('_', ' ')}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Started:</span>
              <p className="font-medium">{formatTime(monitoringData.started_at)}</p>
            </div>
            <div>
              <span className="text-gray-600">Duration:</span>
              <p className="font-medium">{formatDuration(monitoringData.total_duration)}</p>
            </div>
            <div>
              <span className="text-gray-600">Agent:</span>
              <p className="font-medium">{monitoringData.agent.name}</p>
            </div>
            <div>
              <span className="text-gray-600">Phone Numbers:</span>
              <p className="font-medium">{monitoringData.phone_numbers.length}</p>
            </div>
            <div>
              <span className="text-gray-600">Remaining:</span>
              <p className="font-medium">{monitoringData.contacts.remaining}</p>
            </div>
            <div>
              <span className="text-gray-600">Failed:</span>
              <p className="font-medium text-red-600">{monitoringData.contacts.failed}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Detailed Tabs */}
      <Tabs defaultValue="calls" className="w-full">
        <TabsList className={`grid w-full ${monitoringData.enhanced && monitoringData.recyclable_contacts ? 'grid-cols-4' : 'grid-cols-3'}`}>
          <TabsTrigger value="calls">Recent Calls</TabsTrigger>
          {monitoringData.enhanced && monitoringData.recyclable_contacts && (
            <TabsTrigger value="recycle">
              <Recycle className="h-4 w-4 mr-1" />
              Recyclable ({monitoringData.recyclable_contacts?.count || 0})
            </TabsTrigger>
          )}
          <TabsTrigger value="errors">Issues</TabsTrigger>
          <TabsTrigger value="config">Configuration</TabsTrigger>
        </TabsList>

        <TabsContent value="calls" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Call Activity</CardTitle>
            </CardHeader>
            <CardContent>
              {!monitoringData.recent_calls || monitoringData.recent_calls.length === 0 ? (
                <p className="text-gray-600 text-center py-4">No recent calls</p>
              ) : (
                <div className="space-y-2">
                  {(monitoringData.recent_calls || []).map((call, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <Badge 
                          variant={call.status === 'answered' ? 'default' : call.status === 'initiated' ? 'secondary' : 'destructive'}
                          className="capitalize"
                        >
                          {call.status.replace('_', ' ')}
                        </Badge>
                        <div>
                          <p className="font-medium">{call.contact_name}</p>
                          <p className="text-sm text-gray-600">{call.contact_phone}</p>
                        </div>
                      </div>
                      <div className="text-right text-sm">
                        <p className="text-gray-600">{formatTime(call.timestamp)}</p>
                        {call.duration && <p className="text-xs text-gray-500">{call.duration}s</p>}
                        {call.cost && <p className="text-xs text-green-600">{formatCurrency(call.cost)}</p>}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Recyclable Contacts Tab */}
        {monitoringData.enhanced && monitoringData.recyclable_contacts && (
          <TabsContent value="recycle" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Recycle className="h-5 w-5 text-green-500" />
                  Recyclable Contacts ({monitoringData.recyclable_contacts?.count || 0})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {!monitoringData.recyclable_contacts || monitoringData.recyclable_contacts.count === 0 ? (
                  <div className="text-center py-4">
                    <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-2" />
                    <p className="text-green-600 font-medium">No failed calls to recycle</p>
                    <p className="text-sm text-gray-600">All contacts have been successfully processed</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <p className="text-sm text-gray-600 mb-4">
                      These contacts had failed or no-answer calls and can be retried:
                    </p>
                    <div className="space-y-2">
                      {(monitoringData.recyclable_contacts?.contacts || []).map((contact, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                          <div>
                            <p className="font-medium">{contact?.name || 'Unknown'}</p>
                            <p className="text-sm text-gray-600">{contact?.phone || ''}</p>
                          </div>
                          <Badge variant="outline" className="text-yellow-700 border-yellow-300">
                            {contact?.last_status || 'unknown'}
                          </Badge>
                        </div>
                      ))}
                    </div>
                    {monitoringData.button_states?.recycle?.enabled && (
                      <Button 
                        onClick={() => performAction('recycle')} 
                        className="w-full mt-4"
                      >
                        <Recycle className="h-4 w-4 mr-2" />
                        Recycle All Failed Contacts
                      </Button>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        )}

        <TabsContent value="errors" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-yellow-500" />
                Campaign Issues
              </CardTitle>
            </CardHeader>
            <CardContent>
              {!monitoringData.errors || monitoringData.errors.length === 0 ? (
                <div className="text-center py-4">
                  <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-2" />
                  <p className="text-green-600 font-medium">No issues detected</p>
                  <p className="text-sm text-gray-600">Campaign is running smoothly</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {(monitoringData.errors || []).map((error, index) => (
                    <div key={index} className="p-3 bg-red-50 border border-red-200 rounded-lg">
                      <p className="text-red-800 text-sm">{error || 'Unknown error'}</p>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="config" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Campaign Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">Phone Numbers</h4>
                  <div className="space-y-1">
                    {(monitoringData.phone_numbers || []).map((number, index) => (
                      <Badge key={index} variant="outline">{number || 'Unknown'}</Badge>
                    ))}
                    {(!monitoringData.phone_numbers || monitoringData.phone_numbers.length === 0) && (
                      <p className="text-sm text-gray-600">No phone numbers configured</p>
                    )}
                  </div>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Agent Details</h4>
                  <p className="text-sm">
                    <span className="text-gray-600">ID:</span> {monitoringData.agent.id}
                  </p>
                  <p className="text-sm">
                    <span className="text-gray-600">Name:</span> {monitoringData.agent.name}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 