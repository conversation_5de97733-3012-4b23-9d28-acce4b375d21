#!/usr/bin/env python3
"""
Test script for dynamic SIP trunk assignment

This script demonstrates how the new dynamic SIP trunk system works,
where each call process gets its own SIP trunk based on the phone number mapping.
"""

import json
import requests
import time
import sys
import os

# Configuration
BACKEND_URL = "http://localhost:9090"  # Backend.py runs on port 9090
SIMPLE_BACKEND_URL = "http://localhost:9090"  # Simple backend runs on port 9090

def test_sip_trunk_mapping():
    """Test the SIP trunk mapping system"""
    print("🧪 Testing Dynamic SIP Trunk Assignment System")
    print("=" * 50)
    
    # 1. Check current SIP trunk mapping
    print("\n1. Checking current SIP trunk mapping...")
    try:
        response = requests.get(f"{BACKEND_URL}/api/sip-trunks")
        if response.status_code == 200:
            mapping_data = response.json()
            print(f"✅ Current mapping: {json.dumps(mapping_data, indent=2)}")
        else:
            print(f"❌ Failed to get mapping: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error getting mapping: {e}")
        return False
    
    # 2. Test making a call with specific from_phone
    print("\n2. Testing call with specific from_phone...")
    
    # Example call data with from_phone specified
    call_data = {
        "phoneNumber": "+1234567890",  # Number to call TO
        "from_phone": "+19199256164",  # Telnyx number to call FROM (should use mapped SIP trunk)
        "agentPrompt": "You are a test agent calling to verify SIP trunk assignment.",
        "agentId": "test",
        "voiceId": "tara",
        "modelId": "llama-3.3-70b-versatile"
    }
    
    try:
        print(f"📞 Making test call: {json.dumps(call_data, indent=2)}")
        response = requests.post(f"{BACKEND_URL}/api/processes", json=call_data)
        
        if response.status_code == 200:
            result = response.json()
            process_id = result.get("processId")
            print(f"✅ Call initiated successfully! Process ID: {process_id}")
            
            # 3. Check process logs to verify SIP trunk assignment
            print("\n3. Checking process logs for SIP trunk assignment...")
            time.sleep(3)  # Wait for process to start
            
            log_response = requests.get(f"{BACKEND_URL}/api/processes/{process_id}")
            if log_response.status_code == 200:
                process_data = log_response.json()
                logs = process_data.get("logs", [])
                
                print("📋 Process logs:")
                for log in logs[-10:]:  # Show last 10 logs
                    print(f"  {log}")
                
                # Look for SIP trunk assignment in logs
                sip_trunk_logs = [log for log in logs if "SIP trunk" in log]
                if sip_trunk_logs:
                    print(f"\n✅ SIP trunk assignment found in logs:")
                    for log in sip_trunk_logs:
                        print(f"  {log}")
                else:
                    print(f"\n⚠️  No SIP trunk assignment logs found")
            else:
                print(f"❌ Failed to get process logs: {log_response.status_code}")
            
            return True
        else:
            print(f"❌ Failed to initiate call: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error making call: {e}")
        return False

def test_simple_backend_integration():
    """Test the simple_backend.py integration"""
    print("\n" + "=" * 50)
    print("🧪 Testing Simple Backend Integration")
    print("=" * 50)
    
    # Test making a call through simple_backend.py with from_phone
    call_data = {
        "phone_number": "+1234567890",
        "from_phone": "+19199256164",  # Should use mapped SIP trunk
        "agent_name_for_dispatch": "test-agent"
    }
    
    try:
        print(f"📞 Making call through simple_backend: {json.dumps(call_data, indent=2)}")
        response = requests.post(f"{SIMPLE_BACKEND_URL}/make_call", json=call_data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Simple backend call successful: {json.dumps(result, indent=2)}")
            return True
        else:
            print(f"❌ Simple backend call failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error with simple backend: {e}")
        return False

def show_current_mapping():
    """Show the current SIP trunk mapping"""
    print("\n📋 Current SIP Trunk Mapping:")
    print("-" * 30)
    
    # Check if mapping file exists
    mapping_file = os.path.join(os.path.dirname(__file__), 'data', 'sip_trunks.json')
    if os.path.exists(mapping_file):
        with open(mapping_file, 'r') as f:
            mapping = json.load(f)
        
        for phone, trunk in mapping.items():
            print(f"  {phone} → {trunk}")
    else:
        print("  No mapping file found")

def main():
    """Main test function"""
    print("🚀 Dynamic SIP Trunk Assignment Test Suite")
    print("This tests the new functionality where each call process")
    print("gets its own SIP trunk based on the from_phone parameter.")
    
    show_current_mapping()
    
    # Test backend.py
    success1 = test_sip_trunk_mapping()
    
    # Test simple_backend.py
    success2 = test_simple_backend_integration()
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)
    print(f"Backend.py test: {'✅ PASSED' if success1 else '❌ FAILED'}")
    print(f"Simple backend test: {'✅ PASSED' if success2 else '❌ FAILED'}")
    
    if success1 and success2:
        print("\n🎉 All tests passed! Dynamic SIP trunk assignment is working correctly.")
        print("\nKey benefits:")
        print("  • Each call process now uses its own SIP trunk")
        print("  • Calls are made from the correct Telnyx phone number")
        print("  • Multiple calls can use different phone numbers simultaneously")
        print("  • SIP trunk mapping is automatically loaded and applied")
    else:
        print("\n⚠️  Some tests failed. Check the logs above for details.")
        sys.exit(1)

if __name__ == "__main__":
    main() 