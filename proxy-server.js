const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 8085;

// Proxy backend API requests
app.use('/api', createProxyMiddleware({
  target: 'http://localhost:9090',
  changeOrigin: true,
  pathRewrite: {
    '^/api': '/api', // Keep the /api prefix
  },
  onError: (err, req, res) => {
    console.error('Backend proxy error:', err.message);
    res.status(502).json({ error: 'Backend not available' });
  }
}));

// Proxy frontend requests
app.use('/', createProxyMiddleware({
  target: 'http://localhost:3001',
  changeOrigin: true,
  ws: true, // Enable WebSocket proxying for Next.js hot reload
  onError: (err, req, res) => {
    console.error('Frontend proxy error:', err.message);
    res.status(502).send('Frontend not available');
  }
}));

app.listen(PORT, () => {
  console.log(`🚀 Proxy server running on port ${PORT}`);
  console.log(`📱 Frontend: http://localhost:${PORT}`);
  console.log(`🔗 Backend API: http://localhost:${PORT}/api`);
  console.log(`\n🌐 To share with friends:`);
  console.log(`   1. Run: cloudflared tunnel --url localhost:${PORT}`);
  console.log(`   2. Share the tunnel URL with your friend`);
}); 